<!DOCTYPE html>
<html>
<head>
    <title>Create Default Image</title>
</head>
<body>
    <canvas id="canvas" width="100" height="100" style="border: 1px solid #ccc;"></canvas>
    <br><br>
    <button onclick="downloadImage()">Download Default Image</button>
    
    <script>
        // Create a simple default item image
        const canvas = document.getElementById('canvas');
        const ctx = canvas.getContext('2d');
        
        // Background
        ctx.fillStyle = '#333333';
        ctx.fillRect(0, 0, 100, 100);
        
        // Border
        ctx.strokeStyle = '#d4af37';
        ctx.lineWidth = 2;
        ctx.strokeRect(2, 2, 96, 96);
        
        // Icon (simple box)
        ctx.fillStyle = '#666666';
        ctx.fillRect(25, 25, 50, 50);
        
        // Highlight
        ctx.fillStyle = '#d4af37';
        ctx.fillRect(30, 30, 40, 5);
        ctx.fillRect(30, 40, 25, 5);
        
        // Text
        ctx.fillStyle = '#d4af37';
        ctx.font = '10px Arial';
        ctx.textAlign = 'center';
        ctx.fillText('ITEM', 50, 90);
        
        function downloadImage() {
            const link = document.createElement('a');
            link.download = 'default.png';
            link.href = canvas.toDataURL();
            link.click();
        }
        
        // Auto-generate the image
        console.log('Default image created. Click download to save.');
    </script>
</body>
</html>
