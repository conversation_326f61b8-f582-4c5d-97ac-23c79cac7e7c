<?php
require_once "db.php";

echo "<h2>🔧 Fixing Category Icons</h2>";

// Fix the emoji icons with proper encoding
$updates = [
    "UPDATE shop_categories SET icon = 'sword' WHERE id = 2", // Items
    "UPDATE shop_categories SET icon = 'shirt' WHERE id = 3", // Skins  
    "UPDATE shop_categories SET icon = 'potion' WHERE id = 4"  // Consumables
];

echo "<h3>Updating icons to text-based versions:</h3>";
foreach ($updates as $sql) {
    if ($conn->query($sql)) {
        echo "✅ Icon updated successfully<br>";
    } else {
        echo "❌ Error: " . $conn->error . "<br>";
    }
}

// Show updated categories
echo "<h3>Updated categories:</h3>";
$result = $conn->query("SELECT * FROM shop_categories ORDER BY display_order");
if ($result && $result->num_rows > 0) {
    echo "<table border='1' style='border-collapse: collapse;'>";
    echo "<tr><th>ID</th><th>Name</th><th>Slug</th><th>Icon</th><th>Display Order</th><th>Active</th></tr>";
    while ($row = $result->fetch_assoc()) {
        echo "<tr>";
        echo "<td>" . $row['id'] . "</td>";
        echo "<td>" . $row['name'] . "</td>";
        echo "<td>" . $row['slug'] . "</td>";
        echo "<td>" . $row['icon'] . "</td>";
        echo "<td>" . $row['display_order'] . "</td>";
        echo "<td>" . ($row['is_active'] ? 'Yes' : 'No') . "</td>";
        echo "</tr>";
    }
    echo "</table>";
}

echo "<br><strong>✅ Icons fixed! Now test your shop management and player shop.</strong>";

$conn->close();
?>
