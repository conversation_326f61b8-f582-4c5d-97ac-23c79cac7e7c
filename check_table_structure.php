<?php
echo "Checking table structures...\n";

require_once "db.php";

if ($conn->connect_error) {
    die("Connection failed: " . $conn->connect_error);
}

echo "✓ Database connection successful!\n\n";

// Check inventory table structure
echo "=== INVENTORY TABLE STRUCTURE ===\n";
$result = $conn->query("DESCRIBE inventory");
if ($result) {
    while ($row = $result->fetch_assoc()) {
        echo "- {$row['Field']}: {$row['Type']} " . ($row['Null'] == 'YES' ? '(NULL)' : '(NOT NULL)') . "\n";
    }
} else {
    echo "Error: " . $conn->error . "\n";
}

echo "\n=== MAIL TABLE STRUCTURE ===\n";
$result = $conn->query("DESCRIBE mail");
if ($result) {
    while ($row = $result->fetch_assoc()) {
        echo "- {$row['Field']}: {$row['Type']} " . ($row['Null'] == 'YES' ? '(NULL)' : '(NOT NULL)') . "\n";
    }
} else {
    echo "Error: " . $conn->error . "\n";
}

// Test the problematic queries
echo "\n=== TESTING PROBLEMATIC QUERIES ===\n";

// Test inventory insert query
$testInventoryQuery = "INSERT INTO inventory (item_unique_id, item_id, item_count, item_color, item_creator, item_expire_time, item_activation_count, item_owner, item_location, enchant, item_skin, fusioned_item, optional_socket, charge_points) VALUES (?, ?, ?, 0, '', NULL, 0, ?, 127, 0, 0, 0, 0, 0)";

echo "Testing inventory query preparation...\n";
$stmt = $conn->prepare($testInventoryQuery);
if ($stmt) {
    echo "✓ Inventory query prepared successfully\n";
} else {
    echo "✗ Inventory query failed: " . $conn->error . "\n";
}

// Test mail insert query
$testMailQuery = "INSERT INTO mail (mail_unique_id, mail_recipient_id, sender_name, mail_title, mail_message, unread, attached_item_id, attached_kinah, express, mail_type) VALUES (?, ?, 'Donation Shop', 'Item Purchase', ?, 1, ?, 0, 0, 1)";

echo "Testing mail query preparation...\n";
$stmt = $conn->prepare($testMailQuery);
if ($stmt) {
    echo "✓ Mail query prepared successfully\n";
} else {
    echo "✗ Mail query failed: " . $conn->error . "\n";
}

// Let's try a simpler approach - check what columns actually exist
echo "\n=== SIMPLIFIED INVENTORY INSERT TEST ===\n";
$simpleInventoryQuery = "INSERT INTO inventory (item_unique_id, item_id, item_count, item_owner, item_location) VALUES (999, 164000073, 5, 1217, 127)";
echo "Testing simple inventory insert...\n";
if ($conn->query($simpleInventoryQuery)) {
    echo "✓ Simple inventory insert works\n";
    // Clean up test data
    $conn->query("DELETE FROM inventory WHERE item_unique_id = 999");
} else {
    echo "✗ Simple inventory insert failed: " . $conn->error . "\n";
}

echo "\n=== SIMPLIFIED MAIL INSERT TEST ===\n";
$simpleMailQuery = "INSERT INTO mail (mail_unique_id, mail_recipient_id, sender_name, mail_title, mail_message, unread, attached_item_id) VALUES (999, 1217, 'Test', 'Test Mail', 'Test message', 1, 999)";
echo "Testing simple mail insert...\n";
if ($conn->query($simpleMailQuery)) {
    echo "✓ Simple mail insert works\n";
    // Clean up test data
    $conn->query("DELETE FROM mail WHERE mail_unique_id = 999");
} else {
    echo "✗ Simple mail insert failed: " . $conn->error . "\n";
}

$conn->close();
?>
