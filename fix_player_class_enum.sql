-- Fix player_class enum to support all Aion classes including newer ones
-- This fixes the "Data truncated for column 'player_class'" error

-- Replace 'your_database_name' with your actual database name
-- USE your_database_name;

-- Update the player_class enum to include all classes
ALTER TABLE `players` 
MODIFY COLUMN `player_class` enum(
    '<PERSON><PERSON><PERSON><PERSON>',
    '<PERSON><PERSON><PERSON><PERSON><PERSON>',
    '<PERSON><PERSON><PERSON><PERSON>',
    'SCOUT',
    '<PERSON>SA<PERSON><PERSON>',
    'RANGER',
    'MA<PERSON>',
    'SORCERER',
    'SPIRIT_MASTER',
    'PRIEST',
    'CLERIC',
    'CHANTER',
    'ENGINEER',
    'GUNNER',
    'ARTIST',
    'BARD',
    'RIDER',
    'ALL'
) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL;

-- Verify the change
DESCRIBE players;
