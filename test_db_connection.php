<?php
echo "Testing database connection...\n";

// Test different password combinations
$servername = "localhost";
$username = "root";
$dbname = "not-aion";

// Common XAMPP default passwords
$passwords = [
    '', // Empty password (XAMPP default)
    'Azer$tyuio^p789', // Current password in db.php
    'root',
    'password'
];

foreach ($passwords as $password) {
    echo "\nTrying password: " . ($password === '' ? '(empty)' : $password) . "\n";
    
    try {
        $conn = new mysqli($servername, $username, $password, $dbname);
        
        if ($conn->connect_error) {
            echo "✗ Failed: " . $conn->connect_error . "\n";
        } else {
            echo "✓ SUCCESS! Connected to database: $dbname\n";
            
            // Test if shop_items table exists
            $result = $conn->query("SHOW TABLES LIKE 'shop_items'");
            if ($result && $result->num_rows > 0) {
                echo "✓ shop_items table exists\n";
                
                // Count items
                $count_result = $conn->query("SELECT COUNT(*) as count FROM shop_items");
                if ($count_result) {
                    $count = $count_result->fetch_assoc()['count'];
                    echo "Items in shop: $count\n";
                } else {
                    echo "✗ Error counting items: " . $conn->error . "\n";
                }
            } else {
                echo "✗ shop_items table missing\n";
            }
            
            $conn->close();
            echo "\n=== USE THIS PASSWORD IN db.php ===\n";
            break;
        }
    } catch (Exception $e) {
        echo "✗ Exception: " . $e->getMessage() . "\n";
    }
}
?>
