<?php
require_once "db.php";

// Create database connection
$conn = new mysqli($servername, $username, $password, $dbname);
if ($conn->connect_error) {
    die("Connection failed: " . $conn->connect_error);
}

// Function to calculate level from experience
function calculateLevel($exp) {
    if ($exp < 1000) return 1;
    if ($exp < 5000) return 10;
    if ($exp < 50000) return 20;
    if ($exp < 200000) return 30;
    if ($exp < 500000) return 40;
    if ($exp < 1000000) return 50;
    if ($exp < 2000000) return 60;
    return 65;
}

// Function to time ago
function timeAgo($datetime) {
    $time = time() - strtotime($datetime);
    if ($time < 60) return 'just now';
    if ($time < 3600) return floor($time/60) . ' minutes ago';
    if ($time < 86400) return floor($time/3600) . ' hours ago';
    if ($time < 2592000) return floor($time/86400) . ' days ago';
    return date('M j, Y', strtotime($datetime));
}

// Get recent player registrations
$recentPlayersQuery = "
    SELECT 
        p.name,
        p.player_class,
        p.race,
        p.creation_date,
        p.exp
    FROM players p
    ORDER BY p.creation_date DESC
    LIMIT 10
";

$recentPlayersResult = $conn->query($recentPlayersQuery);

// Get recent player creations (instead of account registrations)
$recentAccountsQuery = "
    SELECT
        p.name,
        p.creation_date
    FROM players p
    ORDER BY p.creation_date DESC
    LIMIT 10
";

$recentAccountsResult = $conn->query($recentAccountsQuery);

// Get players with recent activity (based on last_online)
$recentActivityQuery = "
    SELECT 
        p.name,
        p.player_class,
        p.race,
        p.last_online,
        p.exp,
        ar.gp,
        ar.ap,
        ar.all_kill
    FROM players p
    LEFT JOIN abyss_rank ar ON p.id = ar.player_id
    WHERE p.last_online IS NOT NULL
    ORDER BY p.last_online DESC
    LIMIT 15
";

$recentActivityResult = $conn->query($recentActivityQuery);

// Get top performers (recent high achievers)
$topPerformersQuery = "
    SELECT 
        p.name,
        p.player_class,
        p.race,
        p.exp,
        ar.gp,
        ar.ap,
        ar.all_kill,
        ar.daily_ap,
        ar.daily_gp,
        ar.daily_kill
    FROM players p
    LEFT JOIN abyss_rank ar ON p.id = ar.player_id
    WHERE ar.player_id IS NOT NULL
    ORDER BY (ar.daily_ap + ar.daily_gp + ar.daily_kill) DESC
    LIMIT 10
";

$topPerformersResult = $conn->query($topPerformersQuery);

// Get legion information
$legionStatsQuery = "
    SELECT 
        l.name as legion_name,
        COUNT(lm.player_id) as member_count,
        AVG(ar.gp) as avg_gp,
        AVG(ar.ap) as avg_ap,
        SUM(ar.all_kill) as total_kills
    FROM legions l
    LEFT JOIN legion_members lm ON l.id = lm.legion_id
    LEFT JOIN abyss_rank ar ON lm.player_id = ar.player_id
    GROUP BY l.id, l.name
    HAVING member_count > 0
    ORDER BY avg_gp DESC
    LIMIT 5
";

$legionStatsResult = $conn->query($legionStatsQuery);

$conn->close();
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Server Activity - Aion-Blitz Server</title>
    <link rel="stylesheet" href="styles.css">
    <link href="https://fonts.googleapis.com/css2?family=Cinzel:wght@400;600;700&family=Open+Sans:wght@300;400;600&display=swap" rel="stylesheet">
</head>
<body>
    <!-- Video Background -->
    <div class="video-background">
        <video autoplay muted loop playsinline>
            <source src="images/bg_main.mp4" type="video/mp4">
        </video>
    </div>
    <div class="video-overlay"></div>

    <!-- Top Navigation -->
    <nav class="top-nav">
        <div class="nav-left">
            <span class="server-time">🕐 Server Time: 00:00</span>
            <span class="server-status">🟢 Server Status</span>
        </div>
        <div class="nav-center">
            <ul class="nav-menu">
                <li><a href="index.php">🏠 Home</a></li>
                <li><a href="index.php#faq">❓ F.A.Q</a></li>
                <li><a href="register.php">📝 Register</a></li>
                <li><a href="index.php#schedule">📅 Schedule</a></li>
                <li><a href="ranks.php">🏆 Ranks</a></li>
                <li><a href="activity.php" class="active">📊 Activity</a></li>
                <li><a href="pvp.php">⚔️ PvP</a></li>
                <?php if (isset($_SESSION["username"])): ?>
                <li><a href="dashboard.php">👤 Dashboard</a></li>
                <?php endif; ?>
                <li><a href="index.php#discord">💬 Discord</a></li>
            </ul>
        </div>
        <div class="nav-right">
            <div class="user-section">
                <?php
                session_start();
                if (isset($_SESSION["username"])) {
                    echo "<span class='welcome-user'>Welcome, " . $_SESSION["username"] . "!</span>";
                    echo "<a href='logout.php' class='logout-btn'>Logout</a>";
                } else {
                    echo "<a href='signin.php' class='signin-btn'>Sign In</a>";
                }
                ?>
            </div>
        </div>
    </nav>

    <!-- Activity Section -->
    <section class="activity-section">
        <div class="activity-container">
            <div class="activity-header">
                <h1>📊 Server Activity</h1>
                <p class="activity-subtitle">Live updates from Aion-Blitz Server</p>
            </div>

            <div class="activity-grid">
                <!-- Recent Player Creations -->
                <div class="activity-card">
                    <div class="card-header">
                        <h3>👥 New Players</h3>
                        <span class="card-badge">Latest</span>
                    </div>
                    <div class="activity-list">
                        <?php
                        if ($recentAccountsResult && $recentAccountsResult->num_rows > 0) {
                            while ($player = $recentAccountsResult->fetch_assoc()) {
                                echo "<div class='activity-item'>";
                                echo "<div class='activity-icon'>🆕</div>";
                                echo "<div class='activity-content'>";
                                echo "<div class='activity-title'>" . htmlspecialchars($player['name']) . "</div>";
                                echo "<div class='activity-time'>" . timeAgo($player['creation_date']) . "</div>";
                                echo "</div>";
                                echo "</div>";
                            }
                        } else {
                            echo "<div class='no-activity'>No recent player creations</div>";
                        }
                        ?>
                    </div>
                </div>

                <!-- Recent Player Activity -->
                <div class="activity-card">
                    <div class="card-header">
                        <h3>🎮 Recent Players</h3>
                        <span class="card-badge">Active</span>
                    </div>
                    <div class="activity-list">
                        <?php
                        if ($recentActivityResult && $recentActivityResult->num_rows > 0) {
                            while ($player = $recentActivityResult->fetch_assoc()) {
                                $level = calculateLevel($player['exp']);
                                echo "<div class='activity-item'>";
                                echo "<div class='activity-icon'>⚡</div>";
                                echo "<div class='activity-content'>";
                                echo "<div class='activity-title'>";
                                echo "<a href='player.php?name=" . urlencode($player['name']) . "'>" . htmlspecialchars($player['name']) . "</a>";
                                echo " <span class='level-badge'>Lv.$level</span>";
                                echo "</div>";
                                echo "<div class='activity-meta'>" . $player['player_class'] . " • " . $player['race'] . "</div>";
                                echo "<div class='activity-time'>" . timeAgo($player['last_online']) . "</div>";
                                echo "</div>";
                                echo "</div>";
                            }
                        } else {
                            echo "<div class='no-activity'>No recent activity</div>";
                        }
                        ?>
                    </div>
                </div>

                <!-- Top Daily Performers -->
                <div class="activity-card">
                    <div class="card-header">
                        <h3>🏆 Daily Champions</h3>
                        <span class="card-badge">Today</span>
                    </div>
                    <div class="activity-list">
                        <?php
                        if ($topPerformersResult && $topPerformersResult->num_rows > 0) {
                            $position = 1;
                            while ($performer = $topPerformersResult->fetch_assoc()) {
                                $level = calculateLevel($performer['exp']);
                                $dailyScore = $performer['daily_ap'] + $performer['daily_gp'] + $performer['daily_kill'];
                                
                                if ($dailyScore > 0) {
                                    echo "<div class='activity-item'>";
                                    echo "<div class='activity-icon'>";
                                    if ($position <= 3) {
                                        $medals = ['🥇', '🥈', '🥉'];
                                        echo $medals[$position - 1];
                                    } else {
                                        echo "🏅";
                                    }
                                    echo "</div>";
                                    echo "<div class='activity-content'>";
                                    echo "<div class='activity-title'>";
                                    echo "<a href='player.php?name=" . urlencode($performer['name']) . "'>" . htmlspecialchars($performer['name']) . "</a>";
                                    echo " <span class='level-badge'>Lv.$level</span>";
                                    echo "</div>";
                                    echo "<div class='activity-meta'>Daily Score: " . number_format($dailyScore) . "</div>";
                                    echo "<div class='activity-time'>" . $performer['player_class'] . "</div>";
                                    echo "</div>";
                                    echo "</div>";
                                    $position++;
                                }
                            }
                        } else {
                            echo "<div class='no-activity'>No daily activity yet</div>";
                        }
                        ?>
                    </div>
                </div>

                <!-- Legion Statistics -->
                <div class="activity-card">
                    <div class="card-header">
                        <h3>🏰 Top Legions</h3>
                        <span class="card-badge">Active</span>
                    </div>
                    <div class="activity-list">
                        <?php
                        if ($legionStatsResult && $legionStatsResult->num_rows > 0) {
                            while ($legion = $legionStatsResult->fetch_assoc()) {
                                echo "<div class='activity-item'>";
                                echo "<div class='activity-icon'>🛡️</div>";
                                echo "<div class='activity-content'>";
                                echo "<div class='activity-title'>" . htmlspecialchars($legion['legion_name']) . "</div>";
                                echo "<div class='activity-meta'>" . $legion['member_count'] . " members</div>";
                                echo "<div class='activity-time'>Avg GP: " . number_format($legion['avg_gp']) . "</div>";
                                echo "</div>";
                                echo "</div>";
                            }
                        } else {
                            echo "<div class='no-activity'>No legion data available</div>";
                        }
                        ?>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <script src="script.js"></script>
</body>
</html>
