<?php
echo "Checking all databases and mail tables...\n";

require_once "db.php";

if ($conn->connect_error) {
    die("Connection failed: " . $conn->connect_error);
}

echo "✓ Database connection successful!\n\n";

// Check all databases
echo "=== ALL DATABASES ===\n";
$result = $conn->query("SHOW DATABASES");
$databases = [];
if ($result) {
    while ($row = $result->fetch_assoc()) {
        $databases[] = $row['Database'];
        echo "- {$row['Database']}\n";
    }
} else {
    echo "Error: " . $conn->error . "\n";
}

// Check for mail tables in all databases
echo "\n=== MAIL TABLES IN ALL DATABASES ===\n";
$mailTables = [];
foreach ($databases as $db) {
    if (in_array($db, ['information_schema', 'performance_schema', 'mysql', 'sys'])) {
        continue; // Skip system databases
    }
    
    $conn->select_db($db);
    $result = $conn->query("SHOW TABLES LIKE 'mail'");
    if ($result && $result->num_rows > 0) {
        $mailTables[] = $db;
        echo "✓ Database '$db' has mail table\n";
        
        // Check mail table structure
        $structResult = $conn->query("DESCRIBE mail");
        if ($structResult) {
            $columns = [];
            while ($col = $structResult->fetch_assoc()) {
                $columns[] = $col['Field'];
            }
            echo "  Columns: " . implode(', ', $columns) . "\n";
            
            // Check specifically for temperance
            if (in_array('temperance', $columns)) {
                echo "  ✓ Has temperance column\n";
            } else {
                echo "  ✗ Missing temperance column\n";
            }
        }
        echo "\n";
    }
}

if (empty($mailTables)) {
    echo "✗ No mail tables found in any database!\n";
}

// Check current database setting
echo "=== CURRENT DATABASE CONNECTION ===\n";
$result = $conn->query("SELECT DATABASE() as current_db");
if ($result) {
    $current = $result->fetch_assoc();
    echo "Currently connected to: {$current['current_db']}\n";
}

// Check not-aion database specifically
echo "\n=== CHECKING 'not-aion' DATABASE ===\n";
$conn->select_db('not-aion');
$result = $conn->query("SELECT DATABASE() as current_db");
if ($result) {
    $current = $result->fetch_assoc();
    echo "Connected to: {$current['current_db']}\n";
    
    // Check mail table structure in not-aion
    $result = $conn->query("DESCRIBE mail");
    if ($result) {
        echo "Mail table structure in 'not-aion':\n";
        while ($row = $result->fetch_assoc()) {
            echo "  - {$row['Field']}: {$row['Type']}\n";
        }
        
        // Test temperance column specifically
        $testResult = $conn->query("SELECT temperance FROM mail LIMIT 1");
        if ($testResult) {
            echo "✓ temperance column is accessible\n";
        } else {
            echo "✗ temperance column error: " . $conn->error . "\n";
        }
    } else {
        echo "✗ Cannot describe mail table: " . $conn->error . "\n";
    }
} else {
    echo "✗ Cannot connect to 'not-aion' database\n";
}

// Check if there might be other Aion-related databases
echo "\n=== LOOKING FOR OTHER AION DATABASES ===\n";
foreach ($databases as $db) {
    if (stripos($db, 'aion') !== false || stripos($db, 'game') !== false) {
        echo "Found potential game database: $db\n";
        
        $conn->select_db($db);
        $result = $conn->query("SHOW TABLES LIKE 'mail'");
        if ($result && $result->num_rows > 0) {
            echo "  ✓ Has mail table\n";
            
            // Check structure
            $structResult = $conn->query("DESCRIBE mail");
            if ($structResult) {
                $columns = [];
                while ($col = $structResult->fetch_assoc()) {
                    $columns[] = $col['Field'];
                }
                if (in_array('temperance', $columns)) {
                    echo "  ✓ Has temperance column\n";
                } else {
                    echo "  ✗ Missing temperance column - THIS MIGHT BE THE ISSUE!\n";
                }
            }
        }
    }
}

$conn->close();
?>
