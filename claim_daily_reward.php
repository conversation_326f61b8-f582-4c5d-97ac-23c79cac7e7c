<?php
session_start();
require_once "db.php";

// Check if user is logged in
if (!isset($_SESSION["username"])) {
    http_response_code(401);
    echo json_encode(['success' => false, 'message' => 'Not logged in']);
    exit;
}

// Create database connection
$conn = new mysqli($servername, $username, $password, $dbname);
if ($conn->connect_error) {
    http_response_code(500);
    echo json_encode(['success' => false, 'message' => 'Database connection failed']);
    exit;
}

$accountName = $_SESSION["username"];

// Get account ID
$accountQuery = "SELECT id FROM account_data WHERE name = ?";
$stmt = $conn->prepare($accountQuery);
$stmt->bind_param("s", $accountName);
$stmt->execute();
$accountResult = $stmt->get_result();
$account = $accountResult->fetch_assoc();

if (!$account) {
    http_response_code(404);
    echo json_encode(['success' => false, 'message' => 'Account not found']);
    exit;
}

$userId = $account['id'];

// Check current stamps
$stampsQuery = "SELECT stamps, last_stamp FROM account_stamps WHERE account_id = ?";
$stmt = $conn->prepare($stampsQuery);
$stmt->bind_param("i", $userId);
$stmt->execute();
$stampsResult = $stmt->get_result();
$stamps = $stampsResult->fetch_assoc();

$currentStamps = $stamps['stamps'] ?? 0;
$lastStamp = $stamps['last_stamp'] ?? null;

// Check if can claim (24 hours since last claim)
$canClaim = !$lastStamp || (time() - strtotime($lastStamp)) >= 86400;

if (!$canClaim) {
    echo json_encode(['success' => false, 'message' => 'Daily reward already claimed today']);
    exit;
}

// Update stamps
$newStamps = $currentStamps + 1;

if ($stamps) {
    // Update existing record
    $updateQuery = "UPDATE account_stamps SET stamps = ?, last_stamp = NOW() WHERE account_id = ?";
    $stmt = $conn->prepare($updateQuery);
    $stmt->bind_param("ii", $newStamps, $userId);
} else {
    // Insert new record
    $updateQuery = "INSERT INTO account_stamps (account_id, stamps, last_stamp) VALUES (?, ?, NOW())";
    $stmt = $conn->prepare($updateQuery);
    $stmt->bind_param("ii", $userId, $newStamps);
}

if ($stmt->execute()) {
    // Get player characters for rewards
    $playersQuery = "SELECT id, name FROM players WHERE account_id = ? LIMIT 1";
    $stmt = $conn->prepare($playersQuery);
    $stmt->bind_param("i", $userId);
    $stmt->execute();
    $playersResult = $stmt->get_result();
    
    if ($playersResult->num_rows > 0) {
        $player = $playersResult->fetch_assoc();
        $playerId = $player['id'];
        
        // Determine daily reward based on day
        $dayRewards = [
            1 => ['item_id' => *********, 'count' => 5], // Day 1: Test Item
            2 => ['item_id' => *********, 'count' => 5], // Day 2: Test Item
            3 => ['item_id' => *********, 'count' => 5], // Day 3: Test Item
            4 => ['item_id' => *********, 'count' => 5], // Day 4: Test Item
            5 => ['item_id' => *********, 'count' => 5], // Day 5: Test Item
            6 => ['item_id' => *********, 'count' => 5], // Day 6: Test Item
            7 => ['item_id' => *********, 'count' => 5], // Day 7: Test Item
        ];
        
        $dayOfWeek = (($newStamps - 1) % 7) + 1;
        $reward = $dayRewards[$dayOfWeek];
        
        // Special rewards for milestones
        if ($newStamps % 7 == 0) {
            // Weekly bonus
            $reward = ['item_id' => *********, 'count' => 5];
        }
        if ($newStamps % 30 == 0) {
            // Monthly bonus
            $reward = ['item_id' => *********, 'count' => 5];
        }
        
        // Bypass the problematic trigger - add items directly to inventory and mail

        // Get next unique IDs
        $itemUniqueIdQuery = "SELECT MAX(item_unique_id) + 1 as next_id FROM inventory";
        $result = $conn->query($itemUniqueIdQuery);
        $newItemUniqueId = $result->fetch_assoc()['next_id'] ?? 1;

        $mailUniqueIdQuery = "SELECT MAX(mail_unique_id) + 1 as next_id FROM mail";
        $result = $conn->query($mailUniqueIdQuery);
        $newMailId = $result->fetch_assoc()['next_id'] ?? 1;

        // Start transaction
        $conn->begin_transaction();

        try {
            // Insert item directly into inventory (location 127 = mail attachment)
            $inventoryQuery = "INSERT INTO inventory (item_unique_id, item_id, item_count, item_owner, item_location) VALUES (?, ?, ?, ?, 127)";
            $stmt = $conn->prepare($inventoryQuery);
            $stmt->bind_param("iiii", $newItemUniqueId, $reward['item_id'], $reward['count'], $playerId);
            $stmt->execute();

            // Insert mail with item attached
            $mailQuery = "INSERT INTO mail (mail_unique_id, mail_recipient_id, sender_name, mail_title, mail_message, attached_item_id, attached_kinah_count, unread, express) VALUES (?, ?, 'Daily Rewards', 'Daily Login Reward', 'Thank you for playing Aion-Blitz! Here is your daily login reward.', ?, 0, 1, 1)";
            $stmt = $conn->prepare($mailQuery);
            $stmt->bind_param("iii", $newMailId, $playerId, $newItemUniqueId);
            $stmt->execute();

            // Commit transaction
            $conn->commit();

            echo json_encode([
                'success' => true,
                'message' => 'Daily reward claimed successfully! Check your in-game mail.',
                'stamps' => $newStamps,
                'reward' => $reward
            ]);

        } catch (Exception $e) {
            // Rollback transaction on error
            $conn->rollback();
            error_log("Daily reward error: " . $e->getMessage());
            echo json_encode([
                'success' => false,
                'message' => 'Failed to add reward to inventory. Error: ' . $e->getMessage()
            ]);
        }
    } else {
        echo json_encode(['success' => false, 'message' => 'No characters found. Create a character first!']);
    }
} else {
    echo json_encode(['success' => false, 'message' => 'Failed to update stamps']);
}

$conn->close();
?>
