<?php
// Test character selection for shop purchases
session_start();
require_once "db.php";

// Simulate being logged in as lahart77
$_SESSION["username"] = "lahart77";

echo "Testing Character Selection System\n";
echo "=================================\n";

// Get account info
$accountQuery = "SELECT id, name FROM account_data WHERE name = ?";
$stmt = $conn->prepare($accountQuery);
$stmt->bind_param("s", $_SESSION["username"]);
$stmt->execute();
$accountResult = $stmt->get_result();
$account = $accountResult->fetch_assoc();

if (!$account) {
    echo "✗ Account not found\n";
    exit;
}

$userId = $account['id'];
echo "Account: {$account['name']} (ID: $userId)\n\n";

// Get all characters for this account
$charactersQuery = "SELECT id, name, player_class, exp FROM players WHERE account_id = ? ORDER BY last_online DESC";
$stmt = $conn->prepare($charactersQuery);
$stmt->bind_param("i", $userId);
$stmt->execute();
$charactersResult = $stmt->get_result();

echo "Characters on this account:\n";
if ($charactersResult->num_rows > 0) {
    while ($char = $charactersResult->fetch_assoc()) {
        echo "- ID: {$char['id']}, Name: {$char['name']}, Class: {$char['player_class']}, Level: " . floor($char['exp'] / 1000) . "\n";
    }
} else {
    echo "- No characters found\n";
}

echo "\n";

// Test character validation
$testCharacterId = 106621; // Replace with actual character ID from above
echo "Testing character validation for ID: $testCharacterId\n";

$validateQuery = "SELECT id, name FROM players WHERE id = ? AND account_id = ?";
$stmt = $conn->prepare($validateQuery);
$stmt->bind_param("ii", $testCharacterId, $userId);
$stmt->execute();
$validateResult = $stmt->get_result();

if ($validateResult->num_rows > 0) {
    $char = $validateResult->fetch_assoc();
    echo "✓ Character validation passed: {$char['name']} (ID: {$char['id']})\n";
} else {
    echo "✗ Character validation failed - character not found or doesn't belong to account\n";
}

echo "\n";

// Test with wrong character ID (should fail)
$wrongCharacterId = 999999;
echo "Testing with wrong character ID: $wrongCharacterId\n";

$validateQuery = "SELECT id, name FROM players WHERE id = ? AND account_id = ?";
$stmt = $conn->prepare($validateQuery);
$stmt->bind_param("ii", $wrongCharacterId, $userId);
$stmt->execute();
$validateResult = $stmt->get_result();

if ($validateResult->num_rows > 0) {
    echo "✗ Security issue - wrong character ID was accepted\n";
} else {
    echo "✓ Security check passed - wrong character ID was rejected\n";
}

echo "\n✓ Character selection system test completed\n";

$conn->close();
?>
