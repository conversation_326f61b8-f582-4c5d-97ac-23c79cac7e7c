<?php
// Fix default images to use the new system
require_once "db.php";

echo "Fixing Default Images\n";
echo "====================\n";

// Update all items that use the old default.svg to use the new default system
$updateQuery = "UPDATE shop_items SET image_url = 'default' WHERE image_url = '/images/items/default.svg' OR image_url IS NULL OR image_url = ''";

if ($conn->query($updateQuery)) {
    $affected_rows = $conn->affected_rows;
    echo "✓ Updated $affected_rows items to use new default image system\n";
} else {
    echo "✗ Error updating items: " . $conn->error . "\n";
}

// Show current status
echo "\nCurrent items:\n";
$itemsQuery = "SELECT id, name, image_url FROM shop_items";
$result = $conn->query($itemsQuery);

if ($result->num_rows > 0) {
    while ($item = $result->fetch_assoc()) {
        $status = ($item['image_url'] === 'default') ? '📦 Default' : '🖼️ Custom';
        echo "- ID {$item['id']}: {$item['name']} → {$item['image_url']} ($status)\n";
    }
}

echo "\n✓ Default image fix completed\n";

$conn->close();
?>
