<?php
session_start();
require_once "db.php";

// Force check for lahart77 points
$username = 'lahart77';

echo "<h2>Points Test Page</h2>";
echo "<p>Testing points display for: <strong>$username</strong></p>";

// Get account info
$accountQuery = "SELECT id, name FROM account_data WHERE name = ?";
$stmt = $conn->prepare($accountQuery);
$stmt->bind_param("s", $username);
$stmt->execute();
$accountResult = $stmt->get_result();
$account = $accountResult->fetch_assoc();

if (!$account) {
    echo "<p style='color: red;'>❌ Account '$username' not found</p>";
    exit;
}

echo "<p>✅ Account found - ID: {$account['id']}</p>";

// Get balance using the same query as dashboard
$balanceQuery = "
    SELECT ab.value, pt.price_name, pt.symbolic
    FROM account_balance ab
    JOIN price_type pt ON ab.price_id = pt.price_id
    WHERE ab.account_id = ?
";

$stmt = $conn->prepare($balanceQuery);
$stmt->bind_param("i", $account['id']);
$stmt->execute();
$balanceResult = $stmt->get_result();

echo "<h3>Balance Results:</h3>";
if ($balanceResult->num_rows > 0) {
    while ($balance = $balanceResult->fetch_assoc()) {
        echo "<p style='color: green; font-size: 18px;'>✅ {$balance['price_name']}: <strong>{$balance['value']} {$balance['symbolic']}</strong></p>";
    }
} else {
    echo "<p style='color: red;'>❌ No balance found</p>";
    
    // Check if account_balance record exists at all
    $checkQuery = "SELECT * FROM account_balance WHERE account_id = ?";
    $stmt = $conn->prepare($checkQuery);
    $stmt->bind_param("i", $account['id']);
    $stmt->execute();
    $checkResult = $stmt->get_result();
    
    if ($checkResult->num_rows > 0) {
        echo "<p>Raw account_balance data:</p>";
        while ($row = $checkResult->fetch_assoc()) {
            echo "<pre>" . print_r($row, true) . "</pre>";
        }
    } else {
        echo "<p style='color: orange;'>⚠️ No account_balance record exists for this account</p>";
    }
}

// Check current session
echo "<h3>Current Session:</h3>";
if (isset($_SESSION["username"])) {
    echo "<p>Logged in as: <strong>{$_SESSION["username"]}</strong></p>";
    if ($_SESSION["username"] === $username) {
        echo "<p style='color: green;'>✅ Session matches test account</p>";
    } else {
        echo "<p style='color: orange;'>⚠️ Session is different from test account</p>";
    }
} else {
    echo "<p style='color: red;'>❌ Not logged in</p>";
}

echo "<hr>";
echo "<p><a href='dashboard.php'>Go to Dashboard</a> | <a href='shop.php'>Go to Shop</a></p>";

$conn->close();
?>
