<?php
// Shop management interface
session_start();
require_once "db.php";

// Simple admin check (temporarily disabled for testing)
// if (!isset($_SESSION["username"]) || $_SESSION["username"] !== "lahart77") {
//     echo "Access denied. Admin only.";
//     exit;
// }

// Function to resize and save images
function resizeAndSaveImage($source_path, $destination_path, $target_width, $target_height) {
    // Check if GD extension is available
    if (!extension_loaded('gd')) {
        // Fallback: just copy the original file without resizing
        return copy($source_path, $destination_path);
    }

    // Get image info
    $image_info = getimagesize($source_path);
    if (!$image_info) {
        return false;
    }

    $source_width = $image_info[0];
    $source_height = $image_info[1];
    $mime_type = $image_info['mime'];

    // Create source image resource based on type
    switch ($mime_type) {
        case 'image/jpeg':
            $source_image = imagecreatefromjpeg($source_path);
            break;
        case 'image/png':
            $source_image = imagecreatefrompng($source_path);
            break;
        case 'image/gif':
            $source_image = imagecreatefromgif($source_path);
            break;
        case 'image/webp':
            $source_image = imagecreatefromwebp($source_path);
            break;
        default:
            return false;
    }

    if (!$source_image) {
        return false;
    }

    // Create destination image
    $destination_image = imagecreatetruecolor($target_width, $target_height);

    // Preserve transparency for PNG and GIF
    if ($mime_type == 'image/png' || $mime_type == 'image/gif') {
        imagealphablending($destination_image, false);
        imagesavealpha($destination_image, true);
        $transparent = imagecolorallocatealpha($destination_image, 255, 255, 255, 127);
        imagefill($destination_image, 0, 0, $transparent);
    }

    // Resize image
    imagecopyresampled(
        $destination_image, $source_image,
        0, 0, 0, 0,
        $target_width, $target_height,
        $source_width, $source_height
    );

    // Save as PNG for best quality and transparency support
    $result = imagepng($destination_image, $destination_path, 6); // Compression level 6 (good balance)

    // Clean up memory
    imagedestroy($source_image);
    imagedestroy($destination_image);

    return $result;
}

// Handle form submissions
if ($_POST) {
    if (isset($_POST['add_item'])) {
        $item_id = intval($_POST['item_id']);
        $name = $_POST['name'];
        $description = $_POST['description'];
        $price = intval($_POST['price']);
        $quantity = intval($_POST['quantity']);
        $rarity = $_POST['rarity'];
        $category_id = intval($_POST['category_id']);

        // Handle image upload
        $image_url = 'default'; // Will be handled by display logic

        if (isset($_FILES['item_image']) && $_FILES['item_image']['error'] !== UPLOAD_ERR_NO_FILE) {
            $upload_dir = 'images/items/';

            // Check for upload errors first
            if ($_FILES['item_image']['error'] !== UPLOAD_ERR_OK) {
                switch ($_FILES['item_image']['error']) {
                    case UPLOAD_ERR_INI_SIZE:
                        $error = "File too large (exceeds upload_max_filesize). Max: " . ini_get('upload_max_filesize');
                        break;
                    case UPLOAD_ERR_FORM_SIZE:
                        $error = "File too large (exceeds form limit)";
                        break;
                    case UPLOAD_ERR_PARTIAL:
                        $error = "File upload was interrupted";
                        break;
                    default:
                        $error = "Upload error code: " . $_FILES['item_image']['error'];
                }
            } else {
                // Create directory if it doesn't exist
                if (!is_dir($upload_dir)) {
                    mkdir($upload_dir, 0755, true);
                }

                $file_info = pathinfo($_FILES['item_image']['name']);
                $file_extension = strtolower($file_info['extension']);
                $file_size = $_FILES['item_image']['size'];

                // Check file size (limit to 5MB)
                if ($file_size > 5 * 1024 * 1024) {
                    $error = "File too large. Maximum size is 5MB. Your file: " . round($file_size / 1024 / 1024, 2) . "MB";
                } else {
                    // Validate file type
                    $allowed_types = ['jpg', 'jpeg', 'png', 'gif', 'webp'];
                    if (in_array($file_extension, $allowed_types)) {
                        // Generate unique filename
                        $new_filename = $item_id . '_' . time() . '.png'; // Always save as PNG for best quality
                        $upload_path = $upload_dir . $new_filename;

                        // Resize and save image
                        if (resizeAndSaveImage($_FILES['item_image']['tmp_name'], $upload_path, 128, 128)) {
                            $image_url = 'images/items/' . $new_filename;
                            $final_size = filesize($upload_path);

                            if (extension_loaded('gd')) {
                                $success_note = "Image uploaded and resized: " . $new_filename . " (Original: " . round($file_size / 1024, 2) . "KB → Resized: " . round($final_size / 1024, 2) . "KB)";
                            } else {
                                $success_note = "Image uploaded: " . $new_filename . " (Original size kept - GD extension not available for resizing)";
                            }
                        } else {
                            $error = "Failed to process image.";
                        }
                    } else {
                        $error = "Invalid image type. Please use JPG, PNG, GIF, or WebP. Using default image.";
                    }
                }
            }
        }

        $insertQuery = "INSERT INTO shop_items (item_id, name, description, price, quantity, image_url, category_id, rarity) VALUES (?, ?, ?, ?, ?, ?, ?, ?)";
        $stmt = $conn->prepare($insertQuery);
        $stmt->bind_param("issiisis", $item_id, $name, $description, $price, $quantity, $image_url, $category_id, $rarity);

        if ($stmt->execute()) {
            $success = "Item added successfully!";
            if (isset($success_note)) {
                $success .= " " . $success_note;
            }
            if (isset($error)) {
                $success .= " (Note: " . $error . ")";
            }
        } else {
            $error = "Error adding item: " . $stmt->error;
        }
    }
    
    if (isset($_POST['update_image'])) {
        $update_id = intval($_POST['update_id']);

        if (isset($_FILES['new_image']) && $_FILES['new_image']['error'] === UPLOAD_ERR_OK) {
            $upload_dir = 'images/items/';

            // Create directory if it doesn't exist
            if (!is_dir($upload_dir)) {
                mkdir($upload_dir, 0755, true);
            }

            $file_info = pathinfo($_FILES['new_image']['name']);
            $file_extension = strtolower($file_info['extension']);

            // Validate file type
            $allowed_types = ['jpg', 'jpeg', 'png', 'gif', 'webp'];
            if (in_array($file_extension, $allowed_types)) {
                // Get item info for filename
                $itemQuery = "SELECT item_id FROM shop_items WHERE id = ?";
                $stmt = $conn->prepare($itemQuery);
                $stmt->bind_param("i", $update_id);
                $stmt->execute();
                $itemResult = $stmt->get_result();
                $itemData = $itemResult->fetch_assoc();

                if ($itemData) {
                    // Generate unique filename
                    $new_filename = $itemData['item_id'] . '_' . time() . '.png'; // Always save as PNG
                    $upload_path = $upload_dir . $new_filename;

                    // Resize and save image
                    if (resizeAndSaveImage($_FILES['new_image']['tmp_name'], $upload_path, 128, 128)) {
                        $image_url = 'images/items/' . $new_filename;

                        // Update database
                        $updateQuery = "UPDATE shop_items SET image_url = ? WHERE id = ?";
                        $stmt = $conn->prepare($updateQuery);
                        $stmt->bind_param("si", $image_url, $update_id);

                        if ($stmt->execute()) {
                            $final_size = filesize($upload_path);
                            if (extension_loaded('gd')) {
                                $success = "Image updated and resized successfully! (Resized to: " . round($final_size / 1024, 2) . "KB)";
                            } else {
                                $success = "Image updated successfully! (Original size kept - GD extension not available for resizing)";
                            }
                        } else {
                            $error = "Failed to update database: " . $stmt->error;
                        }
                    } else {
                        $error = "Failed to process image.";
                    }
                } else {
                    $error = "Item not found.";
                }
            } else {
                $error = "Invalid image type. Please use JPG, PNG, GIF, or WebP.";
            }
        } else {
            $error = "Please select an image file.";
        }
    }

    if (isset($_POST['delete_item'])) {
        $delete_id = intval($_POST['delete_id']);
        $deleteQuery = "DELETE FROM shop_items WHERE id = ?";
        $stmt = $conn->prepare($deleteQuery);
        $stmt->bind_param("i", $delete_id);

        if ($stmt->execute()) {
            $success = "Item deleted successfully!";
        } else {
            $error = "Error deleting item: " . $stmt->error;
        }
    }
}

// Get categories for dropdown
$categoriesQuery = "SELECT * FROM shop_categories WHERE is_active = 1 ORDER BY display_order";
$categoriesResult = $conn->query($categoriesQuery);

// Get current shop items with category names
$itemsQuery = "
    SELECT si.*, sc.name as category_name, sc.icon as category_icon
    FROM shop_items si
    LEFT JOIN shop_categories sc ON si.category_id = sc.id
    ORDER BY si.category_id, si.price ASC
";
$itemsResult = $conn->query($itemsQuery);

// Check if query failed
if (!$itemsResult) {
    echo "<div style='color: red; padding: 20px; background: rgba(255,0,0,0.1);'>";
    echo "<h3>Database Error:</h3>";
    echo "<p>Error: " . $conn->error . "</p>";
    echo "<p>This usually means the 'shop_items' table doesn't exist yet.</p>";
    echo "</div>";
}
?>

<!DOCTYPE html>
<html>
<head>
    <title>Shop Management - Aion-Blitz</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background: #1a1a1a; color: #fff; }
        .container { max-width: 1200px; margin: 0 auto; }
        .form-group { margin: 15px 0; }
        label { display: inline-block; width: 120px; font-weight: bold; }
        input, select, textarea { padding: 8px; margin: 5px; width: 200px; }
        input[type="file"] { width: 250px; }
        small { display: block; margin-left: 125px; color: #ccc; font-style: italic; }
        button { padding: 10px 20px; background: #d4af37; color: #000; border: none; cursor: pointer; }
        button:hover { background: #f4d03f; }
        .success { color: #4CAF50; padding: 10px; background: rgba(76, 175, 80, 0.1); border-radius: 5px; }
        .error { color: #f44336; padding: 10px; background: rgba(244, 67, 54, 0.1); border-radius: 5px; }
        table { width: 100%; border-collapse: collapse; margin: 20px 0; }
        th, td { padding: 10px; border: 1px solid #333; text-align: left; vertical-align: middle; }
        th { background: #333; }
        .delete-btn { background: #f44336; color: white; padding: 5px 10px; border: none; cursor: pointer; }

        /* Prevent image flickering */
        img {
            transition: opacity 0.2s ease;
            background: #333;
        }

        img[src=""] {
            opacity: 0;
        }

        /* Image loading placeholder */
        .image-cell {
            width: 70px;
            height: 70px;
            position: relative;
        }

        /* Loading state */
        .image-cell::before {
            content: '';
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            width: 20px;
            height: 20px;
            border: 2px solid #d4af37;
            border-top: 2px solid transparent;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            opacity: 0;
            pointer-events: none;
        }

        .image-cell.loading::before {
            opacity: 1;
        }

        @keyframes spin {
            0% { transform: translate(-50%, -50%) rotate(0deg); }
            100% { transform: translate(-50%, -50%) rotate(360deg); }
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🛒 Shop Management</h1>
        
        <?php if (isset($success)): ?>
            <div class="success"><?php echo $success; ?></div>
        <?php endif; ?>
        
        <?php if (isset($error)): ?>
            <div class="error"><?php echo $error; ?></div>
        <?php endif; ?>
        
        <h2>Add New Item</h2>
        <form method="POST" enctype="multipart/form-data">
            <div class="form-group">
                <label>Item ID:</label>
                <input type="number" name="item_id" required placeholder="e.g. 164000073">
            </div>
            <div class="form-group">
                <label>Name:</label>
                <input type="text" name="name" required placeholder="e.g. Health Potion (x5)">
            </div>
            <div class="form-group">
                <label>Description:</label>
                <textarea name="description" required placeholder="Item description"></textarea>
            </div>
            <div class="form-group">
                <label>Price (MP):</label>
                <input type="number" name="price" required placeholder="e.g. 50">
            </div>
            <div class="form-group">
                <label>Quantity:</label>
                <input type="number" name="quantity" required placeholder="e.g. 5">
            </div>
            <div class="form-group">
                <label>Category:</label>
                <select name="category_id" required>
                    <?php
                    if ($categoriesResult && $categoriesResult->num_rows > 0) {
                        while ($category = $categoriesResult->fetch_assoc()) {
                            // Convert text icons to emojis
                            $icon = $category['icon'];
                            switch($icon) {
                                case 'sword': $icon = '⚔️'; break;
                                case 'shirt': $icon = '👗'; break;
                                case 'potion': $icon = '🧪'; break;
                                default: $icon = '📦'; break;
                            }
                            echo "<option value='" . $category['id'] . "'>" . $icon . " " . $category['name'] . "</option>";
                        }
                        // Reset result pointer for later use
                        $categoriesResult->data_seek(0);
                    } else {
                        echo "<option value='1'>📦 Items (Default)</option>";
                    }
                    ?>
                </select>
            </div>
            <div class="form-group">
                <label>Item Image:</label>
                <input type="file" name="item_image" accept="image/*">
                <small>Upload any size JPG, PNG, GIF, or WebP image - will be automatically resized to 128x128px (optional - will use default if not provided)</small>
            </div>
            <div class="form-group">
                <label>Rarity:</label>
                <select name="rarity" required>
                    <option value="Common">Common</option>
                    <option value="Superior">Superior</option>
                    <option value="Heroic">Heroic</option>
                    <option value="Fabled">Fabled</option>
                    <option value="Eternal">Eternal</option>
                    <option value="Mythic">Mythic</option>
                    <option value="Legendary">Legendary</option>
                </select>
            </div>
            <button type="submit" name="add_item">Add Item</button>
        </form>
        
        <h2>Current Shop Items</h2>
        <table>
            <tr>
                <th>ID</th>
                <th>Item ID</th>
                <th>Image</th>
                <th>Name</th>
                <th>Description</th>
                <th>Category</th>
                <th>Price</th>
                <th>Quantity</th>
                <th>Rarity</th>
                <th>Actions</th>
            </tr>
            <?php if ($itemsResult->num_rows > 0): ?>
                <?php while ($item = $itemsResult->fetch_assoc()): ?>
                    <tr>
                        <td><?php echo $item['id']; ?></td>
                        <td><?php echo $item['item_id']; ?></td>
                        <td class="image-cell">
                            <?php
                            // Handle both old format (/images/items/file.jpg) and new format (images/items/file.jpg)
                            $image_url = $item['image_url'];
                            if (strpos($image_url, '/') === 0) {
                                $image_url = substr($image_url, 1); // Remove leading slash
                            }

                            $image_path = $image_url;
                            $image_exists = !empty($image_url) &&
                                           $image_url !== 'images/items/default.svg' &&
                                           file_exists($image_path);

                            if ($image_exists) {
                                $display_image = $image_url;
                            } else {
                                // Use a base64 encoded default image that will always work
                                $display_image = 'data:image/svg+xml;base64,' . base64_encode('
                                <svg width="50" height="50" xmlns="http://www.w3.org/2000/svg">
                                    <rect width="50" height="50" fill="#333333"/>
                                    <rect x="2" y="2" width="46" height="46" stroke="#d4af37" stroke-width="1" fill="none"/>
                                    <rect x="12" y="12" width="26" height="26" fill="#666666"/>
                                    <rect x="15" y="15" width="20" height="3" fill="#d4af37"/>
                                    <rect x="15" y="20" width="12" height="3" fill="#d4af37"/>
                                    <text x="25" y="42" text-anchor="middle" fill="#d4af37" font-size="6" font-family="Arial">ITEM</text>
                                </svg>');
                            }
                            ?>
                            <img src="<?php echo htmlspecialchars($display_image); ?>"
                                 alt="<?php echo htmlspecialchars($item['name']); ?>"
                                 style="width: 50px; height: 50px; object-fit: cover; border-radius: 5px; border: 1px solid #d4af37;"
                                 loading="lazy">
                        </td>
                        <td><?php echo htmlspecialchars($item['name']); ?></td>
                        <td><?php echo htmlspecialchars($item['description']); ?></td>
                        <td>
                            <?php
                            if ($item['category_name']) {
                                // Convert text icons to emojis
                                $icon = $item['category_icon'];
                                switch($icon) {
                                    case 'sword': $icon = '⚔️'; break;
                                    case 'shirt': $icon = '👗'; break;
                                    case 'potion': $icon = '🧪'; break;
                                    default: $icon = '📦'; break;
                                }
                                echo $icon . ' ' . $item['category_name'];
                            } else {
                                echo '📦 Items';
                            }
                            ?>
                        </td>
                        <td><?php echo $item['price']; ?> MP</td>
                        <td><?php echo $item['quantity']; ?></td>
                        <td><?php echo $item['rarity']; ?></td>
                        <td>
                            <form method="POST" enctype="multipart/form-data" style="display: inline; margin-right: 10px;">
                                <input type="hidden" name="update_id" value="<?php echo $item['id']; ?>">
                                <input type="file" name="new_image" accept="image/*" style="width: 100px; font-size: 10px;">
                                <button type="submit" name="update_image" style="background: #4CAF50; color: white; padding: 3px 8px; border: none; cursor: pointer; font-size: 11px;">Update Image</button>
                            </form>
                            <form method="POST" style="display: inline;">
                                <input type="hidden" name="delete_id" value="<?php echo $item['id']; ?>">
                                <button type="submit" name="delete_item" class="delete-btn" onclick="return confirm('Delete this item?')">Delete</button>
                            </form>
                        </td>
                    </tr>
                <?php endwhile; ?>
            <?php else: ?>
                <tr><td colspan="10">No items in shop</td></tr>
            <?php endif; ?>
        </table>
        
        <p><a href="dashboard.php" style="color: #d4af37;">← Back to Dashboard</a></p>
    </div>
</body>
</html>

<?php $conn->close(); ?>
