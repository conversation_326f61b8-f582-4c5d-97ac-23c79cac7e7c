-- Fix the trigger to include attached_kinah_count field
DROP TRIGGER IF EXISTS `trg_process_web_reward`;
delimiter ;;
CREATE TRIGGER `trg_process_web_reward` AFTER INSERT ON `player_web_rewards` FOR EACH ROW BEGIN
    -- Declare variables to hold the new unique IDs
    DECLARE new_item_unique_id INT;
    DECLARE new_mail_id INT;
    DECLARE mail_title VARCHAR(255);
    DECLARE mail_body VARCHAR(1000);

    -- Generate a unique ID for the new item in the inventory
    SELECT MAX(item_unique_id) + 1 INTO new_item_unique_id FROM inventory;
    IF new_item_unique_id IS NULL THEN
        SET new_item_unique_id = 1;
    END IF;

    -- Generate a unique ID for the new mail
    SELECT MAX(mail_unique_id) + 1 INTO new_mail_id FROM mail;
    IF new_mail_id IS NULL THEN
        SET new_mail_id = 1;
    END IF;
    
    -- Create the item in the inventory table, setting its location to 127 (Mail Attachment)
    -- This is the correct logic from the Beyond Aion source code.
    INSERT INTO `inventory` (item_unique_id, item_id, item_count, item_owner, item_location)
    VALUES (new_item_unique_id, NEW.item_id, NEW.item_count, NEW.player_id, 127);
    
    -- Set the mail title and body
    SET mail_title = 'Daily Login Reward';
    SET mail_body = 'Thank you for playing Aion-Blitz! Here is your daily login reward.';

    -- Create the mail record and attach the item's unique ID (including attached_kinah_count = 0)
    INSERT INTO `mail` (mail_unique_id, mail_recipient_id, sender_name, mail_title, mail_message, attached_item_id, attached_kinah_count, unread, express)
    VALUES (new_mail_id, NEW.player_id, 'Daily Rewards', mail_title, mail_body, new_item_unique_id, 0, 1, 1);

    -- Mark the reward as processed so it doesn't get sent again
    UPDATE `player_web_rewards` SET `received` = NOW() WHERE `entry_id` = NEW.entry_id;

END
;;
delimiter ;
