<?php
// Test the donation system to make sure it adds points correctly
session_start();
require_once "db.php";

// Simulate being logged in as lahart77 for testing
$_SESSION["username"] = "lahart77";

echo "Testing Donation System\n";
echo "=======================\n";

// Test donation data
$testDonation = [
    'order_id' => 'TEST_' . time(),
    'amount' => 25.00, // 25 euros = 25 points
    'payer_name' => 'Test User',
    'payer_email' => '<EMAIL>'
];

echo "Test donation: {$testDonation['amount']} EUR\n";
echo "Expected points: " . intval($testDonation['amount']) . " DP\n\n";

// Get current balance before donation
$accountQuery = "SELECT id, name FROM account_data WHERE name = ?";
$stmt = $conn->prepare($accountQuery);
$stmt->bind_param("s", $_SESSION["username"]);
$stmt->execute();
$accountResult = $stmt->get_result();
$account = $accountResult->fetch_assoc();

if (!$account) {
    echo "✗ Account not found\n";
    exit;
}

$userId = $account['id'];
echo "Account: {$account['name']} (ID: $userId)\n";

// Check current balance
$balanceQuery = "SELECT value FROM account_balance WHERE account_id = ? AND price_id = 1";
$stmt = $conn->prepare($balanceQuery);
$stmt->bind_param("i", $userId);
$stmt->execute();
$balanceResult = $stmt->get_result();
$balance = $balanceResult->fetch_assoc();
$currentPoints = $balance['value'] ?? 0;

echo "Current balance: $currentPoints DP\n\n";

// Simulate the donation process
echo "Processing test donation...\n";

// Call the donation processing logic directly
$orderId = $testDonation['order_id'];
$amount = floatval($testDonation['amount']);
$accountName = $_SESSION["username"];

// Check if order already exists
$checkQuery = "SELECT id FROM donations WHERE paypal_order_id = ?";
$stmt = $conn->prepare($checkQuery);
$stmt->bind_param("s", $orderId);
$stmt->execute();
$checkResult = $stmt->get_result();

if ($checkResult->num_rows > 0) {
    echo "✗ Order already exists\n";
    exit;
}

// Insert donation record
$insertQuery = "
    INSERT INTO donations (user_id, amount, paypal_order_id, status, created_at)
    VALUES (?, ?, ?, 'completed', NOW())
";

$stmt = $conn->prepare($insertQuery);
$stmt->bind_param("ids", $userId, $amount, $orderId);

if ($stmt->execute()) {
    $donationId = $conn->insert_id;
    echo "✓ Donation record created (ID: $donationId)\n";
    
    // Update membership level
    $newMembership = 0;
    if ($amount >= 50) $newMembership = 4; // Platinum
    elseif ($amount >= 25) $newMembership = 3; // Gold
    elseif ($amount >= 10) $newMembership = 2; // Silver
    elseif ($amount >= 5) $newMembership = 1; // Bronze
    
    if ($newMembership > 0) {
        $updateMembershipQuery = "UPDATE account_data SET membership = GREATEST(membership, ?) WHERE id = ?";
        $stmt = $conn->prepare($updateMembershipQuery);
        $stmt->bind_param("ii", $newMembership, $userId);
        $stmt->execute();
        echo "✓ Membership updated to level $newMembership\n";
    }
    
    // Add donation points (1 euro = 1 point)
    $donationPoints = intval($amount);
    $priceId = 1; // Donation Points currency
    
    // Check if account balance exists
    $balanceCheckQuery = "SELECT value FROM account_balance WHERE account_id = ? AND price_id = ?";
    $stmt = $conn->prepare($balanceCheckQuery);
    $stmt->bind_param("ii", $userId, $priceId);
    $stmt->execute();
    $balanceResult = $stmt->get_result();
    
    if ($balanceResult->num_rows > 0) {
        // Update existing balance
        $updateBalanceQuery = "UPDATE account_balance SET value = value + ? WHERE account_id = ? AND price_id = ?";
        $stmt = $conn->prepare($updateBalanceQuery);
        $stmt->bind_param("iii", $donationPoints, $userId, $priceId);
        $stmt->execute();
        echo "✓ Added $donationPoints points to existing balance\n";
    } else {
        // Insert new balance record
        $insertBalanceQuery = "INSERT INTO account_balance (account_id, account_name, price_id, value) VALUES (?, ?, ?, ?)";
        $stmt = $conn->prepare($insertBalanceQuery);
        $stmt->bind_param("isii", $userId, $accountName, $priceId, $donationPoints);
        $stmt->execute();
        echo "✓ Created new balance with $donationPoints points\n";
    }
    
    // Log the transaction
    $historyQuery = "INSERT INTO balance_history (date, reference_id, account_id, account_name, price_id, amount, description, type) VALUES (NOW(), ?, ?, ?, ?, ?, 'PayPal Donation', 'IN')";
    $stmt = $conn->prepare($historyQuery);
    $stmt->bind_param("ssiii", $orderId, $userId, $accountName, $priceId, $donationPoints);
    $stmt->execute();
    echo "✓ Transaction logged in history\n";
    
    // Check final balance
    $finalBalanceQuery = "SELECT value FROM account_balance WHERE account_id = ? AND price_id = 1";
    $stmt = $conn->prepare($finalBalanceQuery);
    $stmt->bind_param("i", $userId);
    $stmt->execute();
    $finalBalanceResult = $stmt->get_result();
    $finalBalance = $finalBalanceResult->fetch_assoc();
    $finalPoints = $finalBalance['value'] ?? 0;
    
    echo "\n=== RESULTS ===\n";
    echo "Previous balance: $currentPoints DP\n";
    echo "Donation amount: $amount EUR\n";
    echo "Points added: $donationPoints DP\n";
    echo "Final balance: $finalPoints DP\n";
    echo "Expected balance: " . ($currentPoints + $donationPoints) . " DP\n";
    
    if ($finalPoints == ($currentPoints + $donationPoints)) {
        echo "✅ SUCCESS: Points added correctly!\n";
    } else {
        echo "❌ ERROR: Points calculation mismatch!\n";
    }
    
} else {
    echo "✗ Failed to create donation record: " . $conn->error . "\n";
}

$conn->close();
?>
