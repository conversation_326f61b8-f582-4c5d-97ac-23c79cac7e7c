<?php
require_once "db.php";

echo "<h2>🔍 Gift System Debug</h2>";

// Check players table
echo "<h3>Players in Database:</h3>";
$playersQuery = "SELECT id, name, account_id FROM players ORDER BY name LIMIT 20";
$result = $conn->query($playersQuery);

if ($result->num_rows > 0) {
    echo "<table border='1' style='border-collapse: collapse;'>";
    echo "<tr><th>ID</th><th>Name</th><th>Account ID</th></tr>";
    while ($row = $result->fetch_assoc()) {
        echo "<tr><td>" . $row['id'] . "</td><td>" . $row['name'] . "</td><td>" . $row['account_id'] . "</td></tr>";
    }
    echo "</table>";
} else {
    echo "No players found in database.";
}

// Check mail table structure
echo "<h3>Mail Table Structure:</h3>";
$mailStructure = $conn->query("DESCRIBE mail");
if ($mailStructure) {
    echo "<table border='1' style='border-collapse: collapse;'>";
    echo "<tr><th>Field</th><th>Type</th><th>Null</th><th>Key</th><th>Default</th></tr>";
    while ($row = $mailStructure->fetch_assoc()) {
        echo "<tr>";
        echo "<td>" . $row['Field'] . "</td>";
        echo "<td>" . $row['Type'] . "</td>";
        echo "<td>" . $row['Null'] . "</td>";
        echo "<td>" . $row['Key'] . "</td>";
        echo "<td>" . $row['Default'] . "</td>";
        echo "</tr>";
    }
    echo "</table>";
} else {
    echo "Could not get mail table structure.";
}

// Check recent mail with player names
echo "<h3>Recent Mail (Last 10):</h3>";
$recentMail = $conn->query("
    SELECT m.mail_unique_id, m.mail_recipient_id, p.name as recipient_name, p.account_id,
           m.sender_name, m.mail_title, m.attached_item_id, m.unread, m.express
    FROM mail m
    LEFT JOIN players p ON m.mail_recipient_id = p.id
    ORDER BY m.mail_unique_id DESC LIMIT 10
");
if ($recentMail && $recentMail->num_rows > 0) {
    echo "<table border='1' style='border-collapse: collapse;'>";
    echo "<tr><th>Mail ID</th><th>Recipient ID</th><th>Recipient Name</th><th>Account ID</th><th>Sender</th><th>Title</th><th>Item ID</th><th>Unread</th><th>Express</th></tr>";
    while ($row = $recentMail->fetch_assoc()) {
        echo "<tr>";
        echo "<td>" . $row['mail_unique_id'] . "</td>";
        echo "<td>" . $row['mail_recipient_id'] . "</td>";
        echo "<td>" . $row['recipient_name'] . "</td>";
        echo "<td>" . $row['account_id'] . "</td>";
        echo "<td>" . $row['sender_name'] . "</td>";
        echo "<td>" . $row['mail_title'] . "</td>";
        echo "<td>" . $row['attached_item_id'] . "</td>";
        echo "<td>" . ($row['unread'] ? 'Yes' : 'No') . "</td>";
        echo "<td>" . ($row['express'] ? 'Yes' : 'No') . "</td>";
        echo "</tr>";
    }
    echo "</table>";
} else {
    echo "No mail found in database.";
}

// Check recent inventory items (mail attachments)
echo "<h3>Recent Inventory Items (Location 127 = Mail Attachments):</h3>";
$recentItems = $conn->query("
    SELECT i.item_unique_id, i.item_id, i.item_count, i.item_owner, i.item_location, p.name as owner_name
    FROM inventory i
    LEFT JOIN players p ON i.item_owner = p.id
    WHERE i.item_location = 127
    ORDER BY i.item_unique_id DESC LIMIT 10
");
if ($recentItems && $recentItems->num_rows > 0) {
    echo "<table border='1' style='border-collapse: collapse;'>";
    echo "<tr><th>Item Unique ID</th><th>Item ID</th><th>Count</th><th>Owner ID</th><th>Owner Name</th><th>Location</th></tr>";
    while ($row = $recentItems->fetch_assoc()) {
        echo "<tr>";
        echo "<td>" . $row['item_unique_id'] . "</td>";
        echo "<td>" . $row['item_id'] . "</td>";
        echo "<td>" . $row['item_count'] . "</td>";
        echo "<td>" . $row['item_owner'] . "</td>";
        echo "<td>" . $row['owner_name'] . "</td>";
        echo "<td>" . $row['item_location'] . "</td>";
        echo "</tr>";
    }
    echo "</table>";
} else {
    echo "No mail attachment items found.";
}

// Check inventory table structure
echo "<h3>Inventory Table Structure:</h3>";
$invStructure = $conn->query("DESCRIBE inventory");
if ($invStructure) {
    echo "<table border='1' style='border-collapse: collapse;'>";
    echo "<tr><th>Field</th><th>Type</th><th>Null</th><th>Key</th><th>Default</th></tr>";
    while ($row = $invStructure->fetch_assoc()) {
        echo "<tr>";
        echo "<td>" . $row['Field'] . "</td>";
        echo "<td>" . $row['Type'] . "</td>";
        echo "<td>" . $row['Null'] . "</td>";
        echo "<td>" . $row['Key'] . "</td>";
        echo "<td>" . $row['Default'] . "</td>";
        echo "</tr>";
    }
    echo "</table>";
} else {
    echo "Could not get inventory table structure.";
}

$conn->close();
?>
