<?php
echo "Fixing currency type...\n";

require_once "db.php";

// Test database connection
if ($conn->connect_error) {
    die("Connection failed: " . $conn->connect_error);
}

echo "✓ Database connection successful!\n";

// Check current price_type table
echo "\nCurrent price_type table:\n";
$priceQuery = "SELECT * FROM price_type";
$result = $conn->query($priceQuery);

if ($result && $result->num_rows > 0) {
    while ($row = $result->fetch_assoc()) {
        echo "  ID: {$row['price_id']}, Name: {$row['price_name']}, Symbol: {$row['symbolic']}\n";
    }
} else {
    echo "  No records found\n";
}

// Update the currency to be Donation Points
echo "\nUpdating currency to Donation Points...\n";

$updateQuery = "UPDATE price_type SET price_name = 'Donation Points', symbolic = 'DP' WHERE price_id = 1";
if ($conn->query($updateQuery) === TRUE) {
    echo "✓ Currency updated successfully!\n";
} else {
    echo "✗ Error updating currency: " . $conn->error . "\n";
}

// Verify the change
echo "\nUpdated price_type table:\n";
$result = $conn->query($priceQuery);

if ($result && $result->num_rows > 0) {
    while ($row = $result->fetch_assoc()) {
        echo "  ID: {$row['price_id']}, Name: {$row['price_name']}, Symbol: {$row['symbolic']}\n";
    }
}

// Test the balance query that shop.php uses
echo "\nTesting shop balance query for admin account...\n";

$username = "admin";
$accountQuery = "SELECT id FROM account_data WHERE name = ?";
$stmt = $conn->prepare($accountQuery);
$stmt->bind_param("s", $username);
$stmt->execute();
$accountResult = $stmt->get_result();
$account = $accountResult->fetch_assoc();

$balanceQuery = "
    SELECT ab.value, pt.price_name, pt.symbolic
    FROM account_balance ab
    JOIN price_type pt ON ab.price_id = pt.price_id
    WHERE ab.account_id = ? AND pt.price_id = 1
";

$stmt = $conn->prepare($balanceQuery);
$stmt->bind_param("i", $account['id']);
$stmt->execute();
$balanceResult = $stmt->get_result();

if ($balanceResult->num_rows > 0) {
    $balance = $balanceResult->fetch_assoc();
    echo "✓ Shop will now show: {$balance['value']} {$balance['symbolic']}\n";
} else {
    echo "✗ Shop query still not working\n";
}

echo "\n✅ Currency fix complete!\n";
echo "Now refresh your shop page to see your DP balance!\n";

$conn->close();
?>
