<?php
require_once "db.php";

// Configuration
$accountName = "admin"; // Change this to your account name
$mightToAdd = 1000; // Change this to the amount you want to add

echo "=== ADDING MIGHT POINTS ===\n";
echo "Account: $accountName\n";
echo "Might to add: $mightToAdd\n\n";

// Get account ID
$accountQuery = "SELECT id, name FROM account_data WHERE name = ?";
$stmt = $conn->prepare($accountQuery);
$stmt->bind_param("s", $accountName);
$stmt->execute();
$accountResult = $stmt->get_result();

if ($accountResult->num_rows === 0) {
    die("✗ Account '$accountName' not found!\n");
}

$account = $accountResult->fetch_assoc();
$accountId = $account['id'];

echo "✓ Found account: {$account['name']} (ID: $accountId)\n";

// Check current might
$mightQuery = "SELECT might FROM might WHERE account_id = ?";
$stmt = $conn->prepare($mightQuery);
$stmt->bind_param("i", $accountId);
$stmt->execute();
$mightResult = $stmt->get_result();

if ($mightResult->num_rows > 0) {
    // Update existing might
    $currentMight = $mightResult->fetch_assoc()['might'];
    $newMight = $currentMight + $mightToAdd;
    
    echo "Current might: $currentMight\n";
    
    $updateQuery = "UPDATE might SET might = ? WHERE account_id = ?";
    $updateStmt = $conn->prepare($updateQuery);
    $updateStmt->bind_param("ii", $newMight, $accountId);
    
    if ($updateStmt->execute()) {
        echo "✓ Might updated! New balance: $newMight MP\n";
    } else {
        echo "✗ Error updating might: " . $updateStmt->error . "\n";
    }
} else {
    // Insert new might record
    echo "No existing might found. Creating new might record...\n";
    
    $insertQuery = "INSERT INTO might (account_id, might) VALUES (?, ?)";
    $insertStmt = $conn->prepare($insertQuery);
    $insertStmt->bind_param("ii", $accountId, $mightToAdd);
    
    if ($insertStmt->execute()) {
        echo "✓ Might created! New balance: $mightToAdd MP\n";
    } else {
        echo "✗ Error creating might: " . $insertStmt->error . "\n";
    }
}

echo "\n✅ Might Points added successfully!\n";
echo "🎉 You now have Might Points to test purchasing items in the shop!\n";
echo "Go to your website dashboard to see your new balance and test the shop!\n";

$conn->close();
?>
