<?php
session_start();
require_once "db.php";

echo "Session Debug Information\n";
echo "========================\n";

if (isset($_SESSION["username"])) {
    $sessionUsername = $_SESSION["username"];
    echo "✓ Logged in as: $sessionUsername\n";
    
    // Get account info for session user
    $accountQuery = "SELECT id, name, membership FROM account_data WHERE name = ?";
    $stmt = $conn->prepare($accountQuery);
    $stmt->bind_param("s", $sessionUsername);
    $stmt->execute();
    $accountResult = $stmt->get_result();
    $account = $accountResult->fetch_assoc();
    
    if ($account) {
        echo "✓ Account ID: {$account['id']}\n";
        echo "✓ Membership: {$account['membership']}\n";
        
        // Check points for this session user
        $balanceQuery = "
            SELECT ab.value, pt.price_name, pt.symbolic
            FROM account_balance ab
            JOIN price_type pt ON ab.price_id = pt.price_id
            WHERE ab.account_id = ?
        ";
        
        $stmt = $conn->prepare($balanceQuery);
        $stmt->bind_param("i", $account['id']);
        $stmt->execute();
        $balanceResult = $stmt->get_result();
        
        echo "\nPoints for session user:\n";
        if ($balanceResult->num_rows > 0) {
            while ($balance = $balanceResult->fetch_assoc()) {
                echo "✓ {$balance['price_name']}: {$balance['value']} {$balance['symbolic']}\n";
            }
        } else {
            echo "✗ No points found for this user\n";
        }
        
    } else {
        echo "✗ Account not found in database\n";
    }
    
} else {
    echo "✗ Not logged in (no session)\n";
}

echo "\nTo fix this:\n";
echo "1. Make sure you're logged in as 'lahart77'\n";
echo "2. If logged in as different user, I can add points to that user instead\n";
echo "3. Or logout and login as 'lahart77'\n";

$conn->close();
?>
