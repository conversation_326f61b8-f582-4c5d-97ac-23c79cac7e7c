<?php
echo "Testing corrected purchase with proper column names...\n";

require_once "db.php";

if ($conn->connect_error) {
    die("Connection failed: " . $conn->connect_error);
}

echo "✓ Database connection successful!\n\n";

// Test the corrected queries
echo "=== TESTING CORRECTED QUERIES ===\n";

// Test inventory insert with correct column names
$testInventoryQuery = "INSERT INTO inventory (itemUniqueId, itemId, itemCount, itemOwner, itemLocation, itemColor, isEquiped, isSoulBound, slot, enchant, itemCreator, itemSkin, fusionedItem, optionalSocket, optionalFusionSocket, conditioning) VALUES (?, ?, ?, ?, 127, 0, 0, 0, 0, 0, '', 0, 0, 0, 0, 0)";

echo "Testing corrected inventory query...\n";
$stmt = $conn->prepare($testInventoryQuery);
if ($stmt) {
    echo "✓ Inventory query prepared successfully\n";
    
    // Test with sample data
    $testItemId = 999;
    $gameItemId = 164000073;
    $quantity = 5;
    $playerId = 1217;
    
    $stmt->bind_param("iiii", $testItemId, $gameItemId, $quantity, $playerId);
    if ($stmt->execute()) {
        echo "✓ Test inventory insert successful\n";
        // Clean up
        $conn->query("DELETE FROM inventory WHERE itemUniqueId = $testItemId");
    } else {
        echo "✗ Test inventory insert failed: " . $stmt->error . "\n";
    }
} else {
    echo "✗ Inventory query preparation failed: " . $conn->error . "\n";
}

// Test mail insert with correct column names
$testMailQuery = "INSERT INTO mail (mailUniqueId, mailRecipientId, senderName, mailTitle, mailMessage, attachedItemId, attachedKinahCount, unread, express, recievedTime) VALUES (?, ?, ?, ?, ?, ?, 0, 1, 1, NOW())";

echo "\nTesting corrected mail query...\n";
$stmt = $conn->prepare($testMailQuery);
if ($stmt) {
    echo "✓ Mail query prepared successfully\n";
    
    // Test with sample data
    $testMailId = 999;
    $playerId = 1217;
    $sender = "Test Shop";
    $title = "Test Purchase";
    $message = "Test item purchase";
    $attachedItemId = 999;
    
    $stmt->bind_param("iisssi", $testMailId, $playerId, $sender, $title, $message, $attachedItemId);
    if ($stmt->execute()) {
        echo "✓ Test mail insert successful\n";
        // Clean up
        $conn->query("DELETE FROM mail WHERE mailUniqueId = $testMailId");
    } else {
        echo "✗ Test mail insert failed: " . $stmt->error . "\n";
    }
} else {
    echo "✗ Mail query preparation failed: " . $conn->error . "\n";
}

// Now let's do a complete purchase simulation
echo "\n=== COMPLETE PURCHASE SIMULATION ===\n";

try {
    $conn->begin_transaction();
    
    // Get next IDs
    $itemUniqueIdResult = $conn->query("SELECT COALESCE(MAX(itemUniqueId), 0) + 1 as next_id FROM inventory");
    $newItemUniqueId = $itemUniqueIdResult ? $itemUniqueIdResult->fetch_assoc()['next_id'] : 1;
    
    $mailUniqueIdResult = $conn->query("SELECT COALESCE(MAX(mailUniqueId), 0) + 1 as next_id FROM mail");
    $newMailId = $mailUniqueIdResult ? $mailUniqueIdResult->fetch_assoc()['next_id'] : 1;
    
    echo "✓ Generated IDs - Item: $newItemUniqueId, Mail: $newMailId\n";
    
    // Insert inventory item
    $inventoryQuery = "INSERT INTO inventory (itemUniqueId, itemId, itemCount, itemOwner, itemLocation, itemColor, isEquiped, isSoulBound, slot, enchant, itemCreator, itemSkin, fusionedItem, optionalSocket, optionalFusionSocket, conditioning) VALUES (?, ?, ?, ?, 127, 0, 0, 0, 0, 0, '', 0, 0, 0, 0, 0)";
    $stmt = $conn->prepare($inventoryQuery);
    $stmt->bind_param("iiii", $newItemUniqueId, 164000073, 5, 1217);
    
    if (!$stmt->execute()) {
        throw new Exception("Failed to insert inventory: " . $stmt->error);
    }
    echo "✓ Inventory item inserted\n";
    
    // Insert mail
    $mailQuery = "INSERT INTO mail (mailUniqueId, mailRecipientId, senderName, mailTitle, mailMessage, attachedItemId, attachedKinahCount, unread, express, recievedTime) VALUES (?, ?, ?, ?, ?, ?, 0, 1, 1, NOW())";
    $stmt = $conn->prepare($mailQuery);
    $mailMessage = "You have purchased: Test Reward Item (x5) for 10 DP. Enjoy your item!";
    $stmt->bind_param("iisssi", $newMailId, 1217, "Donation Shop", "Item Purchase", $mailMessage, $newItemUniqueId);
    
    if (!$stmt->execute()) {
        throw new Exception("Failed to insert mail: " . $stmt->error);
    }
    echo "✓ Mail inserted\n";
    
    // Deduct points (simulate)
    echo "✓ Points would be deducted here\n";
    
    // Commit transaction
    $conn->commit();
    
    echo "\n🎉 COMPLETE PURCHASE SIMULATION SUCCESSFUL!\n";
    echo "Item Unique ID: $newItemUniqueId\n";
    echo "Mail ID: $newMailId\n";
    echo "Character: Pinkert (1217)\n";
    echo "Item: Test Reward Item (164000073) x5\n";
    
    echo "\n✅ The purchase system should now work!\n";
    echo "Try purchasing an item in your browser now.\n";
    
} catch (Exception $e) {
    $conn->rollback();
    echo "\n❌ Purchase simulation failed: " . $e->getMessage() . "\n";
}

$conn->close();
?>
