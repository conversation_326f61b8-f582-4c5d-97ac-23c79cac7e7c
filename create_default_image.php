<?php
// Create a default item image
$width = 100;
$height = 100;

// Create image
$image = imagecreate($width, $height);

// Colors
$background = imagecolorallocate($image, 51, 51, 51);      // #333333
$border = imagecolorallocate($image, 212, 175, 55);       // #d4af37
$box = imagecolorallocate($image, 102, 102, 102);         // #666666
$text = imagecolorallocate($image, 212, 175, 55);         // #d4af37

// Fill background
imagefill($image, 0, 0, $background);

// Draw border
imagerectangle($image, 2, 2, $width-3, $height-3, $border);

// Draw item box
imagefilledrectangle($image, 25, 25, 75, 75, $box);

// Draw highlight
imagefilledrectangle($image, 30, 30, 70, 35, $border);
imagefilledrectangle($image, 30, 40, 55, 45, $border);

// Add text
$font_size = 3;
$text_width = imagefontwidth($font_size) * strlen('ITEM');
$x = ($width - $text_width) / 2;
imagestring($image, $font_size, $x, 85, 'ITEM', $text);

// Save image
$filename = 'images/items/default.png';
imagepng($image, $filename);
imagedestroy($image);

echo "Default image created: $filename\n";
echo "Image size: {$width}x{$height} pixels\n";
echo "✓ Ready for use in shop management\n";
?>
