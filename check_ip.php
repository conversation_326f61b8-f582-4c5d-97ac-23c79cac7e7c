<?php
// check_ip.php

require_once "block.php";

$clientIP = $_SERVER['REMOTE_ADDR'];

if (in_array($clientIP, $blockedIPs)) {
    // IP address is blocked
    $response = array('blocked' => true);
} else {
    // Check for excessive registrations
    $registrationLimit = 5; // Maximum allowed registrations per IP
    if (hasExcessiveRegistrations($clientIP, $registrationLimit)) {
        // Add the IP address to the blocked list
        $blockedIPs[] = $clientIP;
        file_put_contents('block.php', '<?php $blockedIPs = ' . var_export($blockedIPs, true) . ';');
        $response = array('blocked' => true);
    } else {
        // IP address is not blocked
        $response = array('blocked' => false);
    }
}

header('Content-Type: application/json');
echo json_encode($response);

// Function to check if the IP address has excessive registrations
function hasExcessiveRegistrations($ip, $limit) {
    // Implement your custom logic to check for excessive registrations
    // You can track the number of registrations for each IP in a database or file
    // Return true if the IP has exceeded the registration limit, false otherwise
}
?>
