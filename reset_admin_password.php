<?php
echo "Resetting admin password...\n";

require_once "db.php";

// Test database connection
if ($conn->connect_error) {
    die("Connection failed: " . $conn->connect_error);
}

echo "✓ Database connection successful!\n";

$username = "test22";
$newPassword = "test123"; // Simple password for testing

echo "Setting password for '$username' to '$newPassword'\n";

// Hash the password (check what method your login system uses)
$hashedPassword = password_hash($newPassword, PASSWORD_DEFAULT);

// Update the password
$updateQuery = "UPDATE account_data SET password = ? WHERE name = ?";
$stmt = $conn->prepare($updateQuery);
$stmt->bind_param("ss", $hashedPassword, $username);

if ($stmt->execute()) {
    echo "✓ Password updated successfully!\n";
    echo "\nLogin credentials:\n";
    echo "Username: $username\n";
    echo "Password: $newPassword\n";
    echo "\nNow go to your website and login with these credentials!\n";
} else {
    echo "✗ Error updating password: " . $stmt->error . "\n";
    
    // Check if the account exists and what columns are available
    echo "\nChecking account structure...\n";
    $result = $conn->query("DESCRIBE account_data");
    if ($result) {
        echo "account_data table columns:\n";
        while ($row = $result->fetch_assoc()) {
            echo "  - {$row['Field']} ({$row['Type']})\n";
        }
    }
}

$conn->close();
?>
