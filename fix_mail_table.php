<?php
echo "Fixing mail table structure...\n";

require_once "db.php";

if ($conn->connect_error) {
    die("Connection failed: " . $conn->connect_error);
}

echo "✓ Database connection successful!\n\n";

// Check current mail table structure
echo "=== CURRENT MAIL TABLE STRUCTURE ===\n";
$result = $conn->query("DESCRIBE mail");
if ($result) {
    $columns = [];
    while ($row = $result->fetch_assoc()) {
        $columns[] = $row['Field'];
        echo "- {$row['Field']}: {$row['Type']} " . ($row['Null'] == 'YES' ? '(NULL)' : '(NOT NULL)') . "\n";
    }
} else {
    echo "Error: " . $conn->error . "\n";
    exit;
}

// Check if temperance column exists
echo "\n=== CHECKING FOR MISSING COLUMNS ===\n";
$requiredColumns = [
    'temperance' => 'int(11) DEFAULT 0',
    'mail_type' => 'tinyint(4) DEFAULT 0'
];

foreach ($requiredColumns as $column => $definition) {
    if (!in_array($column, $columns)) {
        echo "✗ Missing column: $column\n";
        echo "Adding column: $column ($definition)\n";
        
        $alterQuery = "ALTER TABLE mail ADD COLUMN $column $definition";
        if ($conn->query($alterQuery) === TRUE) {
            echo "✓ Added column: $column\n";
        } else {
            echo "✗ Error adding column $column: " . $conn->error . "\n";
        }
    } else {
        echo "✓ Column exists: $column\n";
    }
}

// Check updated structure
echo "\n=== UPDATED MAIL TABLE STRUCTURE ===\n";
$result = $conn->query("DESCRIBE mail");
if ($result) {
    while ($row = $result->fetch_assoc()) {
        echo "- {$row['Field']}: {$row['Type']} " . ($row['Null'] == 'YES' ? '(NULL)' : '(NOT NULL)') . "\n";
    }
}

// Test if the mail system can now read properly
echo "\n=== TESTING MAIL QUERY ===\n";
$testQuery = "SELECT mailUniqueId, mailRecipientId, senderName, mailTitle, mailMessage, attachedItemId, attachedKinahCount, unread, express, recievedTime, temperance FROM mail LIMIT 1";
$result = $conn->query($testQuery);

if ($result) {
    echo "✓ Mail query with temperance column works!\n";
    if ($result->num_rows > 0) {
        $mail = $result->fetch_assoc();
        echo "Sample mail data:\n";
        foreach ($mail as $key => $value) {
            echo "  $key: $value\n";
        }
    }
} else {
    echo "✗ Mail query failed: " . $conn->error . "\n";
}

// Update existing mail records to have default temperance value
echo "\n=== UPDATING EXISTING MAIL RECORDS ===\n";
$updateQuery = "UPDATE mail SET temperance = 0 WHERE temperance IS NULL";
if ($conn->query($updateQuery) === TRUE) {
    echo "✓ Updated existing mail records with default temperance value\n";
    echo "Affected rows: " . $conn->affected_rows . "\n";
} else {
    echo "✗ Error updating mail records: " . $conn->error . "\n";
}

echo "\n✅ Mail table fix complete!\n";
echo "Now restart your game server and try checking mail again.\n";

$conn->close();
?>
