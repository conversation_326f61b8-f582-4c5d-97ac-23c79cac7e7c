<?php
require_once "db.php";

echo "<h2>🛍️ Setting Up Shop Categories</h2>";

// Create shop_categories table if it doesn't exist
$createCategoriesTable = "
CREATE TABLE IF NOT EXISTS shop_categories (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(50) NOT NULL,
    description TEXT,
    icon VARCHAR(10) DEFAULT '📦',
    sort_order INT DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
)";

if ($conn->query($createCategoriesTable)) {
    echo "✅ shop_categories table created/verified<br>";
} else {
    echo "❌ Error creating categories table: " . $conn->error . "<br>";
}

// Insert default categories
$categories = [
    ['name' => 'Items', 'description' => 'General items and equipment', 'icon' => '⚔️', 'sort_order' => 1],
    ['name' => 'Skins', 'description' => 'Cosmetic skins and appearances', 'icon' => '👗', 'sort_order' => 2],
    ['name' => 'Consumables', 'description' => 'Potions, food, and consumable items', 'icon' => '🧪', 'sort_order' => 3]
];

foreach ($categories as $category) {
    // Check if category already exists
    $checkQuery = "SELECT id FROM shop_categories WHERE name = ?";
    $stmt = $conn->prepare($checkQuery);

    if ($stmt === false) {
        echo "❌ Error preparing check query: " . $conn->error . "<br>";
        continue;
    }

    $stmt->bind_param("s", $category['name']);
    $stmt->execute();
    $result = $stmt->get_result();

    if ($result->num_rows == 0) {
        // Insert new category
        $insertQuery = "INSERT INTO shop_categories (name, description, icon, sort_order) VALUES (?, ?, ?, ?)";
        $stmt = $conn->prepare($insertQuery);

        if ($stmt === false) {
            echo "❌ Error preparing insert query: " . $conn->error . "<br>";
            continue;
        }

        $stmt->bind_param("sssi", $category['name'], $category['description'], $category['icon'], $category['sort_order']);

        if ($stmt->execute()) {
            echo "✅ Added category: " . $category['name'] . "<br>";
        } else {
            echo "❌ Error adding category " . $category['name'] . ": " . $stmt->error . "<br>";
        }
    } else {
        echo "ℹ️ Category already exists: " . $category['name'] . "<br>";
    }
}

// Update shop_items table to ensure category_id column exists
echo "<h3>Adding category_id column to shop_items:</h3>";

// First check if column already exists
$checkColumn = "SHOW COLUMNS FROM shop_items LIKE 'category_id'";
$result = $conn->query($checkColumn);

if ($result && $result->num_rows > 0) {
    echo "ℹ️ category_id column already exists in shop_items<br>";
} else {
    // Add the column
    $addCategoryColumn = "ALTER TABLE shop_items ADD COLUMN category_id INT DEFAULT 1";
    if ($conn->query($addCategoryColumn)) {
        echo "✅ category_id column added to shop_items<br>";
    } else {
        echo "❌ Error adding category_id column: " . $conn->error . "<br>";
    }
}

// Show final categories
echo "<h3>Final Categories:</h3>";
$categoriesQuery = "SELECT * FROM shop_categories ORDER BY sort_order";
$result = $conn->query($categoriesQuery);
if ($result && $result->num_rows > 0) {
    echo "<table border='1' style='border-collapse: collapse;'>";
    echo "<tr><th>ID</th><th>Icon</th><th>Name</th><th>Description</th><th>Sort Order</th></tr>";
    while ($row = $result->fetch_assoc()) {
        echo "<tr>";
        echo "<td>" . $row['id'] . "</td>";
        echo "<td>" . $row['icon'] . "</td>";
        echo "<td>" . $row['name'] . "</td>";
        echo "<td>" . $row['description'] . "</td>";
        echo "<td>" . $row['sort_order'] . "</td>";
        echo "</tr>";
    }
    echo "</table>";
}

echo "<br><strong>✅ Categories setup complete! You can now use categories in your shop.</strong>";

$conn->close();
?>
