<?php
// Test the gifting system
session_start();
require_once "db.php";

echo "Testing Gifting System\n";
echo "=====================\n";

// Get all characters in the database for testing
$allCharactersQuery = "SELECT p.id, p.name, p.player_class, a.name as account_name FROM players p JOIN account_data a ON p.account_id = a.id ORDER BY a.name, p.name";
$result = $conn->query($allCharactersQuery);

echo "Available characters for testing:\n";
if ($result->num_rows > 0) {
    while ($char = $result->fetch_assoc()) {
        echo "- {$char['name']} ({$char['player_class']}) - Account: {$char['account_name']} (ID: {$char['id']})\n";
    }
} else {
    echo "- No characters found\n";
}

echo "\n";

// Test character lookup by name
$testCharacterName = "test"; // Change this to an actual character name
echo "Testing character lookup for: '$testCharacterName'\n";

$lookupQuery = "SELECT id, name, account_id FROM players WHERE name = ?";
$stmt = $conn->prepare($lookupQuery);
$stmt->bind_param("s", $testCharacterName);
$stmt->execute();
$lookupResult = $stmt->get_result();

if ($lookupResult->num_rows > 0) {
    $char = $lookupResult->fetch_assoc();
    echo "✓ Character found: {$char['name']} (ID: {$char['id']}, Account: {$char['account_id']})\n";
} else {
    echo "✗ Character '$testCharacterName' not found\n";
}

echo "\n";

// Test case sensitivity
$testCharacterNameWrong = "TEST"; // Wrong case
echo "Testing case sensitivity with: '$testCharacterNameWrong'\n";

$stmt = $conn->prepare($lookupQuery);
$stmt->bind_param("s", $testCharacterNameWrong);
$stmt->execute();
$lookupResult = $stmt->get_result();

if ($lookupResult->num_rows > 0) {
    echo "✗ Case sensitivity issue - found character with wrong case\n";
} else {
    echo "✓ Case sensitivity working - character not found with wrong case\n";
}

echo "\n";

// Test self-gifting prevention
echo "Testing self-gifting prevention:\n";
$account1Id = 3; // lahart77's account
$account2Id = 3; // Same account (should be prevented)

if ($account1Id == $account2Id) {
    echo "✓ Self-gifting prevention would work (same account ID: $account1Id)\n";
} else {
    echo "✗ Self-gifting prevention would fail (different account IDs: $account1Id vs $account2Id)\n";
}

echo "\n";

// Show gift mail format example
echo "Gift mail format example:\n";
echo "Sender: 'Gift from lahart77'\n";
echo "Title: '🎁 Gift from lahart77'\n";
echo "Message: 'You have received a gift from lahart77! Item: Test Item (x5) (Quantity: 5)'\n";

echo "\n✓ Gifting system test completed\n";

$conn->close();
?>
