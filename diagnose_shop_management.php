<?php
// Diagnostic script for shop management issues
echo "<h1>Shop Management Diagnostics</h1>";
echo "<style>body{background:#1a1a1a;color:#fff;font-family:Arial;padding:20px;} .success{color:#4CAF50;} .error{color:#f44336;} .warning{color:#ff9800;}</style>";

echo "<h2>1. PHP Configuration</h2>";
echo "PHP Version: " . phpversion() . "<br>";
echo "Server: " . $_SERVER['SERVER_SOFTWARE'] . "<br>";
echo "Document Root: " . $_SERVER['DOCUMENT_ROOT'] . "<br>";
echo "Current Script: " . __FILE__ . "<br>";

echo "<h2>2. Database Connection Test</h2>";
try {
    require_once "db.php";
    echo "<span class='success'>✓ Database connection successful</span><br>";
    echo "Connected to database: " . $dbname . "<br>";
    
    // Test if shop_items table exists
    $result = $conn->query("SHOW TABLES LIKE 'shop_items'");
    if ($result->num_rows > 0) {
        echo "<span class='success'>✓ shop_items table exists</span><br>";
        
        // Count items
        $count_result = $conn->query("SELECT COUNT(*) as count FROM shop_items");
        $count = $count_result->fetch_assoc()['count'];
        echo "Items in shop: $count<br>";
    } else {
        echo "<span class='error'>✗ shop_items table missing</span><br>";
    }
    
} catch (Exception $e) {
    echo "<span class='error'>✗ Database connection failed: " . $e->getMessage() . "</span><br>";
}

echo "<h2>3. File Permissions</h2>";
$files_to_check = [
    'manage_shop.php',
    'db.php',
    'images',
    'images/items'
];

foreach ($files_to_check as $file) {
    if (file_exists($file)) {
        $perms = fileperms($file);
        $readable = is_readable($file) ? '✓' : '✗';
        $writable = is_writable($file) ? '✓' : '✗';
        echo "$file: Read $readable Write $writable<br>";
    } else {
        echo "<span class='error'>✗ $file not found</span><br>";
    }
}

echo "<h2>4. Session Test</h2>";
session_start();
if (session_status() === PHP_SESSION_ACTIVE) {
    echo "<span class='success'>✓ Sessions working</span><br>";
    echo "Session ID: " . session_id() . "<br>";
} else {
    echo "<span class='error'>✗ Session issues</span><br>";
}

echo "<h2>5. Error Reporting</h2>";
error_reporting(E_ALL);
ini_set('display_errors', 1);
echo "Error reporting enabled<br>";

echo "<h2>6. Access Test</h2>";
echo "Current user session: ";
if (isset($_SESSION["username"])) {
    echo $_SESSION["username"];
} else {
    echo "<span class='warning'>Not logged in</span>";
}
echo "<br>";

echo "<h2>7. Direct Access Test</h2>";
echo "<a href='manage_shop.php' style='color:#d4af37;'>Click here to test manage_shop.php</a><br>";

echo "<h2>8. Alternative Simple Shop Manager</h2>";
echo "<a href='simple_shop_manager.php' style='color:#d4af37;'>Try Simple Shop Manager (if created)</a><br>";

echo "<h2>9. Manual Database Query Test</h2>";
if (isset($conn)) {
    try {
        $result = $conn->query("SELECT * FROM shop_items LIMIT 3");
        if ($result && $result->num_rows > 0) {
            echo "<table border='1' style='color:#fff;'>";
            echo "<tr><th>ID</th><th>Name</th><th>Price</th></tr>";
            while ($row = $result->fetch_assoc()) {
                echo "<tr><td>{$row['id']}</td><td>{$row['name']}</td><td>{$row['price']}</td></tr>";
            }
            echo "</table>";
        } else {
            echo "No items found or query failed<br>";
        }
    } catch (Exception $e) {
        echo "<span class='error'>Query failed: " . $e->getMessage() . "</span><br>";
    }
}

echo "<h2>10. Troubleshooting Steps</h2>";
echo "If manage_shop.php doesn't work:<br>";
echo "1. Check if you're logged in as 'lahart77'<br>";
echo "2. Try the simple shop manager below<br>";
echo "3. Check browser console for JavaScript errors<br>";
echo "4. Check server error logs<br>";
?>

<?php if (isset($conn)) $conn->close(); ?>
