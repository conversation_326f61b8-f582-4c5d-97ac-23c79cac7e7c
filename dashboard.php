<?php
session_start();
require_once "db.php";

// Check if user is logged in
if (!isset($_SESSION["username"])) {
    header("Location: signin.php");
    exit;
}

// Create database connection
$conn = new mysqli($servername, $username, $password, $dbname);
if ($conn->connect_error) {
    die("Connection failed: " . $conn->connect_error);
}

$accountName = $_SESSION["username"];

// Get account information
$accountQuery = "
    SELECT 
        ad.id,
        ad.name,
        ad.creation_date,
        ad.access_level,
        ad.membership,
        at.last_active,
        at.accumulated_online,
        at.session_duration
    FROM account_data ad
    LEFT JOIN account_time at ON ad.id = at.account_id
    WHERE ad.name = ?
";

$stmt = $conn->prepare($accountQuery);
$stmt->bind_param("s", $accountName);
$stmt->execute();
$accountResult = $stmt->get_result();
$account = $accountResult->fetch_assoc();

if (!$account) {
    header("Location: signin.php");
    exit;
}

// Get player characters
$playersQuery = "
    SELECT 
        p.id,
        p.name,
        p.player_class,
        p.race,
        p.gender,
        p.exp,
        p.online,
        p.last_online,
        p.creation_date,
        ar.gp,
        ar.ap,
        ar.all_kill,
        ar.rank
    FROM players p
    LEFT JOIN abyss_rank ar ON p.id = ar.player_id
    WHERE p.account_id = ?
    ORDER BY p.creation_date DESC
";

$stmt = $conn->prepare($playersQuery);
$stmt->bind_param("i", $account['id']);
$stmt->execute();
$playersResult = $stmt->get_result();

// Get donation history
$donationsQuery = "
    SELECT 
        amount,
        paypal_order_id,
        status,
        created_at
    FROM donations
    WHERE user_id = ?
    ORDER BY created_at DESC
    LIMIT 10
";

$stmt = $conn->prepare($donationsQuery);
$stmt->bind_param("i", $account['id']);
$stmt->execute();
$donationsResult = $stmt->get_result();

// Calculate total donations
$totalDonationsQuery = "
    SELECT SUM(amount) as total_donated
    FROM donations
    WHERE user_id = ? AND status = 'completed'
";

$stmt = $conn->prepare($totalDonationsQuery);
$stmt->bind_param("i", $account['id']);
$stmt->execute();
$totalDonationsResult = $stmt->get_result();
$totalDonated = $totalDonationsResult->fetch_assoc()['total_donated'] ?? 0;

// Get pending web rewards
$webRewardsQuery = "
    SELECT 
        pwr.item_id,
        pwr.item_count,
        pwr.added,
        pwr.order_id
    FROM player_web_rewards pwr
    JOIN players p ON pwr.player_id = p.id
    WHERE p.account_id = ? AND pwr.received IS NULL
    ORDER BY pwr.added DESC
";

$stmt = $conn->prepare($webRewardsQuery);
$stmt->bind_param("i", $account['id']);
$stmt->execute();
$webRewardsResult = $stmt->get_result();

// Get account stamps (daily login rewards)
$stampsQuery = "
    SELECT stamps, last_stamp
    FROM account_stamps
    WHERE account_id = ?
";

$stmt = $conn->prepare($stampsQuery);
$stmt->bind_param("i", $account['id']);
$stmt->execute();
$stampsResult = $stmt->get_result();
$stamps = $stampsResult->fetch_assoc();

// Get account might points (create record if it doesn't exist)
$mightQuery = "SELECT might FROM might WHERE account_id = ?";
$stmt = $conn->prepare($mightQuery);
$stmt->bind_param("i", $account['id']);
$stmt->execute();
$mightResult = $stmt->get_result();

if ($mightResult->num_rows > 0) {
    $mightData = $mightResult->fetch_assoc();
    $userMight = $mightData['might'];
} else {
    // Create might record with 0 points if it doesn't exist
    $insertMightQuery = "INSERT INTO might (account_id, might) VALUES (?, 0)";
    $stmt = $conn->prepare($insertMightQuery);
    $stmt->bind_param("i", $account['id']);
    $stmt->execute();
    $userMight = 0;
}

// Create balance array for compatibility with existing display code
$balances = [];
if ($userMight > 0) {
    $balances[] = [
        'value' => $userMight,
        'price_name' => 'Might Points',
        'symbolic' => 'MP'
    ];
}

// Helper functions
function calculateLevel($exp) {
    if ($exp < 1000) return 1;
    if ($exp < 5000) return 10;
    if ($exp < 50000) return 20;
    if ($exp < 200000) return 30;
    if ($exp < 500000) return 40;
    if ($exp < 1000000) return 50;
    if ($exp < 2000000) return 60;
    return 65;
}

function formatTime($seconds) {
    $hours = floor($seconds / 3600);
    $minutes = floor(($seconds % 3600) / 60);
    return "{$hours}h {$minutes}m";
}

function timeAgo($datetime) {
    $time = time() - strtotime($datetime);
    if ($time < 60) return 'just now';
    if ($time < 3600) return floor($time/60) . ' minutes ago';
    if ($time < 86400) return floor($time/3600) . ' hours ago';
    if ($time < 2592000) return floor($time/86400) . ' days ago';
    return date('M j, Y', strtotime($datetime));
}

// Calculate membership benefits
$membershipLevel = $account['membership'];
$membershipName = ['Free Player', 'Bronze Member', 'Silver Member', 'Gold Member', 'Platinum Member'][$membershipLevel] ?? 'Free Player';

$conn->close();
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Player Dashboard - Aion-Blitz Server</title>
    <link rel="stylesheet" href="styles.css">
    <link href="https://fonts.googleapis.com/css2?family=Cinzel:wght@400;600;700&family=Open+Sans:wght@300;400;600&display=swap" rel="stylesheet">
    <!-- PayPal SDK -->
    <script src="https://www.paypal.com/sdk/js?client-id=YOUR_PAYPAL_CLIENT_ID&currency=EUR"></script>
</head>
<body>
    <!-- Video Background -->
    <div class="video-background">
        <video autoplay muted loop playsinline>
            <source src="images/bg_main.mp4" type="video/mp4">
        </video>
    </div>
    <div class="video-overlay"></div>

    <!-- Top Navigation -->
    <nav class="top-nav">
        <div class="nav-left">
            <span class="server-time">🕐 Server Time: 00:00</span>
            <span class="server-status">🟢 Server Status</span>
        </div>
        <div class="nav-center">
            <ul class="nav-menu">
                <li><a href="index.php">🏠 Home</a></li>
                <li><a href="index.php#faq">❓ F.A.Q</a></li>
                <li><a href="register.php">📝 Register</a></li>
                <li><a href="index.php#schedule">📅 Schedule</a></li>
                <li><a href="ranks.php">🏆 Ranks</a></li>
                <li><a href="activity.php">📊 Activity</a></li>
                <li><a href="pvp.php">⚔️ PvP</a></li>
                <li><a href="dashboard.php" class="active">👤 Dashboard</a></li>
                <li><a href="index.php#discord">💬 Discord</a></li>
            </ul>
        </div>
        <div class="nav-right">
            <div class="user-section">
                <span class="welcome-user">Welcome, <?php echo htmlspecialchars($account['name']); ?>!</span>
                <a href="logout.php" class="logout-btn">Logout</a>
            </div>
        </div>
    </nav>

    <!-- Dashboard Section -->
    <section class="dashboard-section">
        <div class="dashboard-container">
            <div class="dashboard-header">
                <h1>👤 Player Dashboard</h1>
                <p class="dashboard-subtitle">Welcome back, <?php echo htmlspecialchars($account['name']); ?>!</p>
            </div>

            <!-- Account Overview -->
            <div class="dashboard-grid">
                <!-- Account Info Card -->
                <div class="dashboard-card account-info">
                    <div class="card-header">
                        <h3>👤 Account Information</h3>
                        <span class="membership-badge <?php echo strtolower(str_replace(' ', '-', $membershipName)); ?>">
                            <?php echo $membershipName; ?>
                        </span>
                    </div>
                    <div class="card-content">
                        <div class="info-row">
                            <span class="info-label">Account Name:</span>
                            <span class="info-value"><?php echo htmlspecialchars($account['name']); ?></span>
                        </div>
                        <div class="info-row">
                            <span class="info-label">Member Since:</span>
                            <span class="info-value"><?php echo date('M j, Y', strtotime($account['creation_date'])); ?></span>
                        </div>
                        <div class="info-row">
                            <span class="info-label">Total Playtime:</span>
                            <span class="info-value"><?php echo formatTime($account['accumulated_online'] ?? 0); ?></span>
                        </div>
                        <div class="info-row">
                            <span class="info-label">Last Active:</span>
                            <span class="info-value"><?php echo $account['last_active'] ? timeAgo($account['last_active']) : 'Never'; ?></span>
                        </div>
                        <div class="info-row">
                            <span class="info-label">Access Level:</span>
                            <span class="info-value access-level-<?php echo $account['access_level']; ?>">
                                <?php 
                                $accessLevels = ['Player', 'VIP', 'Moderator', 'Admin', 'Owner'];
                                echo $accessLevels[$account['access_level']] ?? 'Player';
                                ?>
                            </span>
                        </div>
                    </div>
                </div>

                <!-- Characters Card -->
                <div class="dashboard-card characters-card">
                    <div class="card-header">
                        <h3>⚔️ Your Characters</h3>
                        <span class="card-badge"><?php echo $playersResult->num_rows; ?> Characters</span>
                    </div>
                    <div class="card-content">
                        <div class="characters-list">
                            <?php
                            if ($playersResult->num_rows > 0) {
                                while ($player = $playersResult->fetch_assoc()) {
                                    $level = calculateLevel($player['exp']);
                                    $onlineStatus = $player['online'] ? 'online' : 'offline';
                                    echo "<div class='character-item'>";
                                    echo "<div class='character-info'>";
                                    echo "<div class='character-name'>";
                                    echo "<a href='player.php?name=" . urlencode($player['name']) . "'>" . htmlspecialchars($player['name']) . "</a>";
                                    echo "<span class='character-status $onlineStatus'>" . ($player['online'] ? 'Online' : 'Offline') . "</span>";
                                    echo "</div>";
                                    echo "<div class='character-details'>Lv.$level " . $player['player_class'] . " • " . $player['race'] . "</div>";
                                    echo "</div>";
                                    echo "<div class='character-stats'>";
                                    echo "<span class='stat'>GP: " . number_format($player['gp'] ?? 0) . "</span>";
                                    echo "<span class='stat'>AP: " . number_format($player['ap'] ?? 0) . "</span>";
                                    echo "</div>";
                                    echo "</div>";
                                }
                            } else {
                                echo "<div class='no-characters'>No characters found. Create your first character in-game!</div>";
                            }
                            ?>
                        </div>
                    </div>
                </div>

                <!-- Account Balance Card -->
                <div class="dashboard-card account-balance">
                    <div class="card-header">
                        <h3>💰 Account Balance</h3>
                        <span class="card-badge">Points</span>
                    </div>
                    <div class="card-content">
                        <?php if (!empty($balances)): ?>
                            <?php foreach ($balances as $balance): ?>
                                <div class="balance-item">
                                    <span class="balance-type"><?php echo htmlspecialchars($balance['price_name']); ?></span>
                                    <span class="balance-amount"><?php echo number_format($balance['value']); ?> <?php echo htmlspecialchars($balance['symbolic']); ?></span>
                                </div>
                            <?php endforeach; ?>
                        <?php else: ?>
                            <div class="balance-item">
                                <span class="balance-type">Might Points</span>
                                <span class="balance-amount">0 MP</span>
                            </div>
                        <?php endif; ?>
                        <div class="balance-info">
                            <small>💡 Earn Might Points by playing the game and collecting might!</small>
                            <br><br>
                            <a href="shop.php" class="btn-primary" style="text-decoration: none; padding: 10px 20px; border-radius: 6px; display: inline-block;">
                                🛒 Visit Shop
                            </a>
                        </div>
                    </div>
                </div>

                <!-- Daily Rewards Card -->
                <div class="dashboard-card daily-rewards">
                    <div class="card-header">
                        <h3>🎁 Daily Login Rewards</h3>
                        <span class="card-badge"><?php echo $stamps['stamps'] ?? 0; ?> Days</span>
                    </div>
                    <div class="card-content">
                        <div class="stamps-progress">
                            <?php
                            $currentStamps = $stamps['stamps'] ?? 0;
                            $maxStamps = 30;
                            for ($i = 1; $i <= $maxStamps; $i++) {
                                $class = $i <= $currentStamps ? 'collected' : 'pending';
                                if ($i % 7 == 0) $class .= ' weekly-bonus';
                                echo "<div class='stamp $class'>$i</div>";
                                if ($i % 10 == 0 && $i < $maxStamps) echo "<div class='stamp-break'></div>";
                            }
                            ?>
                        </div>
                        <div class="next-reward">
                            <?php
                            $lastStamp = $stamps['last_stamp'] ?? null;
                            $canClaim = !$lastStamp || (time() - strtotime($lastStamp)) >= 86400;
                            if ($canClaim) {
                                echo "<button class='claim-btn' onclick='claimDailyReward()'>🎁 Claim Daily Reward</button>";
                            } else {
                                $nextClaim = strtotime($lastStamp) + 86400;
                                $timeLeft = $nextClaim - time();
                                $hoursLeft = floor($timeLeft / 3600);
                                $minutesLeft = floor(($timeLeft % 3600) / 60);
                                echo "<div class='next-claim'>Next reward in: {$hoursLeft}h {$minutesLeft}m</div>";
                            }
                            ?>
                        </div>
                    </div>
                </div>

                <!-- Support Server Card -->
                <div class="dashboard-card donation-card">
                    <div class="card-header">
                        <h3>💝 Support Aion-Blitz</h3>
                        <span class="card-badge">$<?php echo number_format($totalDonated, 2); ?> Total</span>
                    </div>
                    <div class="card-content">
                        <div class="donation-info">
                            <p>Help keep Aion-Blitz running and get exclusive rewards!</p>
                            <div class="donation-benefits">
                                <div class="benefit-item">✨ Premium membership status</div>
                                <div class="benefit-item">🎁 Exclusive in-game items</div>
                                <div class="benefit-item">⚡ Priority support</div>
                                <div class="benefit-item">🏆 Donor recognition</div>
                            </div>
                        </div>
                        
                        <div class="donation-amounts">
                            <button class="donation-btn" onclick="donate(5)">€5 - Bronze (5 MP)</button>
                            <button class="donation-btn" onclick="donate(10)">€10 - Silver (10 MP)</button>
                            <button class="donation-btn" onclick="donate(25)">€25 - Gold (25 MP)</button>
                            <button class="donation-btn" onclick="donate(50)">€50 - Platinum (50 MP)</button>
                        </div>
                        
                        <!-- PayPal Button Container -->
                        <div id="paypal-button-container" style="display: none;"></div>
                        
                        <div class="custom-amount">
                            <input type="number" id="custom-amount" placeholder="Custom amount" min="1" max="500">
                            <button onclick="donateCustom()">Donate Custom</button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <script src="script.js"></script>
    <script>
        let selectedAmount = 0;
        
        function donate(amount) {
            selectedAmount = amount;
            showPayPalButton();
        }
        
        function donateCustom() {
            const customAmount = document.getElementById('custom-amount').value;
            if (customAmount && customAmount > 0) {
                selectedAmount = parseFloat(customAmount);
                showPayPalButton();
            }
        }
        
        function showPayPalButton() {
            document.getElementById('paypal-button-container').style.display = 'block';
            
            // Clear existing PayPal button
            document.getElementById('paypal-button-container').innerHTML = '';
            
            paypal.Buttons({
                createOrder: function(data, actions) {
                    return actions.order.create({
                        purchase_units: [{
                            amount: {
                                value: selectedAmount.toString()
                            },
                            description: 'Aion-Blitz Server Donation'
                        }]
                    });
                },
                onApprove: function(data, actions) {
                    return actions.order.capture().then(function(details) {
                        // Send donation data to server
                        fetch('process_donation.php', {
                            method: 'POST',
                            headers: {
                                'Content-Type': 'application/json',
                            },
                            body: JSON.stringify({
                                order_id: data.orderID,
                                amount: selectedAmount,
                                payer_name: details.payer.name.given_name + ' ' + details.payer.name.surname,
                                payer_email: details.payer.email_address
                            })
                        })
                        .then(response => response.json())
                        .then(data => {
                            if (data.success) {
                                alert('Thank you for your donation! Your rewards will be processed shortly.');
                                location.reload();
                            } else {
                                alert('There was an error processing your donation. Please contact support.');
                            }
                        });
                    });
                },
                onError: function(err) {
                    console.error('PayPal error:', err);
                    alert('There was an error with PayPal. Please try again.');
                }
            }).render('#paypal-button-container');
        }
        
        function claimDailyReward() {
            fetch('claim_daily_reward.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                }
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    alert('Daily reward claimed! Check your in-game mail.');
                    location.reload();
                } else {
                    alert(data.message || 'Unable to claim reward at this time.');
                }
            });
        }
    </script>
</body>
</html>
