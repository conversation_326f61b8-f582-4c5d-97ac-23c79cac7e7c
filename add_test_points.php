<?php
// Add test points to your account for testing the shop
require_once "db.php";

echo "Adding test points...\n";

// Get your account ID (replace 'your_username' with your actual username)
$username = 'test'; // Change this to your username
$accountQuery = "SELECT id, name FROM account_data WHERE name = ?";
$stmt = $conn->prepare($accountQuery);
$stmt->bind_param("s", $username);
$stmt->execute();
$result = $stmt->get_result();
$account = $result->fetch_assoc();

if (!$account) {
    echo "✗ Account '$username' not found. Please change the username in the script.\n";
    exit;
}

$userId = $account['id'];
$accountName = $account['name'];
$testPoints = 1000; // Give 1000 test points
$priceId = 1; // Donation Points currency

// Check if account balance exists
$balanceCheckQuery = "SELECT value FROM account_balance WHERE account_id = ? AND price_id = ?";
$stmt = $conn->prepare($balanceCheckQuery);
$stmt->bind_param("ii", $userId, $priceId);
$stmt->execute();
$balanceResult = $stmt->get_result();

if ($balanceResult->num_rows > 0) {
    // Update existing balance
    $updateBalanceQuery = "UPDATE account_balance SET value = value + ? WHERE account_id = ? AND price_id = ?";
    $stmt = $conn->prepare($updateBalanceQuery);
    $stmt->bind_param("iii", $testPoints, $userId, $priceId);
    $stmt->execute();
    echo "✓ Added $testPoints points to existing balance\n";
} else {
    // Insert new balance record
    $insertBalanceQuery = "INSERT INTO account_balance (account_id, account_name, price_id, value) VALUES (?, ?, ?, ?)";
    $stmt = $conn->prepare($insertBalanceQuery);
    $stmt->bind_param("isii", $userId, $accountName, $priceId, $testPoints);
    $stmt->execute();
    echo "✓ Created new balance with $testPoints points\n";
}

// Log the transaction
$historyQuery = "INSERT INTO balance_history (date, reference_id, account_id, account_name, price_id, amount, description, type) VALUES (NOW(), 'TEST_POINTS', ?, ?, ?, ?, 'Test Points for Shop Testing', 'IN')";
$stmt = $conn->prepare($historyQuery);
$stmt->bind_param("isii", $userId, $accountName, $priceId, $testPoints);
$stmt->execute();

echo "✓ Test points added successfully!\n";
echo "Account: $accountName now has test points to use in the shop.\n";

$conn->close();
?>
