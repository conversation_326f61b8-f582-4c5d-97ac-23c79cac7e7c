﻿

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Sign In - Aion-Blitz Server</title>
    <link rel="stylesheet" href="styles.css">
    <link href="https://fonts.googleapis.com/css2?family=Cinzel:wght@400;600;700&family=Open+Sans:wght@300;400;600&display=swap" rel="stylesheet">
</head>
<body>
    <!-- Video Background -->
    <div class="video-background">
        <video autoplay muted loop playsinline>
            <source src="images/bg_main.mp4" type="video/mp4">
        </video>
    </div>
    <div class="video-overlay"></div>

    <!-- Top Navigation -->
    <nav class="top-nav">
        <div class="nav-left">
            <span class="server-time">ðŸ• Server Time: 00:00</span>
            <span class="server-status">ðŸŸ¢ Server Status</span>
        </div>
        <div class="nav-center">
            <ul class="nav-menu">
                <li><a href="index.php">ðŸ  Home</a></li>
                <li><a href="index.php#faq">â“ F.A.Q</a></li>
                <li><a href="register.php">ðŸ“ Register</a></li>
                <li><a href="index.php#schedule">ðŸ“… Schedule</a></li>
                <li><a href="ranks.php">ðŸ† Ranks</a></li>
                <li><a href="activity.php">ðŸ“Š Activity</a></li>
                <li><a href="pvp.php">âš”ï¸ PvP</a></li>
                <li><a href="index.php#discord">ðŸ’¬ Discord</a></li>
            </ul>
        </div>
        <div class="nav-right">
            <div class="user-section">
                <a href="signin.php" class="signin-btn active">Sign In</a>
            </div>
        </div>
    </nav>
    <div class="signin-container">
        <div class="register-header">
            <h1>Welcome Back</h1>
            <p class="register-subtitle">Sign in to your Aion-Blitz account</p>
        </div>

        <?php
        session_start();
        require_once "db.php";

        // Check if the user is already signed in
        if (isset($_SESSION["username"])) {
            // User is already signed in, redirect to index.php
            header("Location: index.php");
            exit;
        }

        $errorMessage = "";

        // Handle the form submission
        if ($_SERVER["REQUEST_METHOD"] == "POST") {
            // Validate the submitted form data (e.g., username and password)
            $username = $_POST["username"];
            $password = $_POST["password"];

            // Hash the password using the same method as during registration
            $hashedPassword = encryptPassword($password);

            // Perform database query to check the credentials
            $query = "SELECT * FROM account_data WHERE name='$username' AND password='$hashedPassword'";
            $result = $conn->query($query);

            if ($result->num_rows == 1) {
                // User found in the database, set session variable and redirect to index.php
                $_SESSION["username"] = $username;
                header("Location: index.php");
                exit;
            } else {
                // Invalid credentials, display error message
                $errorMessage = "Invalid username or password. Please try again.";
            }
        }

        if (isset($conn)) $conn->close();

        // Function to hash the password
        function encryptPassword($password) {
            $digest = sha1($password, true);
            $encodedDigest = base64_encode($digest);
            return $encodedDigest;
        }
        ?>

        <?php if (!empty($errorMessage)) : ?>
            <div class="message-container error">
                <p><?php echo $errorMessage; ?></p>
            </div>
        <?php endif; ?>

        <form method="POST" action="signin.php" class="register-form">
            <div class="form-group">
                <label for="username">Username</label>
                <input type="text" name="username" id="username" placeholder="Enter your username" required>
            </div>

            <div class="form-group">
                <label for="password">Password</label>
                <input type="password" name="password" id="password" placeholder="Enter your password" required>
            </div>

            <button type="submit" class="register-btn">
                <span>Sign In</span>
            </button>
        </form>

        <div class="register-footer">
            <p>Don't have an account? <a href="register.php" class="signin-link">Register Here</a></p>
        </div>
    </div>

    <script src="script.js"></script>
</body>
</html>
