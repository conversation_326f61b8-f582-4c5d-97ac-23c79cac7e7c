<?php
require_once "db.php";

echo "=== SETTING UP MIGHT POINTS PRICE TYPE ===\n";

// Check if price_type table exists
$result = $conn->query("SHOW TABLES LIKE 'price_type'");
if ($result->num_rows == 0) {
    echo "Creating price_type table...\n";
    $createPriceTypeQuery = "
        CREATE TABLE price_type (
            price_id int(11) NOT NULL AUTO_INCREMENT,
            price_name varchar(50) NOT NULL,
            symbolic varchar(10) NOT NULL,
            PRIMARY KEY (price_id)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8
    ";
    
    if ($conn->query($createPriceTypeQuery) === TRUE) {
        echo "✓ price_type table created successfully!\n";
    } else {
        die("✗ Error creating price_type table: " . $conn->error . "\n");
    }
}

// Check current price types
echo "\nCurrent price types:\n";
$result = $conn->query("SELECT * FROM price_type");
if ($result && $result->num_rows > 0) {
    while ($row = $result->fetch_assoc()) {
        echo "- ID: {$row['price_id']}, Name: {$row['price_name']}, Symbol: {$row['symbolic']}\n";
    }
} else {
    echo "No price types found\n";
}

// Check if Might Points already exists
$checkMightQuery = "SELECT * FROM price_type WHERE price_name = 'Might Points' OR symbolic = 'MP'";
$result = $conn->query($checkMightQuery);

if ($result->num_rows > 0) {
    echo "\n✓ Might Points price type already exists!\n";
    $mightType = $result->fetch_assoc();
    echo "Might Points ID: {$mightType['price_id']}\n";
} else {
    echo "\nAdding Might Points price type...\n";
    $insertMightQuery = "INSERT INTO price_type (price_name, symbolic) VALUES ('Might Points', 'MP')";
    if ($conn->query($insertMightQuery) === TRUE) {
        echo "✓ Might Points price type added successfully!\n";
        $mightPriceId = $conn->insert_id;
        echo "Might Points ID: $mightPriceId\n";
    } else {
        echo "✗ Error adding Might Points: " . $conn->error . "\n";
    }
}

echo "\nFinal price types:\n";
$result = $conn->query("SELECT * FROM price_type ORDER BY price_id");
if ($result && $result->num_rows > 0) {
    while ($row = $result->fetch_assoc()) {
        echo "- ID: {$row['price_id']}, Name: {$row['price_name']}, Symbol: {$row['symbolic']}\n";
    }
}

$conn->close();
?>
