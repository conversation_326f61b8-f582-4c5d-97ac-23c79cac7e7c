<?php
echo "Debugging DP balance issue...\n";

require_once "db.php";

// Test database connection
if ($conn->connect_error) {
    die("Connection failed: " . $conn->connect_error);
}

echo "✓ Database connection successful!\n\n";

// Check what username you're logged in as (simulate session)
$username = "admin"; // Change this to the username you're using to login
echo "Checking balance for username: $username\n";

// Get account info (same query as shop.php)
$accountQuery = "SELECT id, name, membership FROM account_data WHERE name = ?";
$stmt = $conn->prepare($accountQuery);
$stmt->bind_param("s", $username);
$stmt->execute();
$accountResult = $stmt->get_result();
$account = $accountResult->fetch_assoc();

if (!$account) {
    echo "✗ Account '$username' not found!\n";
    exit;
}

echo "✓ Found account: {$account['name']} (ID: {$account['id']})\n";

// Check balance using the same query as shop.php
$balanceQuery = "
    SELECT ab.value, pt.price_name, pt.symbolic
    FROM account_balance ab
    JOIN price_type pt ON ab.price_id = pt.price_id
    WHERE ab.account_id = ? AND pt.price_id = 1
";

$stmt = $conn->prepare($balanceQuery);
$stmt->bind_param("i", $account['id']);
$stmt->execute();
$balanceResult = $stmt->get_result();

if ($balanceResult->num_rows > 0) {
    $balance = $balanceResult->fetch_assoc();
    echo "✓ Balance found: {$balance['value']} {$balance['symbolic']}\n";
    echo "  - Price Name: {$balance['price_name']}\n";
    echo "  - Symbolic: {$balance['symbolic']}\n";
} else {
    echo "✗ No balance found with the shop query!\n";
    
    // Let's check what's actually in the tables
    echo "\nDebugging tables...\n";
    
    // Check account_balance table
    echo "account_balance table contents:\n";
    $allBalances = $conn->query("SELECT * FROM account_balance");
    if ($allBalances && $allBalances->num_rows > 0) {
        while ($row = $allBalances->fetch_assoc()) {
            echo "  Account ID: {$row['account_id']}, Price ID: {$row['price_id']}, Value: {$row['value']}\n";
        }
    } else {
        echo "  No records in account_balance table\n";
    }
    
    // Check price_type table
    echo "\nprice_type table contents:\n";
    $allPrices = $conn->query("SELECT * FROM price_type");
    if ($allPrices && $allPrices->num_rows > 0) {
        while ($row = $allPrices->fetch_assoc()) {
            echo "  Price ID: {$row['price_id']}, Name: {$row['price_name']}, Symbol: {$row['symbolic']}\n";
        }
    } else {
        echo "  No records in price_type table\n";
    }
    
    // Try a simpler balance query
    echo "\nTrying simple balance query...\n";
    $simpleQuery = "SELECT value FROM account_balance WHERE account_id = ?";
    $stmt = $conn->prepare($simpleQuery);
    $stmt->bind_param("i", $account['id']);
    $stmt->execute();
    $simpleResult = $stmt->get_result();
    
    if ($simpleResult->num_rows > 0) {
        $simpleBalance = $simpleResult->fetch_assoc();
        echo "✓ Simple query found balance: {$simpleBalance['value']}\n";
    } else {
        echo "✗ Even simple query found no balance\n";
    }
}

$conn->close();
?>
