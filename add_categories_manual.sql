-- Manual SQL script to add category system to shop
-- Run this in phpMyAdmin or MySQL command line if the PHP setup script doesn't work

-- 1. Create shop_categories table
CREATE TABLE IF NOT EXISTS `shop_categories` (
    `id` INT AUTO_INCREMENT PRIMARY KEY,
    `name` VARCHAR(50) NOT NULL,
    `description` TEXT,
    `icon` VARCHAR(10) DEFAULT '📦',
    `sort_order` INT DEFAULT 0,
    `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 2. Insert default categories
INSERT INTO `shop_categories` (`name`, `description`, `icon`, `sort_order`) VALUES
('Items', 'General items and equipment', '⚔️', 1),
('Skins', 'Cosmetic skins and appearances', '👗', 2),
('Consumables', 'Potions, food, and consumable items', '🧪', 3)
ON DUPLICATE KEY UPDATE `name` = VALUES(`name`);

-- 3. Add category_id column to shop_items table (if it doesn't exist)
ALTER TABLE `shop_items` 
ADD COLUMN IF NOT EXISTS `category_id` INT DEFAULT 1;

-- 4. Update existing items to have category_id = 1 (Items) if they're NULL
UPDATE `shop_items` SET `category_id` = 1 WHERE `category_id` IS NULL OR `category_id` = 0;

-- 5. Add foreign key constraint (optional, for data integrity)
-- ALTER TABLE `shop_items` 
-- ADD CONSTRAINT `fk_shop_items_category` 
-- FOREIGN KEY (`category_id`) REFERENCES `shop_categories`(`id`) 
-- ON DELETE SET NULL ON UPDATE CASCADE;

-- Check results
SELECT 'Categories created:' as status;
SELECT * FROM `shop_categories`;

SELECT 'Shop items with categories:' as status;
SELECT `id`, `name`, `category_id` FROM `shop_items` LIMIT 10;
