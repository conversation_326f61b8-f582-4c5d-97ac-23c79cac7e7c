<?php
// Debug image path issues
echo "Debugging Image Path Issues\n";
echo "===========================\n";

// Check if images directory exists
$dirs_to_check = [
    'images',
    'images/items',
    './images',
    './images/items'
];

foreach ($dirs_to_check as $dir) {
    if (is_dir($dir)) {
        echo "✓ Directory exists: $dir\n";
    } else {
        echo "✗ Directory missing: $dir\n";
    }
}

echo "\n";

// Check files in images/items
$image_dir = 'images/items/';
if (is_dir($image_dir)) {
    echo "Files in $image_dir:\n";
    $files = scandir($image_dir);
    foreach ($files as $file) {
        if ($file !== '.' && $file !== '..') {
            $full_path = $image_dir . $file;
            $size = filesize($full_path);
            echo "- $file (" . number_format($size) . " bytes)\n";
        }
    }
} else {
    echo "Directory $image_dir does not exist\n";
}

echo "\n";

// Test web accessibility
$test_urls = [
    '/images/items/default.svg',
    'images/items/default.svg',
    './images/items/default.svg'
];

echo "Testing web accessibility:\n";
foreach ($test_urls as $url) {
    $full_path = $_SERVER['DOCUMENT_ROOT'] . '/aion-blitz' . $url;
    if (file_exists($full_path)) {
        echo "✓ Accessible: $url (file exists at $full_path)\n";
    } else {
        echo "✗ Not accessible: $url (file not found at $full_path)\n";
    }
}

echo "\n";

// Check current working directory
echo "Current working directory: " . getcwd() . "\n";
echo "Document root: " . $_SERVER['DOCUMENT_ROOT'] . "\n";

// Check if we can create a simple test image
$test_image_content = '<svg width="50" height="50" xmlns="http://www.w3.org/2000/svg"><rect width="50" height="50" fill="#333"/><text x="25" y="30" text-anchor="middle" fill="#d4af37" font-size="8">TEST</text></svg>';
$test_file = 'images/items/test.svg';

if (file_put_contents($test_file, $test_image_content)) {
    echo "✓ Created test image: $test_file\n";
} else {
    echo "✗ Failed to create test image: $test_file\n";
}

echo "\n✓ Debug completed\n";
?>
