<?php
require_once "db.php";

// Create database connection
$conn = new mysqli($servername, $username, $password, $dbname);
if ($conn->connect_error) {
    die("Connection failed: " . $conn->connect_error);
}

// Get ranking type from URL parameter (default to GP)
$rankType = isset($_GET['type']) ? $_GET['type'] : 'gp';
$limit = isset($_GET['limit']) ? (int)$_GET['limit'] : 50;

// Validate ranking type
if (!in_array($rankType, ['gp', 'ap', 'kills'])) {
    $rankType = 'gp';
}

// Build the appropriate query based on ranking type
switch ($rankType) {
    case 'ap':
        $orderBy = 'ar.ap DESC';
        $scoreColumn = 'ar.ap';
        $scoreLabel = 'Abyss Points';
        $pageTitle = 'Abyss Points Rankings';
        $icon = '⚔️';
        break;
    case 'kills':
        $orderBy = 'ar.all_kill DESC';
        $scoreColumn = 'ar.all_kill';
        $scoreLabel = 'Total Kills';
        $pageTitle = 'PvP Kill Rankings';
        $icon = '💀';
        break;
    default: // gp
        $orderBy = 'ar.gp DESC';
        $scoreColumn = 'ar.gp';
        $scoreLabel = 'Glory Points';
        $pageTitle = 'Glory Points Rankings';
        $icon = '👑';
        break;
}

// Query to get player rankings
$query = "
    SELECT 
        p.name as player_name,
        p.player_class,
        p.race,
        p.exp,
        ar.gp,
        ar.ap,
        ar.all_kill,
        ar.rank as abyss_rank,
        ar.rank_pos,
        lm.nickname as legion_name,
        $scoreColumn as score
    FROM players p
    LEFT JOIN abyss_rank ar ON p.id = ar.player_id
    LEFT JOIN legion_members lm ON p.id = lm.player_id
    WHERE ar.player_id IS NOT NULL
    ORDER BY $orderBy
    LIMIT $limit
";

$result = $conn->query($query);

// Function to calculate level from experience
function calculateLevel($exp) {
    // Simplified level calculation - you may need to adjust this based on your server's exp table
    if ($exp < 1000) return 1;
    if ($exp < 5000) return 10;
    if ($exp < 50000) return 20;
    if ($exp < 200000) return 30;
    if ($exp < 500000) return 40;
    if ($exp < 1000000) return 50;
    if ($exp < 2000000) return 60;
    return 65; // Max level
}

// Function to get rank name from rank number
function getRankName($rank) {
    $ranks = [
        1 => 'Soldier',
        2 => 'Soldier 1st Class',
        3 => 'Senior Soldier',
        4 => 'Corporal',
        5 => 'Sergeant',
        6 => 'Master Sergeant',
        7 => 'Warrant Officer',
        8 => 'Lieutenant',
        9 => 'Captain',
        10 => 'Major',
        11 => 'Lieutenant Colonel',
        12 => 'Colonel',
        13 => 'Brigadier General',
        14 => 'Major General',
        15 => 'Lieutenant General',
        16 => 'General',
        17 => 'Great General',
        18 => 'Commander'
    ];
    return isset($ranks[$rank]) ? $ranks[$rank] : 'Soldier';
}

// Function to format numbers
function formatNumber($number) {
    if ($number >= 1000000) {
        return number_format($number / 1000000, 1) . 'M';
    } elseif ($number >= 1000) {
        return number_format($number / 1000, 1) . 'K';
    }
    return number_format($number);
}

$conn->close();
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $pageTitle; ?> - Aion-Blitz Server</title>
    <link rel="stylesheet" href="styles.css">
    <link href="https://fonts.googleapis.com/css2?family=Cinzel:wght@400;600;700&family=Open+Sans:wght@300;400;600&display=swap" rel="stylesheet">
</head>
<body>
    <!-- Video Background -->
    <div class="video-background">
        <video autoplay muted loop playsinline>
            <source src="images/bg_main.mp4" type="video/mp4">
        </video>
    </div>
    <div class="video-overlay"></div>

    <!-- Top Navigation -->
    <nav class="top-nav">
        <div class="nav-left">
            <span class="server-time">🕐 Server Time: 00:00</span>
            <span class="server-status">🟢 Server Status</span>
        </div>
        <div class="nav-center">
            <ul class="nav-menu">
                <li><a href="index.php">🏠 Home</a></li>
                <li><a href="index.php#faq">❓ F.A.Q</a></li>
                <li><a href="register.php">📝 Register</a></li>
                <li><a href="index.php#schedule">📅 Schedule</a></li>
                <li><a href="ranks.php" class="active">🏆 Ranks</a></li>
                <li><a href="activity.php">📊 Activity</a></li>
                <li><a href="pvp.php">⚔️ PvP</a></li>
                <?php if (isset($_SESSION["username"])): ?>
                <li><a href="dashboard.php">👤 Dashboard</a></li>
                <?php endif; ?>
                <li><a href="index.php#discord">💬 Discord</a></li>
            </ul>
        </div>
        <div class="nav-right">
            <div class="user-section">
                <?php
                session_start();
                if (isset($_SESSION["username"])) {
                    echo "<span class='welcome-user'>Welcome, " . $_SESSION["username"] . "!</span>";
                    echo "<a href='logout.php' class='logout-btn'>Logout</a>";
                } else {
                    echo "<a href='signin.php' class='signin-btn'>Sign In</a>";
                }
                ?>
            </div>
        </div>
    </nav>

    <!-- Rankings Section -->
    <section class="rankings-section">
        <div class="rankings-container">
            <div class="rankings-header">
                <h1><?php echo $icon . ' ' . $pageTitle; ?></h1>
                <p class="rankings-subtitle">Top players on Aion-Blitz Server</p>
            </div>

            <!-- Ranking Type Selector -->
            <div class="ranking-tabs">
                <a href="ranks.php?type=gp" class="ranking-tab <?php echo $rankType === 'gp' ? 'active' : ''; ?>">
                    👑 Glory Points
                </a>
                <a href="ranks.php?type=ap" class="ranking-tab <?php echo $rankType === 'ap' ? 'active' : ''; ?>">
                    ⚔️ Abyss Points
                </a>
                <a href="ranks.php?type=kills" class="ranking-tab <?php echo $rankType === 'kills' ? 'active' : ''; ?>">
                    💀 PvP Kills
                </a>
            </div>

            <!-- Rankings Table -->
            <div class="rankings-table-container">
                <table class="rankings-table">
                    <thead>
                        <tr>
                            <th>Rank</th>
                            <th>Player</th>
                            <th>Class</th>
                            <th>Race</th>
                            <th>Level</th>
                            <th>Legion</th>
                            <th>Abyss Rank</th>
                            <th><?php echo $scoreLabel; ?></th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php
                        if ($result && $result->num_rows > 0) {
                            $position = 1;
                            while ($row = $result->fetch_assoc()) {
                                $level = calculateLevel($row['exp']);
                                $rankName = getRankName($row['abyss_rank']);
                                $score = formatNumber($row['score']);
                                $legion = $row['legion_name'] ? $row['legion_name'] : 'None';
                                
                                // Add special styling for top 3
                                $rowClass = '';
                                if ($position == 1) $rowClass = 'rank-first';
                                elseif ($position == 2) $rowClass = 'rank-second';
                                elseif ($position == 3) $rowClass = 'rank-third';
                                
                                echo "<tr class='$rowClass'>";
                                echo "<td class='rank-position'>";
                                if ($position <= 3) {
                                    $medals = ['🥇', '🥈', '🥉'];
                                    echo $medals[$position - 1];
                                } else {
                                    echo $position;
                                }
                                echo "</td>";
                                echo "<td class='player-name'><a href='player.php?name=" . urlencode($row['player_name']) . "'>" . htmlspecialchars($row['player_name']) . "</a></td>";
                                echo "<td class='player-class'>" . htmlspecialchars($row['player_class']) . "</td>";
                                echo "<td class='player-race'>" . htmlspecialchars($row['race']) . "</td>";
                                echo "<td class='player-level'>$level</td>";
                                echo "<td class='player-legion'>" . htmlspecialchars($legion) . "</td>";
                                echo "<td class='abyss-rank'>$rankName</td>";
                                echo "<td class='score'>$score</td>";
                                echo "</tr>";
                                $position++;
                            }
                        } else {
                            echo "<tr><td colspan='8' class='no-data'>No ranking data available</td></tr>";
                        }
                        ?>
                    </tbody>
                </table>
            </div>

            <!-- Ranking Info -->
            <div class="ranking-info">
                <div class="info-card">
                    <h3>📊 Ranking Information</h3>
                    <ul>
                        <li><strong>Glory Points (GP):</strong> Earned through PvP activities and fortress sieges</li>
                        <li><strong>Abyss Points (AP):</strong> Gained by defeating enemy players and completing Abyss quests</li>
                        <li><strong>PvP Kills:</strong> Total number of enemy players defeated</li>
                        <li><strong>Rankings update:</strong> Every 30 minutes</li>
                    </ul>
                </div>
                <div class="info-card">
                    <h3>🏆 Rewards & Benefits</h3>
                    <ul>
                        <li>Top 10 players receive weekly rewards</li>
                        <li>Higher ranks unlock exclusive titles</li>
                        <li>Special recognition in server events</li>
                        <li>Access to rank-restricted areas</li>
                    </ul>
                </div>
            </div>
        </div>
    </section>

    <script src="script.js"></script>
</body>
</html>
