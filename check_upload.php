<?php
echo "<h2>PHP Upload Settings:</h2>";
echo "Max file size: " . ini_get('upload_max_filesize') . "<br>";
echo "Max post size: " . ini_get('post_max_size') . "<br>";
echo "Memory limit: " . ini_get('memory_limit') . "<br>";
echo "Max execution time: " . ini_get('max_execution_time') . "<br>";

echo "<h2>Upload Directory Check:</h2>";
$upload_dir = 'images/items/';
echo "Directory exists: " . (is_dir($upload_dir) ? 'YES' : 'NO') . "<br>";
echo "Directory writable: " . (is_writable($upload_dir) ? 'YES' : 'NO') . "<br>";
echo "Full path: " . realpath($upload_dir) . "<br>";

echo "<h2>Files in directory:</h2>";
if (is_dir($upload_dir)) {
    $files = scandir($upload_dir);
    foreach ($files as $file) {
        if ($file != '.' && $file != '..') {
            echo $file . " (" . filesize($upload_dir . $file) . " bytes)<br>";
        }
    }
}
?>
