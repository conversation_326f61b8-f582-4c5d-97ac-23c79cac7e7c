<?php
require_once "db.php";

$conn = new mysqli($servername, $username, $password, $dbname);
if ($conn->connect_error) {
    die("Connection failed: " . $conn->connect_error);
}

// Process form submission
$name = isset($_POST["name"]) ? $_POST["name"] : "";
$password = isset($_POST["password"]) ? $_POST["password"] : "";

$successMessage = "";

if ($_SERVER["REQUEST_METHOD"] == "POST" && !empty($name) && !empty($password)) {
    // Generate the next available ID
    $nextIDQuery = "SELECT MAX(id) AS max_id FROM account_data";
    $result = $conn->query($nextIDQuery);

    if ($result === FALSE) {
        die("Error: " . $conn->error);
    }

    $row = $result->fetch_assoc();
    $nextID = $row["max_id"] + 1;

    // Hash the password
    $hashedPassword = encryptPassword($password);

    // Insert data into the database
    $insertQuery = "INSERT INTO account_data (id, name, password, activated)
                    VALUES ($nextID, '$name', '$hashedPassword', 1)";

    if ($conn->query($insertQuery) === TRUE) {
        // Registration successful, set the success message
        $successMessage = "Registration Successful!";
    } else {
        $successMessage = "Error: Registration Failed";
    }
}

$conn->close();

// Function to hash the password
function encryptPassword($password) {
    $digest = sha1($password, true);
    $encodedDigest = base64_encode($digest);
    return $encodedDigest;
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Register - Aion-Blitz Server</title>
    <link rel="stylesheet" href="styles.css">
    <link href="https://fonts.googleapis.com/css2?family=Cinzel:wght@400;600;700&family=Open+Sans:wght@300;400;600&display=swap" rel="stylesheet">
</head>
<body>
    <!-- Video Background -->
    <div class="video-background">
        <video autoplay muted loop playsinline>
            <source src="images/bg_main.mp4" type="video/mp4">
        </video>
    </div>
    <div class="video-overlay"></div>

    <!-- Top Navigation -->
    <nav class="top-nav">
        <div class="nav-left">
            <span class="server-time">🕐 Server Time: 00:00</span>
            <span class="server-status">🟢 Server Status</span>
        </div>
        <div class="nav-center">
            <ul class="nav-menu">
                <li><a href="index.php">🏠 Home</a></li>
                <li><a href="index.php#faq">❓ F.A.Q</a></li>
                <li><a href="register.php" class="active">📝 Register</a></li>
                <li><a href="index.php#schedule">📅 Schedule</a></li>
                <li><a href="ranks.php">🏆 Ranks</a></li>
                <li><a href="activity.php">📊 Activity</a></li>
                <li><a href="pvp.php">⚔️ PvP</a></li>
                <li><a href="index.php#discord">💬 Discord</a></li>
            </ul>
        </div>
        <div class="nav-right">
            <div class="user-section">
                <a href="signin.php" class="signin-btn">Sign In</a>
            </div>
        </div>
    </nav>
    <div class="register-container">
        <div class="register-header">
            <h1>Join Aion-Blitz</h1>
            <p class="register-subtitle">Create your account and begin your adventure</p>
        </div>

        <?php if (!empty($successMessage)) : ?>
            <div class="message-container <?php echo (strpos($successMessage, 'Successful') !== false) ? 'success' : 'error'; ?>">
                <p><?php echo $successMessage; ?></p>
            </div>
        <?php endif; ?>

        <form method="POST" action="register.php" class="register-form">
            <div class="form-group">
                <label for="name">Account Name</label>
                <input type="text" name="name" id="name" placeholder="Enter your desired username" required>
            </div>

            <div class="form-group">
                <label for="password">Password</label>
                <input type="password" name="password" id="password" placeholder="Enter a secure password" required>
            </div>

            <button type="submit" class="register-btn">
                <span>Create Account</span>
            </button>
        </form>

        <div class="register-footer">
            <p>Already have an account? <a href="signin.php" class="signin-link">Sign In Here</a></p>
        </div>
    </div>

    <script src="script.js"></script>
</body>
</html>
