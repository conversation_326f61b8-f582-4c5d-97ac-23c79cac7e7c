<?php
// Test image upload functionality
echo "Testing Image Upload System\n";
echo "===========================\n";

// Check if images directory exists
$upload_dir = 'images/items/';
if (is_dir($upload_dir)) {
    echo "✓ Upload directory exists: $upload_dir\n";
    
    // Check permissions
    if (is_writable($upload_dir)) {
        echo "✓ Upload directory is writable\n";
    } else {
        echo "✗ Upload directory is not writable\n";
    }
} else {
    echo "✗ Upload directory does not exist: $upload_dir\n";
    echo "Creating directory...\n";
    if (mkdir($upload_dir, 0755, true)) {
        echo "✓ Directory created successfully\n";
    } else {
        echo "✗ Failed to create directory\n";
    }
}

// Check if default image exists
$default_image = $upload_dir . 'default.svg';
if (file_exists($default_image)) {
    echo "✓ Default image exists: $default_image\n";
} else {
    echo "✗ Default image missing: $default_image\n";
}

// List existing images
echo "\nExisting images in upload directory:\n";
$files = glob($upload_dir . '*');
if (!empty($files)) {
    foreach ($files as $file) {
        $filename = basename($file);
        $size = filesize($file);
        echo "- $filename (" . number_format($size) . " bytes)\n";
    }
} else {
    echo "- No images found\n";
}

// Test file type validation
echo "\nTesting file type validation:\n";
$allowed_types = ['jpg', 'jpeg', 'png', 'gif', 'webp'];
$test_files = [
    'test.jpg' => 'jpg',
    'test.PNG' => 'png',
    'test.gif' => 'gif',
    'test.webp' => 'webp',
    'test.txt' => 'txt',
    'test.exe' => 'exe'
];

foreach ($test_files as $filename => $extension) {
    $file_extension = strtolower(pathinfo($filename, PATHINFO_EXTENSION));
    $is_allowed = in_array($file_extension, $allowed_types);
    $status = $is_allowed ? '✓ Allowed' : '✗ Blocked';
    echo "- $filename ($extension) → $status\n";
}

// Test filename generation
echo "\nTesting filename generation:\n";
$item_id = 164000073;
$time = time();
$test_extensions = ['jpg', 'png', 'gif'];

foreach ($test_extensions as $ext) {
    $new_filename = $item_id . '_' . $time . '.' . $ext;
    echo "- Item ID $item_id → $new_filename\n";
}

echo "\n✓ Image upload system test completed\n";
echo "\nTo test actual uploads:\n";
echo "1. Visit: http://localhost/aion-blitz/manage_shop.php\n";
echo "2. Fill in item details\n";
echo "3. Select an image file (JPG, PNG, GIF, WebP)\n";
echo "4. Click 'Add Item'\n";
echo "5. Check if image appears in the shop items table\n";
?>
