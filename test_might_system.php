<?php
require_once "db.php";

echo "=== TESTING MIGHT SYSTEM ===\n";

// Test account
$testAccount = "admin"; // Change this to your test account

// Get account info
$accountQuery = "SELECT id, name FROM account_data WHERE name = ?";
$stmt = $conn->prepare($accountQuery);
$stmt->bind_param("s", $testAccount);
$stmt->execute();
$accountResult = $stmt->get_result();

if ($accountResult->num_rows === 0) {
    die("✗ Test account '$testAccount' not found!\n");
}

$account = $accountResult->fetch_assoc();
echo "✓ Testing with account: {$account['name']} (ID: {$account['id']})\n\n";

// Test 1: Check might balance
echo "=== TEST 1: MIGHT BALANCE ===\n";
$mightQuery = "SELECT might FROM might WHERE account_id = ?";
$stmt = $conn->prepare($mightQuery);
$stmt->bind_param("i", $account['id']);
$stmt->execute();
$mightResult = $stmt->get_result();

if ($mightResult->num_rows > 0) {
    $mightData = $mightResult->fetch_assoc();
    $currentMight = $mightData['might'];
    echo "✓ Current might balance: $currentMight MP\n";
} else {
    echo "✗ No might record found for this account\n";
    echo "Creating test might record...\n";
    
    $insertQuery = "INSERT INTO might (account_id, might) VALUES (?, 1000)";
    $stmt = $conn->prepare($insertQuery);
    $stmt->bind_param("i", $account['id']);
    
    if ($stmt->execute()) {
        echo "✓ Created test might record with 1000 MP\n";
        $currentMight = 1000;
    } else {
        die("✗ Failed to create might record: " . $stmt->error . "\n");
    }
}

// Test 2: Check shop items
echo "\n=== TEST 2: SHOP ITEMS ===\n";
$itemsQuery = "SELECT id, name, price, quantity FROM shop_items ORDER BY price ASC LIMIT 3";
$itemsResult = $conn->query($itemsQuery);

if ($itemsResult && $itemsResult->num_rows > 0) {
    echo "Available shop items:\n";
    while ($item = $itemsResult->fetch_assoc()) {
        $canAfford = ($currentMight >= $item['price']) ? "✓ Can afford" : "✗ Cannot afford";
        echo "- {$item['name']}: {$item['price']} MP (Qty: {$item['quantity']}) - $canAfford\n";
    }
} else {
    echo "✗ No shop items found\n";
}

// Test 3: Check characters for purchase
echo "\n=== TEST 3: CHARACTERS ===\n";
$charactersQuery = "SELECT id, name, player_class FROM players WHERE account_id = ? LIMIT 3";
$stmt = $conn->prepare($charactersQuery);
$stmt->bind_param("i", $account['id']);
$stmt->execute();
$charactersResult = $stmt->get_result();

if ($charactersResult && $charactersResult->num_rows > 0) {
    echo "Available characters:\n";
    while ($char = $charactersResult->fetch_assoc()) {
        echo "- {$char['name']} ({$char['player_class']}) - ID: {$char['id']}\n";
    }
} else {
    echo "✗ No characters found for this account\n";
    echo "Note: You need at least one character to receive shop items\n";
}

// Test 4: Check price_type for Might Points
echo "\n=== TEST 4: PRICE TYPE ===\n";
$priceTypeQuery = "SELECT * FROM price_type WHERE price_name = 'Might Points'";
$priceResult = $conn->query($priceTypeQuery);

if ($priceResult && $priceResult->num_rows > 0) {
    $priceType = $priceResult->fetch_assoc();
    echo "✓ Might Points price type found:\n";
    echo "  - ID: {$priceType['price_id']}\n";
    echo "  - Name: {$priceType['price_name']}\n";
    echo "  - Symbol: {$priceType['symbolic']}\n";
} else {
    echo "✗ Might Points price type not found\n";
}

// Test 5: Simulate balance check (like shop.php does)
echo "\n=== TEST 5: SHOP BALANCE CHECK ===\n";
$shopBalanceQuery = "SELECT might FROM might WHERE account_id = ?";
$stmt = $conn->prepare($shopBalanceQuery);
$stmt->bind_param("i", $account['id']);
$stmt->execute();
$shopResult = $stmt->get_result();
$shopData = $shopResult->fetch_assoc();
$shopBalance = $shopData['might'] ?? 0;

echo "Shop would show balance: $shopBalance MP\n";

if ($shopBalance > 0) {
    echo "✓ Shop system should work correctly\n";
} else {
    echo "✗ Shop system would show 0 balance\n";
}

echo "\n=== SUMMARY ===\n";
echo "✓ Might system is ready for testing!\n";
echo "✓ Visit your dashboard to see the might balance\n";
echo "✓ Visit the shop to test purchases\n";
echo "✓ Make sure you have characters created to receive items\n";

$conn->close();
?>
