/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

html, body {
    height: 100%;
    font-family: 'Open Sans', sans-serif;
    overflow-x: hidden;
}

body {
    background: #000000;
    color: #ffffff;
    position: relative;
    overflow-x: hidden;
}

/* Static Background */
.static-background {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: url("images/Aion_Blitz_Background.png");
    background-size: cover;
    background-position: center;
    background-attachment: fixed;
    z-index: -2;
}

/* Video Background (kept for reference) */
.video-background {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: -2;
    overflow: hidden;
}

.video-background video {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.video-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(rgba(0, 0, 0, 0.6), rgba(0, 0, 0, 0.7));
    z-index: -1;
}

/* Fallback background for when video doesn't load */
body::before {
    content: '';
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(rgba(0, 0, 0, 0.6), rgba(0, 0, 0, 0.7)), url("images/Aion_Blitz_Background.png");
    background-size: cover;
    background-position: center;
    background-attachment: fixed;
    z-index: -3;
}

/* Top Navigation */
.top-nav {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    background: rgba(0, 0, 0, 0.9);
    backdrop-filter: blur(10px);
    padding: 10px 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    z-index: 1000;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.nav-left {
    display: flex;
    gap: 20px;
    font-size: 12px;
    color: #cccccc;
}

.nav-center {
    flex: 1;
    display: flex;
    justify-content: center;
}

.nav-menu {
    display: flex;
    list-style: none;
    gap: 30px;
}

.nav-menu li a {
    color: #ffffff;
    text-decoration: none;
    font-size: 14px;
    font-weight: 500;
    padding: 8px 16px;
    border-radius: 4px;
    transition: all 0.3s ease;
    position: relative;
}

.nav-menu li a:hover {
    background: rgba(255, 255, 255, 0.1);
    color: #d4af37;
}

.nav-right {
    display: flex;
    align-items: center;
}

.user-section {
    display: flex;
    align-items: center;
    gap: 15px;
}

.welcome-user {
    color: #d4af37;
    font-size: 12px;
}

.signin-btn, .logout-btn {
    background: linear-gradient(45deg, #d4af37, #f4d03f);
    color: #000;
    padding: 6px 12px;
    border-radius: 4px;
    text-decoration: none;
    font-size: 12px;
    font-weight: 600;
    transition: all 0.3s ease;
}

.signin-btn:hover, .logout-btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(212, 175, 55, 0.3);
}

/* Download Panel */
.download-panel {
    position: fixed;
    top: 80px;
    right: 20px;
    z-index: 999;
}

.download-btn {
    background: linear-gradient(135deg, #e67e22, #d35400);
    border: none;
    border-radius: 8px;
    padding: 15px 20px;
    color: white;
    font-weight: bold;
    cursor: pointer;
    box-shadow: 0 4px 15px rgba(230, 126, 34, 0.4);
    transition: all 0.3s ease;
    display: flex;
    flex-direction: column;
    align-items: center;
    text-decoration: none;
    position: relative;
    overflow: hidden;
}

.download-btn::before {
    content: '⬇️';
    position: absolute;
    top: -30px;
    left: 50%;
    transform: translateX(-50%);
    font-size: 1.2rem;
    transition: all 0.3s ease;
    opacity: 0;
}

.download-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(230, 126, 34, 0.6);
    background: linear-gradient(135deg, #d35400, #e67e22);
}

.download-btn:hover::before {
    top: 5px;
    opacity: 1;
}

.download-btn:hover span {
    transform: translateY(3px);
}

.download-btn:hover small {
    transform: translateY(3px);
}

.download-btn span {
    font-size: 14px;
    font-weight: 700;
}

.download-btn small {
    font-size: 10px;
    opacity: 0.9;
}

/* Hero Section */
.hero-section {
    min-height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
    padding-top: 80px;
    position: relative;
}

.hero-background {
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
}

.hero-content {
    text-align: center;
    max-width: 900px;
    padding: 40px 20px;
    background: rgba(0, 0, 0, 0.4);
    border-radius: 20px;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(212, 175, 55, 0.3);
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.5);
    animation: heroFadeIn 1.5s ease-out;
}

@keyframes heroFadeIn {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.logo-container {
    margin-bottom: 40px;
    position: relative;
}

.main-logo {
    max-width: 450px;
    max-height: 320px;
    width: auto;
    height: auto;
    filter: drop-shadow(0 0 30px rgba(212, 175, 55, 0.7))
            drop-shadow(0 0 60px rgba(212, 175, 55, 0.4))
            drop-shadow(0 0 100px rgba(212, 175, 55, 0.2));
    transition: all 0.4s ease;
    margin-bottom: 30px;
    animation: logoGlow 3s ease-in-out infinite alternate;
}

.main-logo:hover {
    filter: drop-shadow(0 0 40px rgba(212, 175, 55, 1.0))
            drop-shadow(0 0 80px rgba(212, 175, 55, 0.6))
            drop-shadow(0 0 120px rgba(212, 175, 55, 0.3));
    transform: scale(1.05);
}

@keyframes logoGlow {
    from {
        filter: drop-shadow(0 0 30px rgba(212, 175, 55, 0.7))
                drop-shadow(0 0 60px rgba(212, 175, 55, 0.4))
                drop-shadow(0 0 100px rgba(212, 175, 55, 0.2));
    }
    to {
        filter: drop-shadow(0 0 35px rgba(212, 175, 55, 0.9))
                drop-shadow(0 0 70px rgba(212, 175, 55, 0.5))
                drop-shadow(0 0 110px rgba(212, 175, 55, 0.3));
    }
}

.logo-symbol {
    font-size: 80px;
    color: #d4af37;
    text-shadow: 0 0 20px rgba(212, 175, 55, 0.5);
    margin-bottom: 20px;
}

.main-title {
    font-family: 'Cinzel', serif;
    font-size: 4.5rem;
    font-weight: 700;
    margin-bottom: 15px;
    letter-spacing: 4px;
    position: relative;
    background: linear-gradient(45deg,
        #ff4500 0%,
        #ff6b00 15%,
        #ff8c00 30%,
        #ffa500 45%,
        #d4af37 60%,
        #f4d03f 75%,
        #ff8c00 90%,
        #ff4500 100%);
    background-size: 400% 100%;
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    animation: fireFlow 3s ease-in-out infinite;
    text-shadow: 0 0 30px rgba(255, 69, 0, 0.8),
                 0 0 60px rgba(255, 140, 0, 0.6),
                 0 0 90px rgba(212, 175, 55, 0.4);
    filter: drop-shadow(2px 2px 4px rgba(0, 0, 0, 0.8));
}

.main-title::before {
    content: '';
    position: absolute;
    top: -10px;
    left: -10px;
    right: -10px;
    bottom: -10px;
    background: radial-gradient(ellipse at center,
        rgba(255, 69, 0, 0.3) 0%,
        rgba(255, 140, 0, 0.2) 30%,
        rgba(255, 165, 0, 0.1) 60%,
        transparent 100%);
    animation: fireGlow 2s ease-in-out infinite alternate;
    z-index: -1;
    border-radius: 20px;
    filter: blur(15px);
}

.main-title::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(90deg,
        transparent 0%,
        rgba(255, 69, 0, 0.4) 20%,
        rgba(255, 140, 0, 0.6) 40%,
        rgba(255, 165, 0, 0.4) 60%,
        transparent 80%,
        transparent 100%);
    background-size: 200% 100%;
    animation: fireWave 4s linear infinite;
    mix-blend-mode: screen;
    pointer-events: none;
}



@keyframes fireFlow {
    0% {
        background-position: 0% 50%;
        text-shadow: 0 0 30px rgba(255, 69, 0, 0.8),
                     0 0 60px rgba(255, 140, 0, 0.6),
                     0 0 90px rgba(212, 175, 55, 0.4);
    }
    50% {
        background-position: 100% 50%;
        text-shadow: 0 0 40px rgba(255, 140, 0, 1.0),
                     0 0 80px rgba(255, 165, 0, 0.8),
                     0 0 120px rgba(255, 69, 0, 0.6);
    }
    100% {
        background-position: 200% 50%;
        text-shadow: 0 0 30px rgba(255, 69, 0, 0.8),
                     0 0 60px rgba(255, 140, 0, 0.6),
                     0 0 90px rgba(212, 175, 55, 0.4);
    }
}

@keyframes fireGlow {
    from {
        opacity: 0.6;
        transform: scale(1);
    }
    to {
        opacity: 1;
        transform: scale(1.05);
    }
}

@keyframes fireWave {
    0% {
        background-position: -200% 0;
    }
    100% {
        background-position: 200% 0;
    }
}

.subtitle {
    font-family: 'Cinzel', serif;
    font-size: 1.4rem;
    color: #ffffff;
    margin-bottom: 50px;
    letter-spacing: 3px;
    opacity: 0.95;
    text-shadow: 1px 1px 3px rgba(0, 0, 0, 0.8);
    font-weight: 400;
}

/* Action Buttons */
.action-buttons {
    display: flex;
    gap: 25px;
    justify-content: center;
    margin-bottom: 70px;
    flex-wrap: wrap;
}

.btn-primary, .btn-secondary {
    padding: 18px 35px;
    border: none;
    border-radius: 12px;
    font-size: 17px;
    font-weight: 700;
    cursor: pointer;
    transition: all 0.4s ease;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    gap: 10px;
    min-width: 200px;
    justify-content: center;
    position: relative;
    overflow: hidden;
    text-transform: uppercase;
    letter-spacing: 1px;
}

.btn-primary::before, .btn-secondary::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s;
}

.btn-primary:hover::before, .btn-secondary:hover::before {
    left: 100%;
}

.btn-primary {
    background: linear-gradient(135deg, #8B4513, #A0522D, #CD853F);
    color: white;
    border: 2px solid #d4af37;
    box-shadow: 0 4px 15px rgba(139, 69, 19, 0.3);
}

.btn-primary:hover {
    transform: translateY(-3px);
    box-shadow: 0 12px 35px rgba(139, 69, 19, 0.5);
    background: linear-gradient(135deg, #A0522D, #CD853F, #8B4513);
    border-color: #f4d03f;
}

.btn-secondary {
    background: linear-gradient(135deg, #2980b9, #3498db, #5dade2);
    color: white;
    border: 2px solid #3498db;
    box-shadow: 0 4px 15px rgba(52, 152, 219, 0.3);
}

.btn-secondary:hover {
    transform: translateY(-3px);
    box-shadow: 0 12px 35px rgba(52, 152, 219, 0.5);
    background: linear-gradient(135deg, #3498db, #5dade2, #2980b9);
    border-color: #85c1e9;
}

/* Server Stats */
.server-stats {
    display: flex;
    justify-content: center;
    gap: 30px;
    flex-wrap: wrap;
    margin-top: 50px;
    padding: 30px 20px;
    background: rgba(0, 0, 0, 0.3);
    border-radius: 15px;
    border: 1px solid rgba(212, 175, 55, 0.2);
    backdrop-filter: blur(5px);
}

.stat-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 12px;
    padding: 20px 15px;
    background: rgba(212, 175, 55, 0.1);
    border-radius: 12px;
    border: 1px solid rgba(212, 175, 55, 0.3);
    transition: all 0.4s ease;
    min-width: 120px;
    position: relative;
    overflow: hidden;
}

.stat-item::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 2px;
    background: linear-gradient(90deg, #d4af37, #f4d03f, #d4af37);
    transform: scaleX(0);
    transition: transform 0.4s ease;
}

.stat-item:hover {
    transform: translateY(-5px);
    background: rgba(212, 175, 55, 0.2);
    border-color: rgba(212, 175, 55, 0.6);
    box-shadow: 0 10px 25px rgba(212, 175, 55, 0.2);
}

.stat-item:hover::before {
    transform: scaleX(1);
}

.stat-icon {
    font-size: 32px;
    color: #d4af37;
    text-shadow: 0 0 10px rgba(212, 175, 55, 0.5);
    transition: all 0.3s ease;
}

.stat-item:hover .stat-icon {
    transform: scale(1.1);
    text-shadow: 0 0 15px rgba(212, 175, 55, 0.8);
}

.stat-label {
    font-size: 13px;
    color: #ffffff;
    text-align: center;
    font-weight: 600;
    letter-spacing: 0.5px;
    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.8);
}

/* Features Section */
.features-section {
    background: rgba(0, 0, 0, 0.95);
    padding: 100px 20px;
    margin-top: 50px;
    border-top: 1px solid rgba(212, 175, 55, 0.3);
    position: relative;
    overflow: hidden;
}

.features-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: radial-gradient(ellipse at center, rgba(212, 175, 55, 0.05) 0%, transparent 70%);
    z-index: 0;
}

.features-container {
    max-width: 1400px;
    margin: 0 auto;
    position: relative;
    z-index: 1;
}

.features-subtitle {
    text-align: center;
    color: #cccccc;
    font-size: 1.2rem;
    margin-bottom: 60px;
    font-weight: 300;
    letter-spacing: 1px;
}

.features-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 30px;
    margin-bottom: 80px;
}

.feature-card {
    background: rgba(0, 0, 0, 0.8);
    border-radius: 20px;
    padding: 40px 30px;
    border: 2px solid rgba(212, 175, 55, 0.2);
    transition: all 0.4s ease;
    position: relative;
    overflow: hidden;
    backdrop-filter: blur(10px);
}

.feature-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(212, 175, 55, 0.1), transparent);
    transition: left 0.6s ease;
}

.feature-card:hover::before {
    left: 100%;
}

.feature-card:hover {
    transform: translateY(-10px);
    border-color: rgba(212, 175, 55, 0.6);
    box-shadow: 0 20px 40px rgba(212, 175, 55, 0.2);
}

.feature-card.highlight {
    border-color: rgba(255, 140, 0, 0.5);
    background: rgba(255, 140, 0, 0.05);
}

.feature-card.highlight:hover {
    border-color: rgba(255, 140, 0, 0.8);
    box-shadow: 0 20px 40px rgba(255, 140, 0, 0.3);
}

.feature-icon {
    font-size: 3rem;
    margin-bottom: 20px;
    text-align: center;
    filter: drop-shadow(0 0 10px rgba(212, 175, 55, 0.5));
}

.feature-card h3 {
    color: #d4af37;
    font-family: 'Cinzel', serif;
    font-size: 1.5rem;
    margin-bottom: 15px;
    text-align: center;
}

.feature-card p {
    color: #ffffff;
    line-height: 1.6;
    text-align: center;
    margin-bottom: 20px;
    font-size: 15px;
}

.feature-badge {
    position: absolute;
    top: 15px;
    right: 15px;
    background: linear-gradient(135deg, #d4af37, #f4d03f);
    color: #000;
    padding: 5px 12px;
    border-radius: 15px;
    font-size: 11px;
    font-weight: 700;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.feature-card.highlight .feature-badge {
    background: linear-gradient(135deg, #ff8c00, #ffa500);
}

/* Rates Showcase */
.rates-showcase {
    background: rgba(0, 0, 0, 0.9);
    border-radius: 20px;
    padding: 50px 40px;
    border: 2px solid rgba(212, 175, 55, 0.3);
    text-align: center;
    position: relative;
    overflow: hidden;
}

.rates-showcase::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(45deg, rgba(212, 175, 55, 0.05), rgba(255, 140, 0, 0.05));
    z-index: 0;
}

.rates-showcase h3 {
    color: #d4af37;
    font-family: 'Cinzel', serif;
    font-size: 2.2rem;
    margin-bottom: 40px;
    position: relative;
    z-index: 1;
    text-shadow: 0 0 20px rgba(212, 175, 55, 0.5);
}

.rates-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 30px;
    position: relative;
    z-index: 1;
}

.rate-item {
    background: rgba(212, 175, 55, 0.1);
    border-radius: 15px;
    padding: 30px 20px;
    border: 2px solid rgba(212, 175, 55, 0.3);
    transition: all 0.4s ease;
    position: relative;
    overflow: hidden;
}

.rate-item::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: linear-gradient(90deg, #d4af37, #f4d03f, #d4af37);
    transform: scaleX(0);
    transition: transform 0.4s ease;
}

.rate-item:hover {
    transform: translateY(-5px);
    border-color: rgba(212, 175, 55, 0.6);
    background: rgba(212, 175, 55, 0.2);
    box-shadow: 0 15px 30px rgba(212, 175, 55, 0.2);
}

.rate-item:hover::before {
    transform: scaleX(1);
}

.rate-multiplier {
    font-size: 3rem;
    font-weight: 900;
    color: #d4af37;
    font-family: 'Cinzel', serif;
    margin-bottom: 10px;
    text-shadow: 0 0 15px rgba(212, 175, 55, 0.6);
    transition: all 0.3s ease;
}

.rate-item:hover .rate-multiplier {
    transform: scale(1.1);
    text-shadow: 0 0 25px rgba(212, 175, 55, 0.8);
}

.rate-label {
    font-size: 1.1rem;
    font-weight: 600;
    color: #ffffff;
    margin-bottom: 5px;
    text-transform: uppercase;
    letter-spacing: 1px;
}

.rate-description {
    font-size: 0.9rem;
    color: #cccccc;
    font-style: italic;
}

/* Schedule Section */
.schedule-section {
    background: rgba(0, 0, 0, 0.9);
    padding: 80px 20px;
    margin-top: 0px;
    border-top: 1px solid rgba(212, 175, 55, 0.3);
}

.schedule-container {
    max-width: 1200px;
    margin: 0 auto;
}

.schedule-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 30px;
    margin-bottom: 40px;
}

.schedule-card {
    background: rgba(255, 255, 255, 0.05);
    border-radius: 12px;
    overflow: hidden;
    border: 1px solid rgba(212, 175, 55, 0.2);
    transition: all 0.3s ease;
}

.schedule-card:hover {
    transform: translateY(-5px);
    border-color: rgba(212, 175, 55, 0.5);
    box-shadow: 0 10px 30px rgba(212, 175, 55, 0.1);
}

.schedule-header {
    background: linear-gradient(135deg, rgba(212, 175, 55, 0.2), rgba(212, 175, 55, 0.1));
    padding: 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.schedule-header h3 {
    color: #d4af37;
    font-family: 'Cinzel', serif;
    font-size: 1.2rem;
    margin: 0;
}

.schedule-type {
    background: rgba(212, 175, 55, 0.3);
    color: #d4af37;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 10px;
    font-weight: 600;
    text-transform: uppercase;
}

.schedule-content {
    padding: 20px;
}

.schedule-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 0;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.schedule-item:last-child {
    border-bottom: none;
}

.fortress-name {
    color: #ffffff;
    font-weight: 500;
    font-size: 14px;
}

.siege-time {
    color: #d4af37;
    font-size: 12px;
    font-weight: 600;
    background: rgba(212, 175, 55, 0.1);
    padding: 4px 8px;
    border-radius: 4px;
}

.schedule-note {
    background: rgba(212, 175, 55, 0.1);
    border: 1px solid rgba(212, 175, 55, 0.3);
    border-radius: 8px;
    padding: 20px;
    text-align: center;
}

.schedule-note p {
    margin: 5px 0;
    color: #ffffff;
    font-size: 14px;
}

.schedule-note strong {
    color: #d4af37;
}

/* World Raids Section */
.world-raids-section {
    background: rgba(0, 0, 0, 0.95);
    padding: 80px 20px;
    border-top: 1px solid rgba(212, 175, 55, 0.3);
}

.world-raids-container {
    max-width: 1400px;
    margin: 0 auto;
}

.raids-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
    gap: 30px;
    margin-bottom: 50px;
}

.raid-category {
    background: rgba(255, 255, 255, 0.03);
    border-radius: 12px;
    border: 1px solid rgba(212, 175, 55, 0.2);
    overflow: hidden;
    transition: all 0.3s ease;
}

.raid-category:hover {
    border-color: rgba(212, 175, 55, 0.4);
    transform: translateY(-3px);
    box-shadow: 0 8px 25px rgba(212, 175, 55, 0.1);
}

.raid-category.special-raid {
    border-color: rgba(231, 76, 60, 0.4);
    background: rgba(231, 76, 60, 0.05);
}

.raid-category.special-raid:hover {
    border-color: rgba(231, 76, 60, 0.6);
    box-shadow: 0 8px 25px rgba(231, 76, 60, 0.2);
}

.category-header {
    background: linear-gradient(135deg, rgba(212, 175, 55, 0.2), rgba(212, 175, 55, 0.1));
    padding: 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.special-raid .category-header {
    background: linear-gradient(135deg, rgba(231, 76, 60, 0.3), rgba(231, 76, 60, 0.1));
}

.category-header h3 {
    color: #d4af37;
    font-family: 'Cinzel', serif;
    font-size: 1.3rem;
    margin: 0;
}

.special-raid .category-header h3 {
    color: #e74c3c;
}

.category-type {
    background: rgba(212, 175, 55, 0.3);
    color: #d4af37;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 10px;
    font-weight: 600;
    text-transform: uppercase;
}

.special-raid .category-type {
    background: rgba(231, 76, 60, 0.3);
    color: #e74c3c;
}

.raid-list {
    padding: 25px;
}

.raid-group {
    margin-bottom: 25px;
}

.raid-group:last-child {
    margin-bottom: 0;
}

.raid-group h4 {
    color: #ffffff;
    font-size: 1rem;
    margin-bottom: 15px;
    padding-bottom: 8px;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    font-weight: 600;
}

.raid-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10px 0;
    border-bottom: 1px solid rgba(255, 255, 255, 0.05);
}

.raid-item:last-child {
    border-bottom: none;
}

.raid-item.special {
    background: rgba(231, 76, 60, 0.1);
    padding: 15px;
    border-radius: 8px;
    border: 1px solid rgba(231, 76, 60, 0.3);
    margin-bottom: 15px;
}

.zone-name {
    color: #ffffff;
    font-weight: 500;
    font-size: 14px;
}

.raid-item.special .zone-name {
    color: #e74c3c;
    font-weight: 600;
    font-size: 16px;
}

.raid-days {
    color: #d4af37;
    font-size: 12px;
    font-weight: 600;
    background: rgba(212, 175, 55, 0.1);
    padding: 4px 8px;
    border-radius: 4px;
}

.raid-item.special .raid-days {
    color: #e74c3c;
    background: rgba(231, 76, 60, 0.2);
    font-size: 13px;
}

.special-note {
    color: #cccccc;
    font-size: 13px;
    font-style: italic;
    margin-top: 10px;
    padding: 10px;
    background: rgba(231, 76, 60, 0.05);
    border-radius: 6px;
    border-left: 3px solid #e74c3c;
}

.special-note strong {
    color: #e74c3c;
}

.raids-info {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 30px;
}

.info-card {
    background: rgba(255, 255, 255, 0.05);
    border-radius: 12px;
    padding: 25px;
    border: 1px solid rgba(212, 175, 55, 0.2);
}

.info-card h4 {
    color: #d4af37;
    font-family: 'Cinzel', serif;
    font-size: 1.1rem;
    margin-bottom: 15px;
}

.info-card ul {
    list-style: none;
    padding: 0;
}

.info-card li {
    color: #ffffff;
    font-size: 14px;
    padding: 8px 0;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.info-card li:last-child {
    border-bottom: none;
}

.info-card strong {
    color: #d4af37;
}

/* News Section */
.news-section {
    background: rgba(0, 0, 0, 0.8);
    padding: 80px 20px;
    margin-top: 0;
}

.news-container {
    max-width: 1200px;
    margin: 0 auto;
}

.section-title {
    font-family: 'Cinzel', serif;
    font-size: 2.5rem;
    color: #d4af37;
    text-align: center;
    margin-bottom: 50px;
    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.8);
}

.news-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 30px;
}

.news-card {
    background: rgba(255, 255, 255, 0.05);
    border-radius: 12px;
    overflow: hidden;
    transition: all 0.3s ease;
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.news-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
    border-color: rgba(212, 175, 55, 0.3);
}

.news-image {
    height: 150px;
    overflow: hidden;
    position: relative;
}

.news-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.3s ease;
}

.news-card:hover .news-image img {
    transform: scale(1.05);
}
.news-content {
    padding: 20px;
}

.news-category {
    display: inline-block;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 10px;
    font-weight: 700;
    text-transform: uppercase;
    margin-bottom: 8px;
}

.gift-code-tag {
    background: #3498db;
    color: white;
}

.patch-notes-tag {
    background: #e74c3c;
    color: white;
}

.maintenance-tag {
    background: #9b59b6;
    color: white;
}

.news-date {
    float: right;
    font-size: 10px;
    color: #888;
    margin-top: 4px;
}

.news-content h3 {
    font-size: 16px;
    color: #ffffff;
    margin: 10px 0;
    font-weight: 600;
}

.news-content p {
    font-size: 13px;
    color: #cccccc;
    line-height: 1.5;
}

/* Cookie Notice */
.cookie-notice {
    position: fixed;
    bottom: 20px;
    left: 50%;
    transform: translateX(-50%);
    background: rgba(0, 0, 0, 0.9);
    color: white;
    padding: 15px 20px;
    border-radius: 8px;
    display: flex;
    align-items: center;
    gap: 15px;
    z-index: 1000;
    border: 1px solid rgba(212, 175, 55, 0.3);
}

.cookie-notice p {
    margin: 0;
    font-size: 14px;
}

.cookie-accept {
    background: #d4af37;
    color: #000;
    border: none;
    padding: 8px 16px;
    border-radius: 4px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
}

.cookie-accept:hover {
    background: #f4d03f;
    transform: translateY(-1px);
}

/* Navigation Active State */
.nav-menu li a.active {
    background: rgba(212, 175, 55, 0.2);
    color: #d4af37;
    border-radius: 4px;
}

/* Form Styles for Login/Register Pages */
.signin-container, .register-container {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background: rgba(0, 0, 0, 0.95);
    padding: 50px;
    border-radius: 16px;
    border: 1px solid rgba(212, 175, 55, 0.4);
    backdrop-filter: blur(15px);
    min-width: 400px;
    max-width: 450px;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.5);
}

.register-header {
    text-align: center;
    margin-bottom: 30px;
}

.signin-container h1, .register-container h1 {
    color: #d4af37;
    font-family: 'Cinzel', serif;
    font-size: 2rem;
    margin-bottom: 10px;
}

.register-subtitle {
    color: #cccccc;
    font-size: 14px;
    margin: 0;
    opacity: 0.8;
}

.message-container {
    padding: 15px;
    border-radius: 8px;
    margin-bottom: 25px;
    text-align: center;
}

.message-container.success {
    background: rgba(46, 204, 113, 0.1);
    border: 1px solid rgba(46, 204, 113, 0.3);
    color: #2ecc71;
}

.message-container.error {
    background: rgba(231, 76, 60, 0.1);
    border: 1px solid rgba(231, 76, 60, 0.3);
    color: #e74c3c;
}

.register-form {
    margin-bottom: 25px;
}

.form-group {
    margin-bottom: 20px;
}

.signin-container label, .register-container label {
    color: #d4af37;
    display: block;
    margin-bottom: 8px;
    font-weight: 600;
    font-size: 14px;
}

.signin-container input[type="text"],
.signin-container input[type="password"],
.register-container input[type="text"],
.register-container input[type="password"] {
    width: 100%;
    padding: 15px;
    background: rgba(255, 255, 255, 0.05);
    border: 2px solid rgba(212, 175, 55, 0.3);
    border-radius: 8px;
    color: #ffffff;
    font-size: 14px;
    transition: all 0.3s ease;
}

.signin-container input[type="text"]::placeholder,
.signin-container input[type="password"]::placeholder,
.register-container input[type="text"]::placeholder,
.register-container input[type="password"]::placeholder {
    color: rgba(255, 255, 255, 0.5);
}

.signin-container input[type="text"]:focus,
.signin-container input[type="password"]:focus,
.register-container input[type="text"]:focus,
.register-container input[type="password"]:focus {
    outline: none;
    border-color: #d4af37;
    box-shadow: 0 0 0 3px rgba(212, 175, 55, 0.2);
    background: rgba(255, 255, 255, 0.08);
}

.signin-container input[type="submit"],
.register-container input[type="submit"],
.register-btn {
    width: 100%;
    padding: 15px;
    background: linear-gradient(135deg, #d4af37, #f4d03f);
    color: #000;
    border: none;
    border-radius: 8px;
    font-weight: 700;
    font-size: 16px;
    cursor: pointer;
    transition: all 0.3s ease;
    text-transform: uppercase;
    letter-spacing: 1px;
}

.signin-container input[type="submit"]:hover,
.register-container input[type="submit"]:hover,
.register-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(212, 175, 55, 0.4);
    background: linear-gradient(135deg, #f4d03f, #d4af37);
}

.register-footer {
    text-align: center;
    padding-top: 20px;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.register-footer p {
    color: #cccccc;
    margin: 0;
    font-size: 14px;
}

.signin-link {
    color: #d4af37;
    text-decoration: none;
    font-weight: 600;
    transition: color 0.3s ease;
}

.signin-link:hover {
    color: #f4d03f;
    text-decoration: underline;
}

/* Rankings Section */
.rankings-section {
    min-height: 100vh;
    padding: 100px 20px 50px;
    background: rgba(0, 0, 0, 0.8);
}

.rankings-container {
    max-width: 1400px;
    margin: 0 auto;
}

.rankings-header {
    text-align: center;
    margin-bottom: 40px;
}

.rankings-header h1 {
    font-family: 'Cinzel', serif;
    font-size: 3rem;
    color: #d4af37;
    margin-bottom: 10px;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.8);
}

.rankings-subtitle {
    color: #cccccc;
    font-size: 1.1rem;
    margin: 0;
}

.ranking-tabs {
    display: flex;
    justify-content: center;
    gap: 20px;
    margin-bottom: 40px;
    flex-wrap: wrap;
}

.ranking-tab {
    background: rgba(255, 255, 255, 0.05);
    color: #ffffff;
    padding: 15px 25px;
    border-radius: 8px;
    text-decoration: none;
    font-weight: 600;
    border: 2px solid rgba(255, 255, 255, 0.1);
    transition: all 0.3s ease;
    font-size: 14px;
}

.ranking-tab:hover {
    background: rgba(212, 175, 55, 0.1);
    border-color: rgba(212, 175, 55, 0.3);
    color: #d4af37;
    transform: translateY(-2px);
}

.ranking-tab.active {
    background: linear-gradient(135deg, rgba(212, 175, 55, 0.2), rgba(212, 175, 55, 0.1));
    border-color: #d4af37;
    color: #d4af37;
    box-shadow: 0 4px 15px rgba(212, 175, 55, 0.2);
}

.rankings-table-container {
    background: rgba(0, 0, 0, 0.9);
    border-radius: 12px;
    overflow: hidden;
    border: 1px solid rgba(212, 175, 55, 0.3);
    margin-bottom: 40px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.5);
}

.rankings-table {
    width: 100%;
    border-collapse: collapse;
    font-size: 14px;
}

.rankings-table thead {
    background: linear-gradient(135deg, rgba(212, 175, 55, 0.3), rgba(212, 175, 55, 0.2));
}

.rankings-table th {
    padding: 20px 15px;
    text-align: left;
    color: #d4af37;
    font-weight: 700;
    text-transform: uppercase;
    font-size: 12px;
    letter-spacing: 1px;
    border-bottom: 2px solid rgba(212, 175, 55, 0.5);
}

.rankings-table td {
    padding: 15px;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    color: #ffffff;
}

.rankings-table tbody tr {
    transition: all 0.3s ease;
}

.rankings-table tbody tr:hover {
    background: rgba(212, 175, 55, 0.05);
    transform: scale(1.01);
}

.rank-first {
    background: linear-gradient(90deg, rgba(255, 215, 0, 0.1), transparent) !important;
    border-left: 4px solid #ffd700;
}

.rank-second {
    background: linear-gradient(90deg, rgba(192, 192, 192, 0.1), transparent) !important;
    border-left: 4px solid #c0c0c0;
}

.rank-third {
    background: linear-gradient(90deg, rgba(205, 127, 50, 0.1), transparent) !important;
    border-left: 4px solid #cd7f32;
}

.rank-position {
    font-weight: 700;
    font-size: 16px;
    text-align: center;
    width: 80px;
}

.player-name {
    font-weight: 600;
    color: #d4af37;
    font-size: 15px;
}

.player-name a {
    color: #d4af37;
    text-decoration: none;
    transition: all 0.3s ease;
}

.player-name a:hover {
    color: #f4d03f;
    text-shadow: 0 0 5px rgba(212, 175, 55, 0.5);
}

.player-class {
    color: #3498db;
    font-weight: 500;
}

.player-race {
    color: #e74c3c;
    font-weight: 500;
}

.player-level {
    color: #2ecc71;
    font-weight: 600;
}

.player-legion {
    color: #9b59b6;
    font-style: italic;
}

.abyss-rank {
    color: #f39c12;
    font-weight: 500;
}

.score {
    font-weight: 700;
    color: #d4af37;
    font-size: 16px;
    text-align: right;
}

.no-data {
    text-align: center;
    color: #888;
    font-style: italic;
    padding: 40px;
}

.ranking-info {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 30px;
}

.ranking-info .info-card {
    background: rgba(255, 255, 255, 0.05);
    border-radius: 12px;
    padding: 25px;
    border: 1px solid rgba(212, 175, 55, 0.2);
}

.ranking-info .info-card h3 {
    color: #d4af37;
    font-family: 'Cinzel', serif;
    font-size: 1.2rem;
    margin-bottom: 15px;
}

.ranking-info .info-card ul {
    list-style: none;
    padding: 0;
}

.ranking-info .info-card li {
    color: #ffffff;
    font-size: 14px;
    padding: 8px 0;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.ranking-info .info-card li:last-child {
    border-bottom: none;
}

.ranking-info .info-card strong {
    color: #d4af37;
}

/* Player Profile Section */
.player-profile-section {
    min-height: 100vh;
    padding: 100px 20px 50px;
    background: rgba(0, 0, 0, 0.8);
}

.profile-container {
    max-width: 1200px;
    margin: 0 auto;
}

.profile-header {
    display: flex;
    align-items: center;
    gap: 20px;
    margin-bottom: 30px;
}

.back-btn {
    background: rgba(255, 255, 255, 0.1);
    color: #d4af37;
    padding: 10px 20px;
    border-radius: 6px;
    text-decoration: none;
    font-weight: 600;
    transition: all 0.3s ease;
    border: 1px solid rgba(212, 175, 55, 0.3);
}

.back-btn:hover {
    background: rgba(212, 175, 55, 0.2);
    transform: translateX(-3px);
}

.profile-header h1 {
    font-family: 'Cinzel', serif;
    color: #d4af37;
    font-size: 2.5rem;
    margin: 0;
}

.profile-main {
    display: grid;
    gap: 30px;
}

.player-card {
    background: rgba(0, 0, 0, 0.9);
    border-radius: 16px;
    padding: 30px;
    border: 1px solid rgba(212, 175, 55, 0.3);
    display: flex;
    align-items: center;
    gap: 30px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.5);
}

.player-avatar {
    position: relative;
    flex-shrink: 0;
}

.avatar-placeholder {
    width: 120px;
    height: 120px;
    background: linear-gradient(135deg, #d4af37, #f4d03f);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 2.5rem;
    font-weight: 700;
    color: #000;
    border: 4px solid rgba(212, 175, 55, 0.5);
}

.online-indicator {
    position: absolute;
    bottom: 10px;
    right: 10px;
    width: 20px;
    height: 20px;
    border-radius: 50%;
    border: 3px solid #000;
}

.online-indicator.online {
    background: #2ecc71;
    box-shadow: 0 0 10px rgba(46, 204, 113, 0.5);
}

.online-indicator.offline {
    background: #95a5a6;
}

.player-info {
    flex: 1;
}

.player-name {
    font-family: 'Cinzel', serif;
    font-size: 2.2rem;
    color: #d4af37;
    margin: 0 0 15px 0;
    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.8);
}

.player-details {
    display: flex;
    gap: 20px;
    margin-bottom: 15px;
    flex-wrap: wrap;
}

.player-class {
    background: rgba(52, 152, 219, 0.2);
    color: #3498db;
    padding: 6px 12px;
    border-radius: 6px;
    font-weight: 600;
    font-size: 14px;
}

.player-race {
    background: rgba(231, 76, 60, 0.2);
    color: #e74c3c;
    padding: 6px 12px;
    border-radius: 6px;
    font-weight: 600;
    font-size: 14px;
}

.player-gender {
    background: rgba(155, 89, 182, 0.2);
    color: #9b59b6;
    padding: 6px 12px;
    border-radius: 6px;
    font-weight: 600;
    font-size: 14px;
}

.player-level {
    font-size: 1.5rem;
    color: #2ecc71;
    font-weight: 700;
    margin-bottom: 15px;
}

.player-status {
    display: flex;
    align-items: center;
    gap: 15px;
}

.status {
    padding: 6px 12px;
    border-radius: 6px;
    font-weight: 600;
    font-size: 14px;
}

.status.online {
    background: rgba(46, 204, 113, 0.2);
    color: #2ecc71;
}

.status.offline {
    background: rgba(149, 165, 166, 0.2);
    color: #95a5a6;
}

.last-seen {
    color: #888;
    font-size: 12px;
}

.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
}

.stat-card {
    background: rgba(0, 0, 0, 0.9);
    border-radius: 12px;
    padding: 25px;
    border: 1px solid rgba(212, 175, 55, 0.2);
    display: flex;
    align-items: center;
    gap: 20px;
    transition: all 0.3s ease;
}

.stat-card:hover {
    border-color: rgba(212, 175, 55, 0.4);
    transform: translateY(-3px);
    box-shadow: 0 8px 25px rgba(212, 175, 55, 0.1);
}

.stat-icon {
    font-size: 2.5rem;
    width: 60px;
    height: 60px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: rgba(212, 175, 55, 0.1);
    border-radius: 12px;
    flex-shrink: 0;
}

.stat-info {
    flex: 1;
}

.stat-value {
    font-size: 1.8rem;
    font-weight: 700;
    color: #d4af37;
    margin-bottom: 5px;
}

.stat-label {
    color: #ffffff;
    font-weight: 600;
    margin-bottom: 3px;
}

.stat-sub {
    color: #888;
    font-size: 12px;
}

/* Activity Section */
.activity-section {
    min-height: 100vh;
    padding: 100px 20px 50px;
    background: rgba(0, 0, 0, 0.8);
}

.activity-container {
    max-width: 1400px;
    margin: 0 auto;
}

.activity-header {
    text-align: center;
    margin-bottom: 40px;
}

.activity-header h1 {
    font-family: 'Cinzel', serif;
    font-size: 3rem;
    color: #d4af37;
    margin-bottom: 10px;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.8);
}

.activity-subtitle {
    color: #cccccc;
    font-size: 1.1rem;
    margin: 0;
}

.activity-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 30px;
}

.activity-card {
    background: rgba(0, 0, 0, 0.9);
    border-radius: 16px;
    border: 1px solid rgba(212, 175, 55, 0.3);
    overflow: hidden;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.5);
    transition: all 0.3s ease;
}

.activity-card:hover {
    border-color: rgba(212, 175, 55, 0.5);
    transform: translateY(-5px);
    box-shadow: 0 15px 40px rgba(212, 175, 55, 0.1);
}

.card-header {
    background: linear-gradient(135deg, rgba(212, 175, 55, 0.2), rgba(212, 175, 55, 0.1));
    padding: 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-bottom: 1px solid rgba(212, 175, 55, 0.3);
}

.card-header h3 {
    color: #d4af37;
    font-family: 'Cinzel', serif;
    font-size: 1.2rem;
    margin: 0;
}

.card-badge {
    background: rgba(212, 175, 55, 0.3);
    color: #d4af37;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 10px;
    font-weight: 600;
    text-transform: uppercase;
}

.activity-list {
    padding: 20px;
    max-height: 400px;
    overflow-y: auto;
}

.activity-item {
    display: flex;
    align-items: center;
    gap: 15px;
    padding: 12px 0;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    transition: all 0.3s ease;
}

.activity-item:last-child {
    border-bottom: none;
}

.activity-item:hover {
    background: rgba(212, 175, 55, 0.05);
    border-radius: 8px;
    padding-left: 10px;
    padding-right: 10px;
}

.activity-icon {
    font-size: 1.5rem;
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: rgba(212, 175, 55, 0.1);
    border-radius: 8px;
    flex-shrink: 0;
}

.activity-content {
    flex: 1;
    min-width: 0;
}

.activity-title {
    color: #ffffff;
    font-weight: 600;
    margin-bottom: 3px;
    display: flex;
    align-items: center;
    gap: 8px;
}

.activity-title a {
    color: #d4af37;
    text-decoration: none;
    transition: color 0.3s ease;
}

.activity-title a:hover {
    color: #f4d03f;
}

.level-badge {
    background: rgba(46, 204, 113, 0.2);
    color: #2ecc71;
    padding: 2px 6px;
    border-radius: 4px;
    font-size: 10px;
    font-weight: 600;
}

.activity-meta {
    color: #888;
    font-size: 12px;
    margin-bottom: 2px;
}

.activity-time {
    color: #666;
    font-size: 11px;
}

.no-activity {
    text-align: center;
    color: #888;
    font-style: italic;
    padding: 20px;
}

/* Custom scrollbar for activity lists */
.activity-list::-webkit-scrollbar {
    width: 6px;
}

.activity-list::-webkit-scrollbar-track {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 3px;
}

.activity-list::-webkit-scrollbar-thumb {
    background: rgba(212, 175, 55, 0.3);
    border-radius: 3px;
}

.activity-list::-webkit-scrollbar-thumb:hover {
    background: rgba(212, 175, 55, 0.5);
}

/* PvP Section */
.pvp-section {
    min-height: 100vh;
    padding: 100px 20px 50px;
    background: rgba(0, 0, 0, 0.8);
}

.pvp-container {
    max-width: 1400px;
    margin: 0 auto;
}

.pvp-header {
    text-align: center;
    margin-bottom: 40px;
}

.pvp-header h1 {
    font-family: 'Cinzel', serif;
    font-size: 3rem;
    color: #d4af37;
    margin-bottom: 10px;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.8);
}

.pvp-subtitle {
    color: #cccccc;
    font-size: 1.1rem;
    margin: 0;
}

.pvp-tabs {
    display: flex;
    justify-content: center;
    gap: 20px;
    margin-bottom: 40px;
    flex-wrap: wrap;
}

.pvp-tab {
    background: rgba(255, 255, 255, 0.05);
    color: #ffffff;
    padding: 15px 25px;
    border: 2px solid rgba(255, 255, 255, 0.1);
    border-radius: 8px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 14px;
}

.pvp-tab:hover {
    background: rgba(212, 175, 55, 0.1);
    border-color: rgba(212, 175, 55, 0.3);
    color: #d4af37;
    transform: translateY(-2px);
}

.pvp-tab.active {
    background: linear-gradient(135deg, rgba(212, 175, 55, 0.2), rgba(212, 175, 55, 0.1));
    border-color: #d4af37;
    color: #d4af37;
    box-shadow: 0 4px 15px rgba(212, 175, 55, 0.2);
}

.pvp-content {
    display: none;
}

.pvp-content.active {
    display: block;
}

.pvp-grid {
    display: grid;
    grid-template-columns: 1fr;
    gap: 30px;
}

.pvp-card {
    background: rgba(0, 0, 0, 0.9);
    border-radius: 16px;
    border: 1px solid rgba(212, 175, 55, 0.3);
    overflow: hidden;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.5);
}

.pvp-list {
    padding: 20px;
    max-height: 600px;
    overflow-y: auto;
}

.pvp-item {
    display: flex;
    align-items: center;
    gap: 20px;
    padding: 15px 0;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    transition: all 0.3s ease;
}

.pvp-item:last-child {
    border-bottom: none;
}

.pvp-item:hover {
    background: rgba(212, 175, 55, 0.05);
    border-radius: 8px;
    padding-left: 15px;
    padding-right: 15px;
}

.pvp-rank {
    font-size: 1.2rem;
    font-weight: 700;
    color: #d4af37;
    min-width: 50px;
    text-align: center;
}

.pvp-player {
    flex: 1;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.player-info {
    display: flex;
    flex-direction: column;
    gap: 5px;
}

.player-link {
    color: #d4af37;
    text-decoration: none;
    font-weight: 600;
    font-size: 16px;
    transition: color 0.3s ease;
}

.player-link:hover {
    color: #f4d03f;
}

.player-details {
    color: #888;
    font-size: 12px;
}

.pvp-stats {
    display: flex;
    flex-direction: column;
    align-items: flex-end;
    gap: 3px;
}

.kill-count {
    color: #e74c3c;
    font-weight: 600;
    font-size: 14px;
}

.gp-count, .ap-count {
    color: #3498db;
    font-size: 12px;
}

/* Achievements Grid */
.achievements-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 20px;
}

.achievement-card {
    background: rgba(0, 0, 0, 0.9);
    border-radius: 12px;
    padding: 20px;
    border: 1px solid rgba(212, 175, 55, 0.2);
    transition: all 0.3s ease;
}

.achievement-card:hover {
    border-color: rgba(212, 175, 55, 0.4);
    transform: translateY(-3px);
    box-shadow: 0 8px 25px rgba(212, 175, 55, 0.1);
}

.achievement-player {
    margin-bottom: 15px;
}

.achievement-name {
    color: #d4af37;
    text-decoration: none;
    font-weight: 600;
    font-size: 16px;
}

.achievement-name:hover {
    color: #f4d03f;
}

.achievement-class {
    color: #888;
    font-size: 12px;
    margin-top: 3px;
}

.achievement-titles {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
    margin-bottom: 15px;
}

.achievement-badge {
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 10px;
    font-weight: 600;
    text-transform: uppercase;
}

.kill-badge {
    background: rgba(231, 76, 60, 0.2);
    color: #e74c3c;
}

.glory-badge {
    background: rgba(212, 175, 55, 0.2);
    color: #d4af37;
}

.abyss-badge {
    background: rgba(155, 89, 182, 0.2);
    color: #9b59b6;
}

.achievement-stats {
    display: flex;
    justify-content: space-between;
    color: #888;
    font-size: 12px;
}

/* Class Stats Grid */
.class-stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 20px;
}

.class-stat-card {
    background: rgba(0, 0, 0, 0.9);
    border-radius: 12px;
    padding: 20px;
    border: 1px solid rgba(212, 175, 55, 0.2);
    transition: all 0.3s ease;
}

.class-stat-card:hover {
    border-color: rgba(212, 175, 55, 0.4);
    transform: translateY(-3px);
}

.class-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
    padding-bottom: 10px;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.class-header h4 {
    color: #d4af37;
    font-family: 'Cinzel', serif;
    margin: 0;
    font-size: 1.1rem;
}

.player-count {
    background: rgba(212, 175, 55, 0.2);
    color: #d4af37;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 10px;
    font-weight: 600;
}

.class-stats {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.stat-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.stat-label {
    color: #888;
    font-size: 12px;
}

.stat-value {
    color: #ffffff;
    font-weight: 600;
    font-size: 14px;
}

/* Dashboard Section */
.dashboard-section {
    min-height: 100vh;
    padding: 100px 20px 50px;
    background: rgba(0, 0, 0, 0.8);
}

.dashboard-container {
    max-width: 1400px;
    margin: 0 auto;
}

.dashboard-header {
    text-align: center;
    margin-bottom: 40px;
}

.dashboard-header h1 {
    font-family: 'Cinzel', serif;
    font-size: 3rem;
    color: #d4af37;
    margin-bottom: 10px;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.8);
}

.dashboard-subtitle {
    color: #cccccc;
    font-size: 1.1rem;
    margin: 0;
}

.dashboard-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
    gap: 30px;
}

.dashboard-card {
    background: rgba(0, 0, 0, 0.9);
    border-radius: 16px;
    border: 1px solid rgba(212, 175, 55, 0.3);
    overflow: hidden;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.5);
    transition: all 0.3s ease;
}

.dashboard-card:hover {
    border-color: rgba(212, 175, 55, 0.5);
    transform: translateY(-5px);
    box-shadow: 0 15px 40px rgba(212, 175, 55, 0.1);
}

.dashboard-card .card-header {
    background: linear-gradient(135deg, rgba(212, 175, 55, 0.2), rgba(212, 175, 55, 0.1));
    padding: 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-bottom: 1px solid rgba(212, 175, 55, 0.3);
}

.dashboard-card .card-header h3 {
    color: #d4af37;
    font-family: 'Cinzel', serif;
    font-size: 1.2rem;
    margin: 0;
}

.membership-badge {
    padding: 6px 12px;
    border-radius: 6px;
    font-size: 11px;
    font-weight: 700;
    text-transform: uppercase;
}

.membership-badge.free-player {
    background: rgba(149, 165, 166, 0.2);
    color: #95a5a6;
}

.membership-badge.bronze-member {
    background: rgba(205, 127, 50, 0.2);
    color: #cd7f32;
}

.membership-badge.silver-member {
    background: rgba(192, 192, 192, 0.2);
    color: #c0c0c0;
}

.membership-badge.gold-member {
    background: rgba(255, 215, 0, 0.2);
    color: #ffd700;
}

.membership-badge.platinum-member {
    background: rgba(229, 228, 226, 0.2);
    color: #e5e4e2;
}

.card-content {
    padding: 25px;
}

.info-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 0;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.info-row:last-child {
    border-bottom: none;
}

.info-label {
    color: #888;
    font-size: 14px;
}

.info-value {
    color: #ffffff;
    font-weight: 600;
    font-size: 14px;
}

.access-level-0 { color: #95a5a6; }
.access-level-1 { color: #3498db; }
.access-level-2 { color: #e74c3c; }
.access-level-3 { color: #e67e22; }
.access-level-4 { color: #d4af37; }

/* Characters Card */
.characters-list {
    max-height: 300px;
    overflow-y: auto;
}

.character-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px 0;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    transition: all 0.3s ease;
}

.character-item:last-child {
    border-bottom: none;
}

.character-item:hover {
    background: rgba(212, 175, 55, 0.05);
    border-radius: 8px;
    padding-left: 10px;
    padding-right: 10px;
}

.character-info {
    flex: 1;
}

.character-name {
    display: flex;
    align-items: center;
    gap: 10px;
    margin-bottom: 5px;
}

.character-name a {
    color: #d4af37;
    text-decoration: none;
    font-weight: 600;
    font-size: 15px;
}

.character-name a:hover {
    color: #f4d03f;
}

.character-status {
    padding: 2px 6px;
    border-radius: 4px;
    font-size: 10px;
    font-weight: 600;
    text-transform: uppercase;
}

.character-status.online {
    background: rgba(46, 204, 113, 0.2);
    color: #2ecc71;
}

.character-status.offline {
    background: rgba(149, 165, 166, 0.2);
    color: #95a5a6;
}

.character-details {
    color: #888;
    font-size: 12px;
}

.character-stats {
    display: flex;
    flex-direction: column;
    align-items: flex-end;
    gap: 3px;
}

.character-stats .stat {
    color: #3498db;
    font-size: 11px;
    font-weight: 600;
}

.no-characters {
    text-align: center;
    color: #888;
    font-style: italic;
    padding: 30px;
}

/* Daily Rewards */
.stamps-progress {
    display: grid;
    grid-template-columns: repeat(10, 1fr);
    gap: 8px;
    margin-bottom: 20px;
}

.stamp {
    aspect-ratio: 1;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 8px;
    font-size: 12px;
    font-weight: 600;
    transition: all 0.3s ease;
    border: 2px solid rgba(255, 255, 255, 0.1);
}

.stamp.pending {
    background: rgba(255, 255, 255, 0.05);
    color: #666;
}

.stamp.collected {
    background: linear-gradient(135deg, #d4af37, #f4d03f);
    color: #000;
    border-color: #d4af37;
    box-shadow: 0 2px 8px rgba(212, 175, 55, 0.3);
}

.stamp.weekly-bonus {
    border-color: #e74c3c;
    position: relative;
}

.stamp.weekly-bonus::after {
    content: '🎁';
    position: absolute;
    top: -5px;
    right: -5px;
    font-size: 8px;
}

.stamp-break {
    grid-column: 1 / -1;
    height: 10px;
}

.next-reward {
    text-align: center;
}

.claim-btn {
    background: linear-gradient(135deg, #2ecc71, #27ae60);
    color: white;
    border: none;
    padding: 12px 24px;
    border-radius: 8px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 14px;
}

.claim-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(46, 204, 113, 0.4);
}

.next-claim {
    color: #888;
    font-size: 14px;
    padding: 12px;
    background: rgba(255, 255, 255, 0.05);
    border-radius: 8px;
}

/* Donation Card */
.donation-info {
    margin-bottom: 20px;
}

.donation-info p {
    color: #cccccc;
    margin-bottom: 15px;
    text-align: center;
}

.donation-benefits {
    display: grid;
    gap: 8px;
    margin-bottom: 20px;
}

.benefit-item {
    color: #ffffff;
    font-size: 13px;
    padding: 8px 12px;
    background: rgba(212, 175, 55, 0.1);
    border-radius: 6px;
    border-left: 3px solid #d4af37;
}

.donation-amounts {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 10px;
    margin-bottom: 20px;
}

.donation-btn {
    background: linear-gradient(135deg, #3498db, #2980b9);
    color: white;
    border: none;
    padding: 12px 16px;
    border-radius: 8px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 13px;
}

.donation-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(52, 152, 219, 0.4);
}

.custom-amount {
    display: flex;
    gap: 10px;
    margin-top: 15px;
}

.custom-amount input {
    flex: 1;
    padding: 10px;
    border: 1px solid rgba(212, 175, 55, 0.3);
    border-radius: 6px;
    background: rgba(255, 255, 255, 0.05);
    color: #ffffff;
    font-size: 14px;
}

.custom-amount input:focus {
    outline: none;
    border-color: #d4af37;
    box-shadow: 0 0 0 2px rgba(212, 175, 55, 0.2);
}

.custom-amount button {
    background: linear-gradient(135deg, #d4af37, #f4d03f);
    color: #000;
    border: none;
    padding: 10px 16px;
    border-radius: 6px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
}

.custom-amount button:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 15px rgba(212, 175, 55, 0.4);
}

#paypal-button-container {
    margin: 20px 0;
    padding: 15px;
    background: rgba(255, 255, 255, 0.05);
    border-radius: 8px;
    border: 1px solid rgba(212, 175, 55, 0.2);
}

/* FAQ Section */
.faq-section {
    background: rgba(0, 0, 0, 0.9);
    padding: 80px 20px;
    border-top: 1px solid rgba(212, 175, 55, 0.3);
}

.faq-container {
    max-width: 1400px;
    margin: 0 auto;
}

.faq-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(450px, 1fr));
    gap: 40px;
}

.faq-category {
    background: rgba(255, 255, 255, 0.03);
    border-radius: 16px;
    border: 1px solid rgba(212, 175, 55, 0.2);
    overflow: hidden;
    transition: all 0.3s ease;
}

.faq-category:hover {
    border-color: rgba(212, 175, 55, 0.4);
    transform: translateY(-5px);
    box-shadow: 0 10px 30px rgba(212, 175, 55, 0.1);
}

.faq-category h3 {
    background: linear-gradient(135deg, rgba(212, 175, 55, 0.2), rgba(212, 175, 55, 0.1));
    color: #d4af37;
    font-family: 'Cinzel', serif;
    font-size: 1.3rem;
    margin: 0;
    padding: 25px;
    border-bottom: 1px solid rgba(212, 175, 55, 0.3);
    text-align: center;
}

.faq-items {
    padding: 25px;
}

.faq-item {
    margin-bottom: 25px;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    padding-bottom: 20px;
}

.faq-item:last-child {
    margin-bottom: 0;
    border-bottom: none;
    padding-bottom: 0;
}

.faq-question {
    color: #d4af37;
    font-weight: 600;
    font-size: 1.1rem;
    margin-bottom: 12px;
    cursor: pointer;
    transition: color 0.3s ease;
    position: relative;
    padding-left: 20px;
}

.faq-question::before {
    content: '▶';
    position: absolute;
    left: 0;
    color: #d4af37;
    font-size: 12px;
    transition: transform 0.3s ease;
}

.faq-question:hover {
    color: #f4d03f;
}

.faq-answer {
    color: #cccccc;
    line-height: 1.6;
    margin-left: 20px;
}

.faq-answer p {
    margin: 8px 0;
    font-size: 14px;
}

.faq-answer strong {
    color: #d4af37;
    font-weight: 600;
}

/* FAQ Interactive Behavior */
.faq-item.active .faq-question::before {
    transform: rotate(90deg);
}

.faq-item.active .faq-question {
    color: #f4d03f;
}

/* Responsive Design */
@media (max-width: 768px) {
    .main-title {
        font-size: 2.5rem;
    }

    .subtitle {
        font-size: 1rem;
    }

    .action-buttons {
        flex-direction: column;
        align-items: center;
    }

    .server-stats {
        gap: 20px;
    }

    .nav-menu {
        gap: 15px;
    }

    .nav-menu li a {
        font-size: 12px;
        padding: 6px 12px;
    }

    .download-panel {
        top: 70px;
        right: 10px;
    }

    .news-grid {
        grid-template-columns: 1fr;
    }

    .raids-grid {
        grid-template-columns: 1fr;
    }

    .raids-info {
        grid-template-columns: 1fr;
    }

    .ranking-tabs {
        flex-direction: column;
        align-items: center;
    }

    .ranking-tab {
        width: 200px;
        text-align: center;
    }

    .rankings-header h1 {
        font-size: 2rem;
    }

    .rankings-table-container {
        overflow-x: auto;
    }

    .rankings-table {
        min-width: 800px;
    }

    .rankings-table th,
    .rankings-table td {
        padding: 10px 8px;
        font-size: 12px;
    }

    .faq-grid {
        grid-template-columns: 1fr;
    }

    .faq-category h3 {
        font-size: 1.1rem;
        padding: 20px;
    }

    .faq-items {
        padding: 20px;
    }

    .faq-question {
        font-size: 1rem;
    }
}

@media (max-width: 480px) {
    .hero-content {
        padding: 0 15px;
    }

    .main-title {
        font-size: 2rem;
    }

    .nav-left {
        display: none;
    }

    .top-nav {
        padding: 8px 15px;
    }
}

/* Shop Styles */
.shop-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 2rem;
    padding: 1rem;
    background: rgba(0, 0, 0, 0.7);
    border-radius: 10px;
    border: 1px solid #d4af37;
}

.shop-header h1 {
    color: #d4af37;
    margin: 0;
}

.user-balance {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.balance-label {
    color: #fff;
    font-weight: 600;
}

.balance-amount {
    color: #d4af37;
    font-weight: bold;
    font-size: 1.2rem;
}

.shop-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 1.5rem;
    padding: 1rem;
}

.shop-item {
    background: rgba(0, 0, 0, 0.8);
    border-radius: 10px;
    padding: 1rem;
    border: 2px solid #333;
    transition: all 0.3s ease;
    position: relative;
}

.shop-item:hover {
    transform: translateY(-5px);
    border-color: #d4af37;
    box-shadow: 0 5px 20px rgba(212, 175, 55, 0.3);
}

.shop-item.common { border-color: #9d9d9d; }
.shop-item.superior { border-color: #1eff00; }
.shop-item.heroic { border-color: #0070dd; }
.shop-item.fabled { border-color: #a335ee; }
.shop-item.eternal { border-color: #ff8000; }
.shop-item.mythic { border-color: #e6cc80; }
.shop-item.legendary { border-color: #ff8000; }

.item-image {
    position: relative;
    text-align: center;
    margin-bottom: 1rem;
    height: 120px;
    display: flex;
    align-items: center;
    justify-content: center;
    overflow: hidden;
}

.item-image img {
    max-width: 100%;
    max-height: 100%;
    object-fit: contain;
    border-radius: 5px;
}

.rarity-badge {
    position: absolute;
    top: -5px;
    right: -5px;
    background: #d4af37;
    color: #000;
    padding: 2px 6px;
    border-radius: 3px;
    font-size: 0.7rem;
    font-weight: bold;
    text-transform: uppercase;
}

.item-info {
    text-align: center;
}

.item-name {
    color: #d4af37;
    margin: 0 0 0.5rem 0;
    font-size: 1.1rem;
}

.item-description {
    color: #ccc;
    margin: 0 0 1rem 0;
    font-size: 0.9rem;
    line-height: 1.4;
}

.item-price {
    color: #d4af37;
    font-weight: bold;
    font-size: 1.2rem;
    margin-bottom: 1rem;
}

.buy-btn {
    background: linear-gradient(45deg, #d4af37, #f4d03f);
    color: #000;
    border: none;
    padding: 0.7rem 1.5rem;
    border-radius: 5px;
    font-weight: bold;
    cursor: pointer;
    transition: all 0.3s ease;
    width: 100%;
}

.buy-btn:hover {
    background: linear-gradient(45deg, #f4d03f, #d4af37);
    transform: translateY(-2px);
}

.buy-btn.disabled {
    background: #666;
    color: #999;
    cursor: not-allowed;
}

.buy-btn.disabled:hover {
    transform: none;
}

.no-items {
    grid-column: 1 / -1;
    text-align: center;
    padding: 3rem;
    background: rgba(0, 0, 0, 0.7);
    border-radius: 10px;
    border: 1px solid #d4af37;
}

.no-items h3 {
    color: #d4af37;
    margin-bottom: 1rem;
}

.no-items p {
    color: #ccc;
}

/* Account Balance Card */
.account-balance .card-content {
    padding: 1rem;
}

.balance-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.5rem 0;
    border-bottom: 1px solid rgba(212, 175, 55, 0.2);
}

.balance-item:last-child {
    border-bottom: none;
}

.balance-type {
    color: #ccc;
    font-weight: 500;
}

.balance-amount {
    color: #d4af37;
    font-weight: bold;
    font-size: 1.1rem;
}

.balance-info {
    margin-top: 1rem;
    padding-top: 1rem;
    border-top: 1px solid rgba(212, 175, 55, 0.2);
    text-align: center;
}

.balance-info small {
    color: #999;
    font-style: italic;
}

