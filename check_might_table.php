<?php
require_once "db.php";

echo "=== CHECKING FOR MIGHT TABLE ===\n";

// Show all tables
echo "All tables in database:\n";
$result = $conn->query("SHOW TABLES");
$tables = [];
if ($result) {
    while ($row = $result->fetch_array()) {
        $tables[] = $row[0];
        echo "- " . $row[0] . "\n";
    }
} else {
    echo "Error: " . $conn->error . "\n";
}

// Check specifically for might table
echo "\n=== CHECKING MIGHT TABLE ===\n";
if (in_array('might', $tables)) {
    echo "✓ Found 'might' table!\n";
    
    // Show structure
    echo "\nMight table structure:\n";
    $result = $conn->query("DESCRIBE might");
    if ($result) {
        while ($row = $result->fetch_assoc()) {
            echo "- {$row['Field']}: {$row['Type']}\n";
        }
    }
    
    // Show sample data
    echo "\nSample might data:\n";
    $result = $conn->query("SELECT * FROM might LIMIT 5");
    if ($result && $result->num_rows > 0) {
        while ($row = $result->fetch_assoc()) {
            print_r($row);
        }
    } else {
        echo "No data in might table\n";
    }
} else {
    echo "✗ No 'might' table found\n";
    
    // Check for similar tables
    echo "\nLooking for similar tables:\n";
    foreach ($tables as $table) {
        if (stripos($table, 'might') !== false || 
            stripos($table, 'point') !== false || 
            stripos($table, 'balance') !== false ||
            stripos($table, 'currency') !== false) {
            echo "- Found similar: $table\n";
        }
    }
}

// Check account_data table structure to see account_id field
echo "\n=== CHECKING ACCOUNT_DATA TABLE ===\n";
if (in_array('account_data', $tables)) {
    echo "Account_data table structure:\n";
    $result = $conn->query("DESCRIBE account_data");
    if ($result) {
        while ($row = $result->fetch_assoc()) {
            echo "- {$row['Field']}: {$row['Type']}\n";
        }
    }
}

$conn->close();
?>
