<?php
// Setup the points system for donations
require_once "db.php";

echo "Setting up points system...\n";

// Read and execute the SQL file
$sql = file_get_contents('create_points_system.sql');

// Split the SQL into individual statements
$statements = explode(';', $sql);

foreach ($statements as $statement) {
    $statement = trim($statement);
    if (!empty($statement)) {
        if ($conn->query($statement) === TRUE) {
            echo "✓ Executed: " . substr($statement, 0, 50) . "...\n";
        } else {
            echo "✗ Error: " . $conn->error . "\n";
            echo "Statement: " . substr($statement, 0, 100) . "...\n";
        }
    }
}

echo "\n✓ Points system setup completed!\n";
echo "Players can now receive donation points and spend them in the shop.\n";

$conn->close();
?>
