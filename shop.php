<?php
session_start();
require_once "db.php";

// Check if user is logged in
if (!isset($_SESSION["username"])) {
    header("Location: login.php");
    exit;
}

// Get account info
$accountQuery = "SELECT id, name, membership FROM account_data WHERE name = ?";
$stmt = $conn->prepare($accountQuery);
$stmt->bind_param("s", $_SESSION["username"]);
$stmt->execute();
$accountResult = $stmt->get_result();
$account = $accountResult->fetch_assoc();

if (!$account) {
    header("Location: login.php");
    exit;
}

// Get account might points (create record if it doesn't exist)
$mightQuery = "SELECT might FROM might WHERE account_id = ?";
$stmt = $conn->prepare($mightQuery);
$stmt->bind_param("i", $account['id']);
$stmt->execute();
$mightResult = $stmt->get_result();

if ($mightResult->num_rows > 0) {
    $mightData = $mightResult->fetch_assoc();
    $userPoints = $mightData['might'];
} else {
    // Create might record with 0 points if it doesn't exist
    $insertMightQuery = "INSERT INTO might (account_id, might) VALUES (?, 0)";
    $stmt = $conn->prepare($insertMightQuery);
    $stmt->bind_param("i", $account['id']);
    $stmt->execute();
    $userPoints = 0;
}

// Get player characters for selection
$charactersQuery = "SELECT id, name, player_class, exp FROM players WHERE account_id = ? ORDER BY last_online DESC";
$stmt = $conn->prepare($charactersQuery);
$stmt->bind_param("i", $account['id']);
$stmt->execute();
$charactersResult = $stmt->get_result();
$characters = [];
while ($char = $charactersResult->fetch_assoc()) {
    $characters[] = $char;
}

// Get categories for filtering
$categoriesQuery = "SELECT * FROM shop_categories WHERE is_active = 1 ORDER BY display_order";
$categoriesResult = $conn->query($categoriesQuery);
$categories = [];
if ($categoriesResult && $categoriesResult->num_rows > 0) {
    while ($cat = $categoriesResult->fetch_assoc()) {
        $categories[] = $cat;
    }
}

// Get selected category from URL parameter
$selectedCategory = isset($_GET['category']) ? intval($_GET['category']) : 0;

// Get shop items with category filtering
$itemsQuery = "
    SELECT si.id, si.item_id, si.name, si.description, si.price, si.quantity, si.image_url, si.rarity, si.category_id,
           sc.name as category_name, sc.icon as category_icon
    FROM shop_items si
    LEFT JOIN shop_categories sc ON si.category_id = sc.id
    WHERE si.id > 0
";

if ($selectedCategory > 0) {
    $itemsQuery .= " AND si.category_id = " . $selectedCategory;
}

$itemsQuery .= " ORDER BY si.category_id, si.quantity ASC, si.price ASC";

$itemsResult = $conn->query($itemsQuery);

// Store items in array to prevent multiple database calls
$items = [];
if ($itemsResult && $itemsResult->num_rows > 0) {
    while ($row = $itemsResult->fetch_assoc()) {
        $items[] = $row;
    }
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Shop - Aion-Blitz Server</title>
    <style>
        /* Minimal clean styles for shop */
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: Arial, sans-serif;
            background: #1a1a1a;
            color: #fff;
            min-height: 100vh;
            opacity: 0;
            transition: opacity 0.3s ease;
        }

        body.loaded {
            opacity: 1;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }

        .top-nav {
            background: rgba(0, 0, 0, 0.9);
            padding: 15px 0;
            border-bottom: 1px solid #333;
        }

        .nav-content {
            max-width: 1200px;
            margin: 0 auto;
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0 20px;
        }

        .nav-menu {
            display: flex;
            list-style: none;
            gap: 20px;
        }

        .nav-menu a {
            color: #fff;
            text-decoration: none;
            padding: 8px 12px;
        }

        .nav-menu a:hover, .nav-menu a.active {
            color: #d4af37;
        }

        .user-section {
            color: #fff;
        }

        .logout-btn {
            color: #d4af37;
            text-decoration: none;
            margin-left: 15px;
        }

        .shop-header {
            margin: 30px 0;
            padding: 20px;
            background: rgba(0, 0, 0, 0.7);
            border-radius: 8px;
            border: 1px solid #d4af37;
        }

        .shop-header h1 {
            color: #d4af37;
            margin: 0 0 15px 0;
            text-align: center;
        }

        .shop-controls {
            display: flex;
            justify-content: space-between;
            align-items: center;
            flex-wrap: wrap;
            gap: 15px;
        }

        .recipient-selection {
            display: flex;
            flex-direction: column;
            gap: 15px;
        }

        .recipient-type {
            display: flex;
            gap: 20px;
            align-items: center;
        }

        .recipient-type label {
            color: #fff;
            font-weight: 600;
            display: flex;
            align-items: center;
            gap: 5px;
            cursor: pointer;
        }

        .recipient-type input[type="radio"] {
            margin: 0;
        }

        .recipient-option {
            display: flex;
            flex-direction: column;
            gap: 5px;
        }

        .recipient-option label {
            color: #fff;
            font-weight: 600;
        }

        .recipient-option select,
        .recipient-option input[type="text"] {
            padding: 8px 12px;
            border-radius: 5px;
            border: 1px solid #d4af37;
            background: rgba(0, 0, 0, 0.8);
            color: #fff;
            font-size: 14px;
            max-width: 300px;
        }

        .recipient-option small {
            color: #ccc;
            font-style: italic;
        }

        .user-balance {
            color: #d4af37;
            font-weight: bold;
            font-size: 1.2rem;
        }

        .category-filters {
            margin: 20px 0;
            display: flex;
            flex-wrap: wrap;
            gap: 10px;
            justify-content: center;
        }

        .category-btn {
            background: rgba(0, 0, 0, 0.8);
            border: 2px solid #333;
            color: #fff;
            padding: 10px 20px;
            border-radius: 25px;
            text-decoration: none;
            font-weight: 600;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .category-btn:hover {
            border-color: #d4af37;
            color: #d4af37;
            transform: translateY(-2px);
        }

        .category-btn.active {
            background: #d4af37;
            border-color: #d4af37;
            color: #000;
        }

        .shop-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
            gap: 20px;
            margin-top: 20px;
        }

        .shop-item {
            background: rgba(0, 0, 0, 0.8);
            border: 2px solid #333;
            border-radius: 8px;
            padding: 20px;
            text-align: center;
            min-height: 300px;
            display: flex;
            flex-direction: column;
            justify-content: space-between;
            position: relative;
        }

        .shop-item.common { border-color: #9d9d9d; }
        .shop-item.superior { border-color: #1eff00; }
        .shop-item.heroic { border-color: #0070dd; }
        .shop-item.legendary { border-color: #ff8000; }

        .item-image {
            margin-bottom: 15px;
            height: 120px;
            display: flex;
            align-items: center;
            justify-content: center;
            overflow: hidden;
        }

        .item-placeholder {
            width: 64px;
            height: 64px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 32px;
            border-radius: 8px;
            background: rgba(255, 255, 255, 0.1);
            border: 2px solid #333;
        }

        .item-placeholder.common { border-color: #9d9d9d; background: rgba(157, 157, 157, 0.1); }
        .item-placeholder.superior { border-color: #1eff00; background: rgba(30, 255, 0, 0.1); }
        .item-placeholder.heroic { border-color: #0070dd; background: rgba(0, 112, 221, 0.1); }
        .item-placeholder.legendary { border-color: #ff8000; background: rgba(255, 128, 0, 0.1); }
        .item-placeholder.mythic { border-color: #e6cc80; background: rgba(230, 204, 128, 0.1); }

        .rarity-badge {
            background: #d4af37;
            color: #000;
            padding: 3px 8px;
            border-radius: 4px;
            font-size: 0.8rem;
            font-weight: bold;
            display: inline-block;
            margin-bottom: 10px;
        }

        .item-name {
            color: #d4af37;
            font-size: 1.1rem;
            margin-bottom: 10px;
        }

        .item-description {
            color: #ccc;
            margin-bottom: 10px;
            font-size: 0.9rem;
        }

        .item-quantity {
            color: #4CAF50;
            font-weight: bold;
            margin-bottom: 10px;
            font-size: 1rem;
        }

        .item-price {
            color: #d4af37;
            font-weight: bold;
            font-size: 1.2rem;
            margin-bottom: 15px;
        }

        .buy-btn {
            background: #d4af37;
            color: #000;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            font-weight: bold;
            cursor: pointer;
            width: 100%;
        }

        .buy-btn:hover {
            background: #f4d03f;
        }

        .buy-btn.disabled {
            background: #666;
            color: #999;
            cursor: not-allowed;
        }

        .no-items {
            grid-column: 1 / -1;
            text-align: center;
            padding: 40px;
            background: rgba(0, 0, 0, 0.7);
            border-radius: 8px;
            border: 1px solid #d4af37;
        }

        .no-items h3 {
            color: #d4af37;
            margin-bottom: 15px;
        }

        /* Responsive design for mobile */
        @media (max-width: 768px) {
            .shop-controls {
                flex-direction: column;
                align-items: stretch;
                text-align: center;
            }

            .recipient-selection {
                align-items: center;
                margin-bottom: 10px;
            }

            .recipient-type {
                flex-direction: column;
                gap: 10px;
            }

            .recipient-option select,
            .recipient-option input[type="text"] {
                width: 100%;
                max-width: 300px;
            }
        }
    </style>
</head>
<body>
    <!-- Top Navigation -->
    <nav class="top-nav">
        <div class="nav-content">
            <ul class="nav-menu">
                <li><a href="index.php">🏠 Home</a></li>
                <li><a href="dashboard.php">👤 Dashboard</a></li>
                <li><a href="shop.php" class="active">🛒 Shop</a></li>
                <li><a href="ranks.php">🏆 Ranks</a></li>
            </ul>
            <div class="user-section">
                <span>Welcome, <?php echo htmlspecialchars($_SESSION["username"]); ?>!</span>
                <a href="logout.php" class="logout-btn">Logout</a>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <div class="container">
            <div class="shop-header">
                <h1>🛒 Might Shop</h1>
                <div class="shop-controls">
                    <div class="recipient-selection">
                        <div class="recipient-type">
                            <label>
                                <input type="radio" name="recipient-type" value="self" checked onchange="toggleRecipientType()">
                                Send to my character
                            </label>
                            <label>
                                <input type="radio" name="recipient-type" value="gift" onchange="toggleRecipientType()">
                                🎁 Gift to another player
                            </label>
                        </div>

                        <div id="self-selection" class="recipient-option">
                            <label for="character-select">My character:</label>
                            <select id="character-select">
                                <?php if (!empty($characters)): ?>
                                    <?php foreach ($characters as $char): ?>
                                        <option value="<?php echo $char['id']; ?>">
                                            <?php echo htmlspecialchars($char['name']); ?> (<?php echo htmlspecialchars($char['player_class']); ?>)
                                        </option>
                                    <?php endforeach; ?>
                                <?php else: ?>
                                    <option value="">No characters found</option>
                                <?php endif; ?>
                            </select>
                        </div>

                        <div id="gift-selection" class="recipient-option" style="display: none;">
                            <label for="gift-character">Recipient character name:</label>
                            <input type="text" id="gift-character" placeholder="Enter character name" maxlength="50">
                            <small>Enter the exact character name (case-sensitive)</small>
                        </div>
                    </div>
                    <div class="user-balance">
                        <span class="balance-label">Your Balance:</span>
                        <span class="balance-amount"><?php echo number_format($userPoints); ?> MP</span>
                    </div>
                </div>
            </div>

            <!-- Category Filters -->
            <?php if (!empty($categories)): ?>
                <div class="category-filters">
                    <a href="shop.php" class="category-btn <?php echo $selectedCategory == 0 ? 'active' : ''; ?>">
                        🛒 All Items
                    </a>
                    <?php foreach ($categories as $category): ?>
                        <a href="shop.php?category=<?php echo $category['id']; ?>"
                           class="category-btn <?php echo $selectedCategory == $category['id'] ? 'active' : ''; ?>">
                            <?php
                            // Convert text icons to emojis
                            $icon = $category['icon'];
                            switch($icon) {
                                case 'sword': $icon = '⚔️'; break;
                                case 'shirt': $icon = '👗'; break;
                                case 'potion': $icon = '🧪'; break;
                                default: $icon = '📦'; break;
                            }
                            echo $icon;
                            ?> <?php echo htmlspecialchars($category['name']); ?>
                        </a>
                    <?php endforeach; ?>
                </div>
            <?php endif; ?>

            <div class="shop-grid">
                <?php if (empty($characters)): ?>
                    <div class="no-items">
                        <h3>🚧 No Characters Found!</h3>
                        <p>You need to create a character in-game before you can purchase items.</p>
                        <p>Items are delivered to your character's in-game mail.</p>
                    </div>
                <?php elseif (!empty($items)): ?>
                    <?php foreach ($items as $item): ?>
                        <div class="shop-item <?php echo strtolower(htmlspecialchars($item['rarity'])); ?>">
                            <div class="rarity-badge"><?php echo htmlspecialchars($item['rarity']); ?></div>
                            <div class="item-image">
                                <?php
                                // Handle both old format (/images/items/file.jpg) and new format (images/items/file.jpg)
                                $image_url = $item['image_url'];
                                if (strpos($image_url, '/') === 0) {
                                    $image_url = substr($image_url, 1); // Remove leading slash
                                }

                                $has_custom_image = !empty($image_url) &&
                                                   $image_url !== 'default' &&
                                                   file_exists($image_url);

                                if ($has_custom_image): ?>
                                    <img src="<?php echo htmlspecialchars($image_url); ?>"
                                         alt="<?php echo htmlspecialchars($item['name']); ?>"
                                         style="max-width: 100%; max-height: 100%; object-fit: contain; border-radius: 8px;"
                                         loading="lazy">
                                <?php else: ?>
                                    <div class="item-placeholder <?php echo strtolower(htmlspecialchars($item['rarity'])); ?>">
                                        <?php
                                        // Simple icons based on item type
                                        $itemName = strtolower($item['name']);
                                        if (strpos($itemName, 'kinah') !== false) echo '💰';
                                        elseif (strpos($itemName, 'potion') !== false) echo '🧪';
                                        elseif (strpos($itemName, 'weapon') !== false) echo '⚔️';
                                        elseif (strpos($itemName, 'stone') !== false) echo '💎';
                                        elseif (strpos($itemName, 'wing') !== false) echo '🪶';
                                        else echo '📦';
                                        ?>
                                    </div>
                                <?php endif; ?>
                            </div>
                            <h3 class="item-name"><?php echo htmlspecialchars($item['name']); ?></h3>
                            <p class="item-description"><?php echo htmlspecialchars($item['description']); ?></p>
                            <div class="item-quantity">Quantity: <?php echo number_format($item['quantity']); ?></div>
                            <div class="item-price"><?php echo number_format($item['price']); ?> MP</div>
                            <?php if ($userPoints >= $item['price']): ?>
                                <button class="buy-btn" onclick="buyItem(<?php echo intval($item['id']); ?>, '<?php echo addslashes(htmlspecialchars($item['name'])); ?>', <?php echo intval($item['price']); ?>, <?php echo intval($item['quantity']); ?>)">
                                    💰 Purchase (<?php echo $item['quantity']; ?>x)
                                </button>
                            <?php else: ?>
                                <button class="buy-btn disabled" disabled>
                                    💸 Insufficient Points
                                </button>
                            <?php endif; ?>
                        </div>
                    <?php endforeach; ?>
                <?php else: ?>
                    <div class="no-items">
                        <h3>🚧 Shop Coming Soon!</h3>
                        <p>Items will be available for purchase soon. Check back later!</p>
                    </div>
                <?php endif; ?>
            </div>
        </div>

    <script>
        // Prevent flickering by showing page only when fully loaded
        document.addEventListener('DOMContentLoaded', function() {
            document.body.classList.add('loaded');
        });

        // Toggle between self and gift recipient selection
        function toggleRecipientType() {
            var recipientType = document.querySelector('input[name="recipient-type"]:checked').value;
            var selfSelection = document.getElementById('self-selection');
            var giftSelection = document.getElementById('gift-selection');

            if (recipientType === 'self') {
                selfSelection.style.display = 'flex';
                giftSelection.style.display = 'none';
            } else {
                selfSelection.style.display = 'none';
                giftSelection.style.display = 'flex';
            }
        }

        // Function to update purchase buttons based on current balance
        function updatePurchaseButtons(currentBalance) {
            var shopItems = document.querySelectorAll('.shop-item');
            shopItems.forEach(function(item) {
                var button = item.querySelector('.buy-btn');
                var priceText = item.querySelector('.item-price').textContent;
                var price = parseInt(priceText.replace(/[^\d]/g, ''));

                if (currentBalance >= price) {
                    button.disabled = false;
                    button.className = 'buy-btn';
                    button.innerHTML = button.innerHTML.replace('💸 Insufficient Points', '💰 Purchase');
                } else {
                    button.disabled = true;
                    button.className = 'buy-btn disabled';
                    button.innerHTML = button.innerHTML.replace('💰 Purchase', '💸 Insufficient Points');
                }
            });
        }

        function buyItem(itemId, itemName, price, quantity) {
            // Get recipient type
            var recipientType = document.querySelector('input[name="recipient-type"]:checked').value;
            var recipientData = {};
            var confirmMessage = '';

            if (recipientType === 'self') {
                // Send to own character
                var characterSelect = document.getElementById('character-select');
                var selectedCharacterId = characterSelect.value;
                var selectedCharacterName = characterSelect.options[characterSelect.selectedIndex].text;

                if (!selectedCharacterId) {
                    alert('Please select a character to receive the item.');
                    return;
                }

                recipientData = {
                    type: 'self',
                    character_id: selectedCharacterId
                };
                confirmMessage = 'Purchase "' + itemName + '" for ' + price + ' MP?\nItem will be sent to: ' + selectedCharacterName;

            } else {
                // Gift to another player
                var giftCharacterName = document.getElementById('gift-character').value.trim();

                if (!giftCharacterName) {
                    alert('Please enter the recipient character name.');
                    return;
                }

                recipientData = {
                    type: 'gift',
                    character_name: giftCharacterName
                };
                confirmMessage = 'Purchase "' + itemName + '" for ' + price + ' MP as a GIFT?\nRecipient: ' + giftCharacterName + '\n\nNote: This will spend YOUR might points to send an item to another player.';
            }

            if (confirm(confirmMessage)) {
                var xhr = new XMLHttpRequest();
                xhr.open('POST', 'purchase_item.php', true);
                xhr.setRequestHeader('Content-Type', 'application/json');

                xhr.onreadystatechange = function() {
                    if (xhr.readyState === 4) {
                        if (xhr.status === 200) {
                            try {
                                var data = JSON.parse(xhr.responseText);
                                if (data.success) {
                                    alert('Purchase successful! Check your in-game mail for the item.');

                                    // Update balance display immediately
                                    if (data.remaining_points !== undefined) {
                                        var balanceElement = document.querySelector('.balance-amount');
                                        if (balanceElement) {
                                            balanceElement.textContent = data.remaining_points.toLocaleString() + ' MP';
                                        }

                                        // Update all purchase buttons based on new balance
                                        updatePurchaseButtons(data.remaining_points);
                                    }

                                    // No need to reload - everything is updated dynamically
                                } else {
                                    alert(data.message || 'Purchase failed. Please try again.');
                                }
                            } catch (e) {
                                console.error('JSON parse error:', e);
                                alert('Server response error. Please try again.');
                            }
                        } else {
                            alert('Network error (Status: ' + xhr.status + '). Please try again.');
                        }
                    }
                };

                xhr.send(JSON.stringify({
                    item_id: itemId,
                    price: price,
                    quantity: quantity,
                    recipient: recipientData
                }));
            }
        }
    </script>
</body>
</html>
