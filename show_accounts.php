<?php
echo "Showing available accounts...\n";

require_once "db.php";

// Test database connection
if ($conn->connect_error) {
    die("Connection failed: " . $conn->connect_error);
}

echo "✓ Database connection successful!\n\n";

// Show all accounts
$accountsQuery = "SELECT id, name, membership FROM account_data ORDER BY id";
$result = $conn->query($accountsQuery);

if ($result && $result->num_rows > 0) {
    echo "Available accounts:\n";
    echo "ID\tUsername\tMembership\n";
    echo "---\t--------\t----------\n";
    
    while ($row = $result->fetch_assoc()) {
        echo "{$row['id']}\t{$row['name']}\t{$row['membership']}\n";
    }
    
    echo "\nTotal accounts: " . $result->num_rows . "\n";
} else {
    echo "No accounts found in the database.\n";
}

$conn->close();
?>
