<?php
require_once "db.php";

// Create database connection
$conn = new mysqli($servername, $username, $password, $dbname);
if ($conn->connect_error) {
    die("Connection failed: " . $conn->connect_error);
}

// Get player name from URL parameter
$playerName = isset($_GET['name']) ? $_GET['name'] : '';

if (empty($playerName)) {
    header("Location: ranks.php");
    exit;
}

// Function to calculate level from experience
function calculateLevel($exp) {
    if ($exp < 1000) return 1;
    if ($exp < 5000) return 10;
    if ($exp < 50000) return 20;
    if ($exp < 200000) return 30;
    if ($exp < 500000) return 40;
    if ($exp < 1000000) return 50;
    if ($exp < 2000000) return 60;
    return 65;
}

// Function to get rank name from rank number
function getRankName($rank) {
    $ranks = [
        1 => 'Soldier', 2 => 'Soldier 1st Class', 3 => 'Senior Soldier', 4 => 'Corporal',
        5 => 'Sergeant', 6 => 'Master Sergeant', 7 => 'Warrant Officer', 8 => 'Lieutenant',
        9 => 'Captain', 10 => 'Major', 11 => 'Lieutenant Colonel', 12 => 'Colonel',
        13 => 'Brigadier General', 14 => 'Major General', 15 => 'Lieutenant General',
        16 => 'General', 17 => 'Great General', 18 => 'Commander'
    ];
    return isset($ranks[$rank]) ? $ranks[$rank] : 'Soldier';
}

// Function to format numbers
function formatNumber($number) {
    if ($number >= 1000000) return number_format($number / 1000000, 1) . 'M';
    elseif ($number >= 1000) return number_format($number / 1000, 1) . 'K';
    return number_format($number);
}

// Get player basic information
$playerQuery = "
    SELECT 
        p.id,
        p.name,
        p.player_class,
        p.race,
        p.gender,
        p.exp,
        p.creation_date,
        p.last_online,
        p.online,
        p.title_id,
        p.world_id,
        ar.gp,
        ar.ap,
        ar.all_kill,
        ar.daily_kill,
        ar.weekly_kill,
        ar.rank as abyss_rank,
        ar.rank_pos,
        ar.daily_ap,
        ar.weekly_ap,
        ar.daily_gp,
        ar.weekly_gp,
        lm.nickname as legion_name,
        lm.rank as legion_rank,
        pls.hp,
        pls.mp,
        pls.fp
    FROM players p
    LEFT JOIN abyss_rank ar ON p.id = ar.player_id
    LEFT JOIN legion_members lm ON p.id = lm.player_id
    LEFT JOIN player_life_stats pls ON p.id = pls.player_id
    WHERE p.name = ?
";

$stmt = $conn->prepare($playerQuery);
$stmt->bind_param("s", $playerName);
$stmt->execute();
$playerResult = $stmt->get_result();

if ($playerResult->num_rows === 0) {
    header("Location: ranks.php");
    exit;
}

$player = $playerResult->fetch_assoc();

// Get player equipment (equipped items)
$equipmentQuery = "
    SELECT 
        i.item_id,
        i.item_count,
        i.item_color,
        i.slot,
        i.item_creation_time,
        i.is_soul_bound
    FROM inventory i
    WHERE i.item_owner = ? AND i.is_equipped = 1
    ORDER BY i.slot
";

$stmt = $conn->prepare($equipmentQuery);
$stmt->bind_param("i", $player['id']);
$stmt->execute();
$equipmentResult = $stmt->get_result();

// Get player titles
$titlesQuery = "
    SELECT 
        pt.title_id,
        pt.remaining
    FROM player_titles pt
    WHERE pt.player_id = ?
    ORDER BY pt.title_id
";

$stmt = $conn->prepare($titlesQuery);
$stmt->bind_param("i", $player['id']);
$stmt->execute();
$titlesResult = $stmt->get_result();

// Get recent quests (completed)
$questsQuery = "
    SELECT 
        pq.quest_id,
        pq.status,
        pq.complete_count,
        pq.complete_time
    FROM player_quests pq
    WHERE pq.player_id = ? AND pq.status = 'COMPLETE'
    ORDER BY pq.complete_time DESC
    LIMIT 10
";

$stmt = $conn->prepare($questsQuery);
$stmt->bind_param("i", $player['id']);
$stmt->execute();
$questsResult = $stmt->get_result();

// Calculate some statistics
$level = calculateLevel($player['exp']);
$rankName = getRankName($player['abyss_rank']);
$onlineStatus = $player['online'] ? 'Online' : 'Offline';
$lastOnline = $player['last_online'] ? date('Y-m-d H:i', strtotime($player['last_online'])) : 'Never';
$memberSince = date('Y-m-d', strtotime($player['creation_date']));

$conn->close();
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo htmlspecialchars($player['name']); ?> - Player Profile - Aion-Blitz</title>
    <link rel="stylesheet" href="styles.css">
    <link href="https://fonts.googleapis.com/css2?family=Cinzel:wght@400;600;700&family=Open+Sans:wght@300;400;600&display=swap" rel="stylesheet">
</head>
<body>
    <!-- Video Background -->
    <div class="video-background">
        <video autoplay muted loop playsinline>
            <source src="images/bg_main.mp4" type="video/mp4">
        </video>
    </div>
    <div class="video-overlay"></div>

    <!-- Top Navigation -->
    <nav class="top-nav">
        <div class="nav-left">
            <span class="server-time">🕐 Server Time: 00:00</span>
            <span class="server-status">🟢 Server Status</span>
        </div>
        <div class="nav-center">
            <ul class="nav-menu">
                <li><a href="index.php">🏠 Home</a></li>
                <li><a href="index.php#faq">❓ F.A.Q</a></li>
                <li><a href="register.php">📝 Register</a></li>
                <li><a href="index.php#schedule">📅 Schedule</a></li>
                <li><a href="ranks.php">🏆 Ranks</a></li>
                <li><a href="activity.php">📊 Activity</a></li>
                <li><a href="pvp.php">⚔️ PvP</a></li>
                <li><a href="index.php#discord">💬 Discord</a></li>
            </ul>
        </div>
        <div class="nav-right">
            <div class="user-section">
                <?php
                session_start();
                if (isset($_SESSION["username"])) {
                    echo "<span class='welcome-user'>Welcome, " . $_SESSION["username"] . "!</span>";
                    echo "<a href='logout.php' class='logout-btn'>Logout</a>";
                } else {
                    echo "<a href='signin.php' class='signin-btn'>Sign In</a>";
                }
                ?>
            </div>
        </div>
    </nav>

    <!-- Player Profile Section -->
    <section class="player-profile-section">
        <div class="profile-container">
            <!-- Back Button -->
            <div class="profile-header">
                <a href="ranks.php" class="back-btn">← Back to Rankings</a>
                <h1>Player Profile</h1>
            </div>

            <!-- Player Basic Info -->
            <div class="profile-main">
                <div class="player-card">
                    <div class="player-avatar">
                        <div class="avatar-placeholder">
                            <?php echo strtoupper(substr($player['name'], 0, 2)); ?>
                        </div>
                        <div class="online-indicator <?php echo $player['online'] ? 'online' : 'offline'; ?>"></div>
                    </div>
                    
                    <div class="player-info">
                        <h2 class="player-name"><?php echo htmlspecialchars($player['name']); ?></h2>
                        <div class="player-details">
                            <span class="player-class"><?php echo $player['player_class']; ?></span>
                            <span class="player-race"><?php echo $player['race']; ?></span>
                            <span class="player-gender"><?php echo $player['gender']; ?></span>
                        </div>
                        <div class="player-level">Level <?php echo $level; ?></div>
                        <div class="player-status">
                            <span class="status <?php echo $player['online'] ? 'online' : 'offline'; ?>">
                                <?php echo $onlineStatus; ?>
                            </span>
                            <?php if (!$player['online']): ?>
                                <span class="last-seen">Last seen: <?php echo $lastOnline; ?></span>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>

                <!-- Stats Grid -->
                <div class="stats-grid">
                    <div class="stat-card">
                        <div class="stat-icon">👑</div>
                        <div class="stat-info">
                            <div class="stat-value"><?php echo formatNumber($player['gp']); ?></div>
                            <div class="stat-label">Glory Points</div>
                            <div class="stat-sub">Daily: <?php echo formatNumber($player['daily_gp']); ?></div>
                        </div>
                    </div>

                    <div class="stat-card">
                        <div class="stat-icon">⚔️</div>
                        <div class="stat-info">
                            <div class="stat-value"><?php echo formatNumber($player['ap']); ?></div>
                            <div class="stat-label">Abyss Points</div>
                            <div class="stat-sub">Daily: <?php echo formatNumber($player['daily_ap']); ?></div>
                        </div>
                    </div>

                    <div class="stat-card">
                        <div class="stat-icon">💀</div>
                        <div class="stat-info">
                            <div class="stat-value"><?php echo formatNumber($player['all_kill']); ?></div>
                            <div class="stat-label">Total Kills</div>
                            <div class="stat-sub">Daily: <?php echo formatNumber($player['daily_kill']); ?></div>
                        </div>
                    </div>

                    <div class="stat-card">
                        <div class="stat-icon">🏅</div>
                        <div class="stat-info">
                            <div class="stat-value"><?php echo $rankName; ?></div>
                            <div class="stat-label">Abyss Rank</div>
                            <div class="stat-sub">Position: #<?php echo $player['rank_pos']; ?></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <script src="script.js"></script>
</body>
</html>
