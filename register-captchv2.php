<?php
require_once "db.php";

$conn = new mysqli($servername, $username, $password, $dbname);
if ($conn->connect_error) {
    die("Connection failed: " . $conn->connect_error);
}

// Process form submission
$name = isset($_POST["name"]) ? $_POST["name"] : "";
$password = isset($_POST["password"]) ? $_POST["password"] : "";

$successMessage = "";

if ($_SERVER["REQUEST_METHOD"] == "POST" && !empty($name) && !empty($password)) {
    // Verify the reCAPTCHA response
    if (!verifyCaptchaResponse()) {
        die("Error: CAPTCHA verification failed.");
    }

    // Generate the next available ID
    $nextIDQuery = "SELECT MAX(id) AS max_id FROM account_data";
    $result = $conn->query($nextIDQuery);

    if ($result === FALSE) {
        die("Error: " . $conn->error);
    }

    $row = $result->fetch_assoc();
    $nextID = $row["max_id"] + 1;

    // Hash the password
    $hashedPassword = encryptPassword($password);

    // Insert data into the database
    $insertQuery = "INSERT INTO account_data (id, name, password, activated)
                    VALUES ($nextID, '$name', '$hashedPassword', 1)";

    if ($conn->query($insertQuery) === TRUE) {
        // Registration successful, set the success message
        $successMessage = "Registration Successful!";
    } else {
        $successMessage = "Error: Registration Failed";
    }
}

$conn->close();

// Function to hash the password
function encryptPassword($password) {
    $digest = sha1($password, true);
    $encodedDigest = base64_encode($digest);
    return $encodedDigest;
}

// Function to verify the reCAPTCHA response
function verifyCaptchaResponse() {
    $recaptchaSecretKey = "6LdcD1QmAAAAAHujZI7P6kjpyxIz-YoSVWLzf1CP";
    $recaptchaResponse = isset($_POST['g-recaptcha-response']) ? $_POST['g-recaptcha-response'] : '';

    $recaptchaUrl = "https://www.google.com/recaptcha/api/siteverify";
    $recaptchaData = array(
        'secret' => $recaptchaSecretKey,
        'response' => $recaptchaResponse
    );

    $recaptchaOptions = array(
        'http' => array(
            'header' => "Content-type: application/x-www-form-urlencoded\r\n",
            'method' => 'POST',
            'content' => http_build_query($recaptchaData),
        ),
    );

    $recaptchaContext = stream_context_create($recaptchaOptions);
    $recaptchaResult = file_get_contents($recaptchaUrl, false, $recaptchaContext);

    if ($recaptchaResult === FALSE) {
        return false;
    }

    $recaptchaResultData = json_decode($recaptchaResult, true);

    return $recaptchaResultData['success'];
}
?>

<!DOCTYPE html>
<html>
<head>
    <title>User Registration</title>
    <link rel="stylesheet" href="styles.css">
    <script src="https://www.google.com/recaptcha/api.js" async defer></script>
</head>
<body>
    <nav>
        <ul>
            <li><a href="index.php">Home</a></li>
            <li><a href="discord.html">Discord</a></li>
            <li><a href="ranking.html">Ranking</a></li>
            <li><a href="forum.html">Forum</a></li>
            <li><a href="register.php">Register</a></li>
        </ul>
    </nav>
    <div class="register-container">
        <h1>User Registration</h1>
        <?php if (!empty($successMessage)) : ?>
            <p><?php echo $successMessage; ?></p>
        <?php endif; ?>
        <form method="POST" action="register.php">
            <label for="name">Name:</label>
            <input type="text" name="name" id="name" required><br>
            <label for="password">Password:</label>
            <input type="password" name="password" id="password" required><br>
          <div class="g-recaptcha" data-sitekey="6LdcD1QmAAAAAI92tLqUkEeycCm_i1YLrp0GukOi"></div> 
            <button type="submit">Register</button>
        </form>
        <p>Already have an account? <a href="signin.php" class="signin-link">Sign In</a></p>
    </div>
</body>
</html>

