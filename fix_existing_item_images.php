<?php
// Fix existing items that don't have proper image URLs
require_once "db.php";

echo "Fixing Existing Item Images\n";
echo "===========================\n";

// Check current state
$checkQuery = "SELECT id, name, image_url FROM shop_items";
$result = $conn->query($checkQuery);

echo "Current items and their image URLs:\n";
$items_to_fix = [];

if ($result->num_rows > 0) {
    while ($item = $result->fetch_assoc()) {
        echo "- ID {$item['id']}: {$item['name']} → {$item['image_url']}\n";
        
        // Check if image_url is empty, null, or points to non-existent file
        if (empty($item['image_url']) || 
            $item['image_url'] === '/images/items/default.png' || 
            $item['image_url'] === 'default.png' ||
            !file_exists('.' . $item['image_url'])) {
            $items_to_fix[] = $item['id'];
        }
    }
} else {
    echo "- No items found\n";
}

echo "\n";

if (!empty($items_to_fix)) {
    echo "Items that need fixing: " . count($items_to_fix) . "\n";
    
    // Update all problematic items to use the default SVG
    $updateQuery = "UPDATE shop_items SET image_url = '/images/items/default.svg' WHERE id IN (" . implode(',', array_map('intval', $items_to_fix)) . ")";
    
    if ($conn->query($updateQuery)) {
        echo "✓ Updated " . count($items_to_fix) . " items to use default image\n";
    } else {
        echo "✗ Error updating items: " . $conn->error . "\n";
    }
} else {
    echo "✓ All items already have valid image URLs\n";
}

echo "\n";

// Verify the fix
echo "Verification - Updated items:\n";
$verifyQuery = "SELECT id, name, image_url FROM shop_items";
$result = $conn->query($verifyQuery);

if ($result->num_rows > 0) {
    while ($item = $result->fetch_assoc()) {
        $status = file_exists('.' . $item['image_url']) ? '✓' : '✗';
        echo "- ID {$item['id']}: {$item['name']} → {$item['image_url']} $status\n";
    }
}

echo "\n✓ Image fix completed\n";

$conn->close();
?>
