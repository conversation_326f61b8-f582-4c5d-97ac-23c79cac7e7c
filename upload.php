<!DOCTYPE html>
<html>
<head>
    <title>File Upload with Progress Bar</title>
    <script src="https://ajax.googleapis.com/ajax/libs/jquery/3.5.1/jquery.min.js"></script>
    <style>
        #progressBar {
            width: 100%;
            background-color: #f5f5f5;
            border: 1px solid #ccc;
            padding: 5px;
        }
        #progress {
            width: 0%;
            height: 20px;
            background-color: #337ab7;
        }
    </style>
</head>
<body>
    <h2>File Upload with Progress Bar</h2>
    <form id="uploadForm" enctype="multipart/form-data">
        <input type="file" name="fileToUpload" id="fileToUpload">
        <input type="submit" value="Upload" name="submit">
    </form>
    <div id="progressBar">
        <div id="progress"></div>
    </div>
    <div id="uploadStatus"></div>

    <script>
        $(document).ready(function() {
            $('#uploadForm').submit(function(event) {
                event.preventDefault(); // Prevent default form submission

                var formData = new FormData($(this)[0]);

                $.ajax({
                    url: 'upload.php',
                    type: 'POST',
                    data: formData,
                    processData: false,
                    contentType: false,
                    xhr: function() {
                        var xhr = new window.XMLHttpRequest();

                        // Handle progress updates
                        xhr.upload.addEventListener('progress', function(event) {
                            if (event.lengthComputable) {
                                var percentComplete = event.loaded / event.total * 100;
                                $('#progress').css('width', percentComplete + '%');
                            }
                        }, false);

                        return xhr;
                    },
                    success: function(response) {
                        $('#uploadStatus').text(response);
                    }
                });
            });
        });
    </script>

    <?php
    $targetDirectory = 'D:/xampp/htdocs/tester/'; // Directory where the files will be stored
    
    if (isset($_FILES['fileToUpload'])) {
        $file = $_FILES['fileToUpload'];
        $fileName = basename($file['name']);
        $targetPath = $targetDirectory . $fileName;
    
        if (move_uploaded_file($file['tmp_name'], $targetPath)) {
            echo "File uploaded successfully.";
        } else {
            echo "Sorry, there was an error uploading your file.";
        }
    }
    ?>
</body>
</html>
