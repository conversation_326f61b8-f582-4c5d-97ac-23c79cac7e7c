<?php
echo "Adding test item to shop...\n";

require_once "db.php";

// Test database connection
if ($conn->connect_error) {
    die("Connection failed: " . $conn->connect_error);
}

echo "✓ Database connection successful!\n";
echo "Connected to database: $dbname\n";

// Check if shop_items table exists
$result = $conn->query("SHOW TABLES LIKE 'shop_items'");
if ($result->num_rows == 0) {
    echo "✗ shop_items table doesn't exist. Creating it...\n";
    
    // Create shop_items table
    $createTableQuery = "
    CREATE TABLE `shop_items` (
        `id` int(11) NOT NULL AUTO_INCREMENT,
        `item_id` int(11) NULL DEFAULT NULL,
        `name` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
        `description` text CHARACTER SET utf8 COLLATE utf8_general_ci NULL,
        `price` int(11) NOT NULL,
        `quantity` int(11) DEFAULT 1,
        `image_url` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
        `category_id` int(11) NULL DEFAULT 1,
        `rarity` enum('Common','Superior','Heroic','Fabled','Eternal','Mythic','Ancient','Legendary','Ultimate') CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT 'Common',
        `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
        PRIMARY KEY (`id`)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_general_ci;
    ";
    
    if ($conn->query($createTableQuery) === TRUE) {
        echo "✓ shop_items table created successfully!\n";
    } else {
        die("✗ Error creating table: " . $conn->error . "\n");
    }
}

// Check if shop_categories table exists
$result = $conn->query("SHOW TABLES LIKE 'shop_categories'");
if ($result->num_rows == 0) {
    echo "✗ shop_categories table doesn't exist. Creating it...\n";
    
    // Create shop_categories table
    $createCategoriesQuery = "
    CREATE TABLE `shop_categories` (
        `id` int(11) NOT NULL AUTO_INCREMENT,
        `name` varchar(100) NOT NULL,
        `icon` varchar(50) DEFAULT '📦',
        `display_order` int(11) DEFAULT 0,
        `is_active` tinyint(1) DEFAULT 1,
        PRIMARY KEY (`id`)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_general_ci;
    ";
    
    if ($conn->query($createCategoriesQuery) === TRUE) {
        echo "✓ shop_categories table created successfully!\n";
        
        // Add default categories
        $insertCategories = "
        INSERT INTO shop_categories (name, icon, display_order, is_active) VALUES
        ('Items', '📦', 1, 1),
        ('Skins', '👕', 2, 1),
        ('Consumables', '🧪', 3, 1)
        ";
        
        if ($conn->query($insertCategories) === TRUE) {
            echo "✓ Default categories added!\n";
        } else {
            echo "✗ Error adding categories: " . $conn->error . "\n";
        }
    } else {
        die("✗ Error creating categories table: " . $conn->error . "\n");
    }
}

// Clear existing items and add test item
echo "\nAdding test shop item...\n";

// First, let's see what's currently in the shop
$countResult = $conn->query("SELECT COUNT(*) as count FROM shop_items");
if ($countResult) {
    $count = $countResult->fetch_assoc()['count'];
    echo "Current items in shop: $count\n";
}

// Add a test item (your favorite item ID 164000073)
$testItem = [
    'item_id' => 164000073,
    'name' => 'Test Reward Item',
    'description' => 'A special test item to verify the shop is working! This item will be delivered to your character\'s in-game mail.',
    'price' => 10, // 10 Might Points
    'quantity' => 5,
    'image_url' => '/images/items/164000073.png',
    'category_id' => 1,
    'rarity' => 'Common'
];

// Check if item already exists
$checkQuery = "SELECT id FROM shop_items WHERE item_id = ? LIMIT 1";
$checkStmt = $conn->prepare($checkQuery);
$checkStmt->bind_param("i", $testItem['item_id']);
$checkStmt->execute();
$existingResult = $checkStmt->get_result();

if ($existingResult->num_rows > 0) {
    echo "✓ Test item already exists in shop!\n";
} else {
    // Insert the test item
    $insertQuery = "INSERT INTO shop_items (item_id, name, description, price, quantity, image_url, category_id, rarity) VALUES (?, ?, ?, ?, ?, ?, ?, ?)";
    $stmt = $conn->prepare($insertQuery);
    $stmt->bind_param("issiiiss", 
        $testItem['item_id'], 
        $testItem['name'], 
        $testItem['description'], 
        $testItem['price'], 
        $testItem['quantity'], 
        $testItem['image_url'], 
        $testItem['category_id'], 
        $testItem['rarity']
    );
    
    if ($stmt->execute()) {
        echo "✓ Test item added successfully!\n";
        echo "  - Item ID: {$testItem['item_id']}\n";
        echo "  - Name: {$testItem['name']}\n";
        echo "  - Price: {$testItem['price']} MP\n";
        echo "  - Quantity: {$testItem['quantity']}\n";
    } else {
        echo "✗ Error adding test item: " . $stmt->error . "\n";
    }
}

// Final count
$finalCountResult = $conn->query("SELECT COUNT(*) as count FROM shop_items");
if ($finalCountResult) {
    $finalCount = $finalCountResult->fetch_assoc()['count'];
    echo "\nFinal items in shop: $finalCount\n";
}

echo "\n✅ Shop setup complete!\n";
echo "Now go to your website dashboard and click on 'Visit Shop' to see the test item!\n";

$conn->close();
?>
