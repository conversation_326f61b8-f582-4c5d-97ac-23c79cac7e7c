<?php
// Disable the problematic trigger
require_once "db.php";

echo "Disabling problematic trigger...\n";

// Drop the trigger that's causing issues
$dropTrigger = "DROP TRIGGER IF EXISTS `trg_process_web_reward`";
if ($conn->query($dropTrigger) === TRUE) {
    echo "✓ Problematic trigger disabled successfully\n";
    echo "✓ Daily rewards will now be handled directly by PHP code\n";
} else {
    echo "✗ Error disabling trigger: " . $conn->error . "\n";
}

$conn->close();
?>
