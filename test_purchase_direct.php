<?php
// Direct test of purchase_item.php to see exact error
session_start();
$_SESSION["username"] = "test22"; // Simulate login

// Simulate the exact POST request that the shop sends
$_POST = []; // Clear POST
$_SERVER['REQUEST_METHOD'] = 'POST';
$_SERVER['CONTENT_TYPE'] = 'application/json';

// Create the JSON input that shop.php sends
$purchaseData = [
    "item_id" => 1,
    "price" => 10,
    "quantity" => 5,
    "recipient" => [
        "type" => "self",
        "character_id" => 1217
    ]
];

// Simulate the JSON input
$jsonInput = json_encode($purchaseData);

// Temporarily override php://input
file_put_contents('php://temp/maxmemory:1048576', $jsonInput);

echo "Testing purchase_item.php directly...\n";
echo "JSON Input: " . $jsonInput . "\n\n";

// Capture output from purchase_item.php
ob_start();

// Include the purchase script
try {
    // We need to simulate the JSON input
    $GLOBALS['HTTP_RAW_POST_DATA'] = $jsonInput;
    
    // Create a temporary file with the JSON data
    $tempFile = tempnam(sys_get_temp_dir(), 'purchase_test');
    file_put_contents($tempFile, $jsonInput);
    
    // Override the input stream
    $originalInput = 'php://input';
    
    echo "Executing purchase logic...\n";
    
    // Let's manually execute the purchase logic instead
    require_once "db.php";
    
    // Enable error reporting
    error_reporting(E_ALL);
    ini_set('display_errors', 1);
    
    $input = json_decode($jsonInput, true);
    
    if (!$input || !isset($input["item_id"], $input["price"], $input["quantity"], $input["recipient"])) {
        throw new Exception("Invalid request data");
    }
    
    $itemId = intval($input["item_id"]);
    $expectedPrice = intval($input["price"]);
    $expectedQuantity = intval($input["quantity"]);
    $recipient = $input["recipient"];
    
    echo "✓ Input parsed successfully\n";
    echo "  - Item ID: $itemId\n";
    echo "  - Price: $expectedPrice\n";
    echo "  - Quantity: $expectedQuantity\n";
    echo "  - Character ID: {$recipient['character_id']}\n\n";
    
    // Get account info
    $accountQuery = "SELECT id, name FROM account_data WHERE name = ?";
    $stmt = $conn->prepare($accountQuery);
    $stmt->bind_param("s", $_SESSION["username"]);
    $stmt->execute();
    $accountResult = $stmt->get_result();
    $account = $accountResult->fetch_assoc();
    
    if (!$account) {
        throw new Exception("Account not found");
    }
    
    echo "✓ Account found: {$account['name']} (ID: {$account['id']})\n";
    
    $userId = $account["id"];
    $accountName = $account["name"];
    
    // Get item details
    $itemQuery = "SELECT id, item_id, name, price, quantity FROM shop_items WHERE id = ?";
    $stmt = $conn->prepare($itemQuery);
    $stmt->bind_param("i", $itemId);
    $stmt->execute();
    $itemResult = $stmt->get_result();
    $item = $itemResult->fetch_assoc();
    
    if (!$item) {
        throw new Exception("Item not found");
    }
    
    echo "✓ Item found: {$item['name']} - {$item['price']} DP\n";
    
    // Verify price and quantity
    if ($item["price"] != $expectedPrice) {
        throw new Exception("Price mismatch: expected $expectedPrice, got {$item['price']}");
    }
    
    if ($item["quantity"] != $expectedQuantity) {
        throw new Exception("Quantity mismatch: expected $expectedQuantity, got {$item['quantity']}");
    }
    
    echo "✓ Price and quantity verified\n";
    
    // Check user balance
    $balanceQuery = "SELECT value FROM account_balance WHERE account_id = ? AND price_id = 1";
    $stmt = $conn->prepare($balanceQuery);
    $stmt->bind_param("i", $userId);
    $stmt->execute();
    $balanceResult = $stmt->get_result();
    $balance = $balanceResult->fetch_assoc();
    $userPoints = $balance["value"] ?? 0;
    
    echo "✓ Current balance: $userPoints DP\n";
    
    if ($userPoints < $item["price"]) {
        throw new Exception("Insufficient points: have $userPoints, need {$item['price']}");
    }
    
    // Get character info
    $characterId = $recipient["character_id"];
    $playerQuery = "SELECT id, name FROM players WHERE id = ? AND account_id = ?";
    $stmt = $conn->prepare($playerQuery);
    $stmt->bind_param("ii", $characterId, $userId);
    $stmt->execute();
    $playerResult = $stmt->get_result();
    $player = $playerResult->fetch_assoc();
    
    if (!$player) {
        throw new Exception("Character not found or does not belong to your account");
    }
    
    echo "✓ Character found: {$player['name']} (ID: {$player['id']})\n";
    
    $playerId = $player["id"];
    $playerName = $player["name"];
    
    // Start transaction
    $conn->begin_transaction();
    echo "✓ Transaction started\n";
    
    // Get next unique IDs safely
    $itemUniqueIdResult = $conn->query("SELECT COALESCE(MAX(item_unique_id), 0) + 1 as next_id FROM inventory");
    $newItemUniqueId = $itemUniqueIdResult ? $itemUniqueIdResult->fetch_assoc()["next_id"] : 1;
    
    $mailUniqueIdResult = $conn->query("SELECT COALESCE(MAX(mail_unique_id), 0) + 1 as next_id FROM mail");
    $newMailId = $mailUniqueIdResult ? $mailUniqueIdResult->fetch_assoc()["next_id"] : 1;
    
    echo "✓ Generated IDs - Item: $newItemUniqueId, Mail: $newMailId\n";
    
    // Insert into inventory
    $inventoryQuery = "INSERT INTO inventory (item_unique_id, item_id, item_count, item_color, item_creator, item_expire_time, item_activation_count, item_owner, item_location, enchant, item_skin, fusioned_item, optional_socket, charge_points) VALUES (?, ?, ?, 0, '', NULL, 0, ?, 127, 0, 0, 0, 0, 0)";
    $stmt = $conn->prepare($inventoryQuery);
    $stmt->bind_param("iiii", $newItemUniqueId, $item["item_id"], $item["quantity"], $playerId);
    
    if (!$stmt->execute()) {
        throw new Exception("Failed to create item: " . $stmt->error);
    }
    echo "✓ Item added to inventory\n";
    
    // Insert mail
    $mailQuery = "INSERT INTO mail (mail_unique_id, mail_recipient_id, sender_name, mail_title, mail_message, unread, attached_item_id, attached_kinah, express, mail_type) VALUES (?, ?, 'Donation Shop', 'Item Purchase', ?, 1, ?, 0, 0, 1)";
    $mailMessage = "You have purchased: " . $item["name"] . " (x" . $item["quantity"] . ") for " . $item["price"] . " DP. Enjoy your item!";
    $stmt = $conn->prepare($mailQuery);
    $stmt->bind_param("iisi", $newMailId, $playerId, $mailMessage, $newItemUniqueId);
    
    if (!$stmt->execute()) {
        throw new Exception("Failed to send mail: " . $stmt->error);
    }
    echo "✓ Mail sent\n";
    
    // Deduct points
    $deductQuery = "UPDATE account_balance SET value = value - ? WHERE account_id = ? AND price_id = 1";
    $stmt = $conn->prepare($deductQuery);
    $stmt->bind_param("ii", $item["price"], $userId);
    
    if (!$stmt->execute()) {
        throw new Exception("Failed to deduct points: " . $stmt->error);
    }
    echo "✓ Points deducted\n";
    
    // Add transaction history
    $historyQuery = "INSERT INTO balance_history (date, reference_id, account_id, account_name, price_id, amount, description, type) VALUES (NOW(), ?, ?, ?, 1, ?, ?, 'OUT')";
    $orderRef = "SHOP_" . $userId . "_" . time();
    $description = "Purchased: " . $item["name"] . " (x" . $item["quantity"] . ")";
    $stmt = $conn->prepare($historyQuery);
    $stmt->bind_param("ssiss", $orderRef, $userId, $accountName, $item["price"], $description);
    
    if (!$stmt->execute()) {
        throw new Exception("Failed to record transaction: " . $stmt->error);
    }
    echo "✓ Transaction recorded\n";
    
    // Commit transaction
    $conn->commit();
    echo "✓ Transaction committed\n";
    
    $newBalance = $userPoints - $item["price"];
    
    echo "\n🎉 PURCHASE SUCCESSFUL!\n";
    echo "Item: {$item['name']} (x{$item['quantity']})\n";
    echo "Sent to: $playerName\n";
    echo "Cost: {$item['price']} DP\n";
    echo "New balance: $newBalance DP\n";
    echo "Mail ID: $newMailId\n";
    echo "Item Unique ID: $newItemUniqueId\n";
    
    // This is what should be returned to the browser
    $response = [
        "success" => true,
        "message" => "Purchase successful! Item sent to character \"$playerName\". Check your in-game mail.",
        "remaining_points" => $newBalance,
        "character_name" => $playerName,
        "mail_id" => $newMailId,
        "item_unique_id" => $newItemUniqueId
    ];
    
    echo "\nJSON Response that should be sent:\n";
    echo json_encode($response, JSON_PRETTY_PRINT) . "\n";
    
} catch (Exception $e) {
    if (isset($conn) && $conn->ping()) {
        $conn->rollback();
    }
    
    echo "\n❌ PURCHASE FAILED!\n";
    echo "Error: " . $e->getMessage() . "\n";
    echo "Stack trace:\n" . $e->getTraceAsString() . "\n";
}

$output = ob_get_clean();
echo $output;
?>
