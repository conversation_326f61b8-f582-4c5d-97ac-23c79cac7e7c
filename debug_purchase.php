<?php
// Debug purchase system to see what happened
require_once "db.php";

$username = 'lahart77';

echo "Purchase Debug for: $username\n";
echo "===============================\n";

// Get account info
$accountQuery = "SELECT id, name FROM account_data WHERE name = ?";
$stmt = $conn->prepare($accountQuery);
$stmt->bind_param("s", $username);
$stmt->execute();
$accountResult = $stmt->get_result();
$account = $accountResult->fetch_assoc();

if (!$account) {
    echo "✗ Account not found\n";
    exit;
}

$accountId = $account['id'];
echo "✓ Account ID: $accountId\n\n";

// Check current balance
$balanceQuery = "SELECT value FROM account_balance WHERE account_id = ? AND price_id = 1";
$stmt = $conn->prepare($balanceQuery);
$stmt->bind_param("i", $accountId);
$stmt->execute();
$balanceResult = $stmt->get_result();
$balance = $balanceResult->fetch_assoc();
$currentPoints = $balance['value'] ?? 0;
echo "Current Points: $currentPoints DP\n\n";

// Check balance history (recent transactions)
echo "Recent Transactions:\n";
$historyQuery = "SELECT * FROM balance_history WHERE account_id = ? ORDER BY date DESC LIMIT 5";
$stmt = $conn->prepare($historyQuery);
$stmt->bind_param("i", $accountId);
$stmt->execute();
$historyResult = $stmt->get_result();

if ($historyResult->num_rows > 0) {
    while ($history = $historyResult->fetch_assoc()) {
        echo "- {$history['date']}: {$history['type']} {$history['amount']} DP - {$history['description']}\n";
    }
} else {
    echo "- No transaction history found\n";
}

// Check players (characters) for this account
echo "\nCharacters for this account:\n";
$playersQuery = "SELECT id, name, player_class, exp FROM players WHERE account_id = ?";
$stmt = $conn->prepare($playersQuery);
$stmt->bind_param("i", $accountId);
$stmt->execute();
$playersResult = $stmt->get_result();

if ($playersResult->num_rows > 0) {
    while ($player = $playersResult->fetch_assoc()) {
        echo "- Character ID: {$player['id']}, Name: {$player['name']}, Class: {$player['player_class']}\n";
        
        // Check inventory for this character
        $inventoryQuery = "SELECT item_unique_id, item_id, item_count, item_location FROM inventory WHERE item_owner = ? AND item_id = ********* ORDER BY item_unique_id DESC LIMIT 5";
        $stmt2 = $conn->prepare($inventoryQuery);
        $stmt2->bind_param("i", $player['id']);
        $stmt2->execute();
        $inventoryResult = $stmt2->get_result();
        
        if ($inventoryResult->num_rows > 0) {
            echo "  Recent items (*********) in inventory:\n";
            while ($item = $inventoryResult->fetch_assoc()) {
                $location = $item['item_location'] == 127 ? 'Mail Attachment' : 'Location ' . $item['item_location'];
                echo "    - Unique ID: {$item['item_unique_id']}, Count: {$item['item_count']}, Location: $location\n";
            }
        } else {
            echo "  No items (*********) found in inventory\n";
        }
        
        // Check mail for this character
        $mailQuery = "SELECT mail_unique_id, sender_name, mail_title, mail_message, attached_item_id, unread FROM mail WHERE mail_recipient_id = ? ORDER BY mail_unique_id DESC LIMIT 5";
        $stmt2 = $conn->prepare($mailQuery);
        $stmt2->bind_param("i", $player['id']);
        $stmt2->execute();
        $mailResult = $stmt2->get_result();
        
        if ($mailResult->num_rows > 0) {
            echo "  Recent mail:\n";
            while ($mail = $mailResult->fetch_assoc()) {
                $status = $mail['unread'] ? 'Unread' : 'Read';
                echo "    - Mail ID: {$mail['mail_unique_id']}, From: {$mail['sender_name']}, Subject: {$mail['mail_title']}, Item: {$mail['attached_item_id']}, Status: $status\n";
            }
        } else {
            echo "  No mail found\n";
        }
        echo "\n";
    }
} else {
    echo "- No characters found for this account!\n";
    echo "  This might be the issue - you need a character to receive items!\n";
}

echo "\nTroubleshooting:\n";
echo "1. Make sure you have a character created in-game\n";
echo "2. Check your in-game mail system\n";
echo "3. Look for mail from 'Donation Shop'\n";
echo "4. Items should be attached to the mail\n";

$conn->close();
?>
