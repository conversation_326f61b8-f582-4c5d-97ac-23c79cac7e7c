<?php
require_once "db.php";

echo "<h2>🛍️ Shop Categories Check</h2>";

// Check shop_items table structure
echo "<h3>Shop Items Table Structure:</h3>";
$shopStructure = $conn->query("DESCRIBE shop_items");
if ($shopStructure) {
    echo "<table border='1' style='border-collapse: collapse;'>";
    echo "<tr><th>Field</th><th>Type</th><th>Null</th><th>Key</th><th>Default</th></tr>";
    while ($row = $shopStructure->fetch_assoc()) {
        echo "<tr>";
        echo "<td>" . $row['Field'] . "</td>";
        echo "<td>" . $row['Type'] . "</td>";
        echo "<td>" . $row['Null'] . "</td>";
        echo "<td>" . $row['Key'] . "</td>";
        echo "<td>" . $row['Default'] . "</td>";
        echo "</tr>";
    }
    echo "</table>";
} else {
    echo "Could not get shop_items table structure.";
}

// Check if shop_categories table exists
echo "<h3>Shop Categories Table Check:</h3>";
$categoriesCheck = $conn->query("SHOW TABLES LIKE 'shop_categories'");
if ($categoriesCheck && $categoriesCheck->num_rows > 0) {
    echo "✅ shop_categories table exists<br>";
    
    // Show categories
    $categoriesQuery = "SELECT * FROM shop_categories ORDER BY id";
    $result = $conn->query($categoriesQuery);
    if ($result && $result->num_rows > 0) {
        echo "<table border='1' style='border-collapse: collapse;'>";
        echo "<tr><th>ID</th><th>Name</th><th>Description</th></tr>";
        while ($row = $result->fetch_assoc()) {
            echo "<tr>";
            echo "<td>" . $row['id'] . "</td>";
            echo "<td>" . $row['name'] . "</td>";
            echo "<td>" . ($row['description'] ?? 'N/A') . "</td>";
            echo "</tr>";
        }
        echo "</table>";
    } else {
        echo "No categories found.";
    }
} else {
    echo "❌ shop_categories table does not exist<br>";
    echo "We need to create it!";
}

// Check current shop items and their categories
echo "<h3>Current Shop Items and Categories:</h3>";
$itemsQuery = "SELECT id, name, category_id FROM shop_items ORDER BY category_id, name";
$result = $conn->query($itemsQuery);
if ($result && $result->num_rows > 0) {
    echo "<table border='1' style='border-collapse: collapse;'>";
    echo "<tr><th>Item ID</th><th>Name</th><th>Category ID</th></tr>";
    while ($row = $result->fetch_assoc()) {
        echo "<tr>";
        echo "<td>" . $row['id'] . "</td>";
        echo "<td>" . $row['name'] . "</td>";
        echo "<td>" . $row['category_id'] . "</td>";
        echo "</tr>";
    }
    echo "</table>";
} else {
    echo "No shop items found.";
}

$conn->close();
?>
