<?php
echo "Verifying temperance column fix in not-aion database...\n";

require_once "db.php";

if ($conn->connect_error) {
    die("Connection failed: " . $conn->connect_error);
}

echo "✓ Database connection successful!\n\n";

// Ensure we're using not-aion database
$conn->select_db('not-aion');

// Check current database
$result = $conn->query("SELECT DATABASE() as current_db");
if ($result) {
    $current = $result->fetch_assoc();
    echo "Currently using database: {$current['current_db']}\n\n";
}

// Check mail table structure
echo "=== MAIL TABLE STRUCTURE IN not-aion ===\n";
$result = $conn->query("DESCRIBE mail");
if ($result) {
    while ($row = $result->fetch_assoc()) {
        $nullable = $row['Null'] == 'YES' ? '(NULL)' : '(NOT NULL)';
        $default = $row['Default'] ? " DEFAULT {$row['Default']}" : '';
        echo "- {$row['Field']}: {$row['Type']} $nullable$default\n";
    }
} else {
    echo "Error: " . $conn->error . "\n";
}

// Test temperance column specifically
echo "\n=== TESTING TEMPERANCE COLUMN ===\n";
$testQueries = [
    "SELECT COUNT(*) as count FROM mail",
    "SELECT temperance FROM mail LIMIT 1",
    "SELECT mailUniqueId, temperance, mail_type FROM mail LIMIT 1"
];

foreach ($testQueries as $query) {
    echo "Testing: $query\n";
    $result = $conn->query($query);
    if ($result) {
        if ($result->num_rows > 0) {
            $row = $result->fetch_assoc();
            echo "  ✓ Success: " . json_encode($row) . "\n";
        } else {
            echo "  ✓ Query executed but no rows returned\n";
        }
    } else {
        echo "  ✗ Error: " . $conn->error . "\n";
    }
}

// Check if there are any triggers or views that might interfere
echo "\n=== CHECKING FOR TRIGGERS/VIEWS ===\n";
$result = $conn->query("SHOW TRIGGERS LIKE 'mail'");
if ($result && $result->num_rows > 0) {
    echo "Mail triggers found:\n";
    while ($row = $result->fetch_assoc()) {
        echo "  - {$row['Trigger']}: {$row['Event']} {$row['Timing']}\n";
    }
} else {
    echo "No mail triggers found\n";
}

$result = $conn->query("SHOW FULL TABLES WHERE Table_type = 'VIEW' AND Tables_in_not-aion LIKE '%mail%'");
if ($result && $result->num_rows > 0) {
    echo "Mail views found:\n";
    while ($row = $result->fetch_assoc()) {
        echo "  - {$row['Tables_in_not-aion']}\n";
    }
} else {
    echo "No mail views found\n";
}

// Test the exact query that might be failing in the game server
echo "\n=== TESTING GAME SERVER STYLE QUERIES ===\n";
$gameServerQueries = [
    "SELECT mailUniqueId, mailRecipientId, senderName, mailTitle, mailMessage, unread, attachedItemId, attachedKinahCount, express, recievedTime, temperance FROM mail WHERE mailRecipientId = 1217",
    "SELECT * FROM mail WHERE mailRecipientId = 1217",
    "SELECT temperance, mail_type FROM mail WHERE mailUniqueId = 1"
];

foreach ($gameServerQueries as $query) {
    echo "Testing game server query: $query\n";
    $result = $conn->query($query);
    if ($result) {
        echo "  ✓ Success - Rows: " . $result->num_rows . "\n";
        if ($result->num_rows > 0) {
            $row = $result->fetch_assoc();
            if (isset($row['temperance'])) {
                echo "  ✓ temperance value: {$row['temperance']}\n";
            }
        }
    } else {
        echo "  ✗ Error: " . $conn->error . "\n";
    }
}

// Show exact CREATE TABLE statement
echo "\n=== EXACT TABLE DEFINITION ===\n";
$result = $conn->query("SHOW CREATE TABLE mail");
if ($result) {
    $row = $result->fetch_assoc();
    echo $row['Create Table'] . "\n";
} else {
    echo "Error: " . $conn->error . "\n";
}

echo "\n=== RECOMMENDATIONS ===\n";
echo "1. Restart your game server completely\n";
echo "2. Check game server config files for database name\n";
echo "3. Look for database connection pooling/caching\n";
echo "4. Check if game server is using a different MySQL port\n";
echo "5. Verify game server is connecting to the same MySQL instance\n";

$conn->close();
?>
