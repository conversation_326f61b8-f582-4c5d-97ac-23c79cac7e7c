// Server Time Update
function updateServerTime() {
    const now = new Date();
    const timeString = now.toLocaleTimeString('en-US', {
        hour12: false,
        hour: '2-digit',
        minute: '2-digit'
    });
    const serverTimeElement = document.querySelector('.server-time');
    if (serverTimeElement) {
        serverTimeElement.textContent = `🕐 Server Time: ${timeString}`;
    }
}

// Update server time every minute
setInterval(updateServerTime, 60000);
updateServerTime(); // Initial call

// Cookie Notice Functionality
document.addEventListener('DOMContentLoaded', function() {
    const cookieNotice = document.querySelector('.cookie-notice');
    const cookieAccept = document.querySelector('.cookie-accept');

    // Check if user has already accepted cookies
    if (localStorage.getItem('cookiesAccepted') === 'true') {
        cookieNotice.style.display = 'none';
    }

    // Handle cookie acceptance
    if (cookieAccept) {
        cookieAccept.addEventListener('click', function() {
            localStorage.setItem('cookiesAccepted', 'true');
            cookieNotice.style.display = 'none';
        });
    }

    // Add click handlers for action buttons
    const discordBtn = document.querySelector('.discord-btn');
    const playBtn = document.querySelector('.play-btn');
    const downloadBtn = document.querySelector('.download-btn');

    if (discordBtn) {
        discordBtn.addEventListener('click', function() {
            // Replace with your actual Discord invite link
            window.open('https://discord.gg/mhTjjHx7Vf', '_blank');
        });
    }

    if (playBtn) {
        playBtn.addEventListener('click', function() {
            // Redirect to registration or game launcher
            window.location.href = 'register.php';
        });
    }

    if (downloadBtn) {
        downloadBtn.addEventListener('click', function() {
            // Replace with your actual download link
            window.open('#', '_blank');
        });
    }

    // Add smooth scrolling for navigation links
    const navLinks = document.querySelectorAll('.nav-menu a[href^="#"]');
    navLinks.forEach(link => {
        link.addEventListener('click', function(e) {
            e.preventDefault();
            const targetId = this.getAttribute('href').substring(1);
            const targetElement = document.getElementById(targetId);
            if (targetElement) {
                targetElement.scrollIntoView({
                    behavior: 'smooth',
                    block: 'start'
                });
            }
        });
    });

    // Add click handler for schedule navigation
    const scheduleLink = document.querySelector('a[href="#schedule"]');
    if (scheduleLink) {
        scheduleLink.addEventListener('click', function(e) {
            e.preventDefault();
            const scheduleSection = document.getElementById('schedule');
            if (scheduleSection) {
                scheduleSection.scrollIntoView({
                    behavior: 'smooth',
                    block: 'start'
                });
            }
        });
    }

    // Add click handler for world raids navigation
    const worldRaidsLink = document.querySelector('a[href="#world-raids"]');
    if (worldRaidsLink) {
        worldRaidsLink.addEventListener('click', function(e) {
            e.preventDefault();
            const worldRaidsSection = document.getElementById('world-raids');
            if (worldRaidsSection) {
                worldRaidsSection.scrollIntoView({
                    behavior: 'smooth',
                    block: 'start'
                });
            }
        });
    }

    // Add parallax effect to hero section
    window.addEventListener('scroll', function() {
        const scrolled = window.pageYOffset;
        const heroSection = document.querySelector('.hero-section');
        if (heroSection) {
            heroSection.style.transform = `translateY(${scrolled * 0.5}px)`;
        }
    });

    // Add animation to news cards on scroll
    const observerOptions = {
        threshold: 0.1,
        rootMargin: '0px 0px -50px 0px'
    };

    const observer = new IntersectionObserver(function(entries) {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                entry.target.style.opacity = '1';
                entry.target.style.transform = 'translateY(0)';
            }
        });
    }, observerOptions);

    // Observe news cards
    const newsCards = document.querySelectorAll('.news-card');
    newsCards.forEach(card => {
        card.style.opacity = '0';
        card.style.transform = 'translateY(30px)';
        card.style.transition = 'opacity 0.6s ease, transform 0.6s ease';
        observer.observe(card);
    });
});

// Add typing effect to main title
function typeWriter(element, text, speed = 100) {
    let i = 0;
    element.innerHTML = '';

    function type() {
        if (i < text.length) {
            element.innerHTML += text.charAt(i);
            i++;
            setTimeout(type, speed);
        }
    }

    type();
}

// Initialize typing effect when page loads
window.addEventListener('load', function() {
    const mainTitle = document.querySelector('.main-title');
    if (mainTitle) {
        const originalText = mainTitle.textContent;
        setTimeout(() => {
            typeWriter(mainTitle, originalText, 150);
        }, 1000);
    }
});

// FAQ Interactive functionality
document.addEventListener('DOMContentLoaded', function() {
    const faqItems = document.querySelectorAll('.faq-item');

    faqItems.forEach(item => {
        const question = item.querySelector('.faq-question');
        const answer = item.querySelector('.faq-answer');

        // Initially hide all answers
        answer.style.display = 'none';

        question.addEventListener('click', function() {
            const isActive = item.classList.contains('active');

            // Close all other FAQ items
            faqItems.forEach(otherItem => {
                if (otherItem !== item) {
                    otherItem.classList.remove('active');
                    otherItem.querySelector('.faq-answer').style.display = 'none';
                }
            });

            // Toggle current item
            if (isActive) {
                item.classList.remove('active');
                answer.style.display = 'none';
            } else {
                item.classList.add('active');
                answer.style.display = 'block';
            }
        });
    });

    // Auto-open first FAQ item in each category
    const faqCategories = document.querySelectorAll('.faq-category');
    faqCategories.forEach(category => {
        const firstItem = category.querySelector('.faq-item');
        if (firstItem) {
            firstItem.classList.add('active');
            firstItem.querySelector('.faq-answer').style.display = 'block';
        }
    });
});