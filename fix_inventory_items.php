<?php
// Fix inventory items with count 0 and check for issues
require_once "db.php";

echo "Checking and fixing inventory issues...\n";
echo "=====================================\n";

// Check for items with count 0
$checkQuery = "SELECT * FROM inventory WHERE item_id = 164000073 AND item_count = 0";
$result = $conn->query($checkQuery);

if ($result->num_rows > 0) {
    echo "Found items with count 0:\n";
    while ($item = $result->fetch_assoc()) {
        echo "- Item Unique ID: {$item['item_unique_id']}, Owner: {$item['item_owner']}, Count: {$item['item_count']}\n";
    }
    
    // Delete items with count 0 (they're broken)
    $deleteQuery = "DELETE FROM inventory WHERE item_id = 164000073 AND item_count = 0";
    if ($conn->query($deleteQuery)) {
        echo "✓ Deleted broken items with count 0\n";
    } else {
        echo "✗ Error deleting items: " . $conn->error . "\n";
    }
} else {
    echo "✓ No items with count 0 found\n";
}

// Check for valid items
$validQuery = "SELECT * FROM inventory WHERE item_id = 164000073 AND item_count > 0";
$result = $conn->query($validQuery);

echo "\nValid items found:\n";
if ($result->num_rows > 0) {
    while ($item = $result->fetch_assoc()) {
        $location = $item['item_location'] == 127 ? 'Mail Attachment' : 'Location ' . $item['item_location'];
        echo "- Item Unique ID: {$item['item_unique_id']}, Owner: {$item['item_owner']}, Count: {$item['item_count']}, Location: $location\n";
    }
} else {
    echo "- No valid items found\n";
}

// Check mail records
echo "\nChecking mail records:\n";
$mailQuery = "SELECT * FROM mail WHERE sender_name = 'Donation Shop' OR sender_name = 'Daily Rewards'";
$result = $conn->query($mailQuery);

if ($result->num_rows > 0) {
    while ($mail = $result->fetch_assoc()) {
        $status = $mail['unread'] ? 'Unread' : 'Read';
        echo "- Mail ID: {$mail['mail_unique_id']}, To: {$mail['mail_recipient_id']}, From: {$mail['sender_name']}, Item: {$mail['attached_item_id']}, Status: $status\n";
    }
} else {
    echo "- No mail from shop/rewards found\n";
}

echo "\n✓ Inventory check completed\n";

$conn->close();
?>
