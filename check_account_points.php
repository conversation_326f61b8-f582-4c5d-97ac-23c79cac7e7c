<?php
// Check account points for debugging
require_once "db.php";

$username = 'lahart77';

echo "Checking account points for: $username\n";
echo "=====================================\n";

// 1. Check if account exists
$accountQuery = "SELECT id, name FROM account_data WHERE name = ?";
$stmt = $conn->prepare($accountQuery);
$stmt->bind_param("s", $username);
$stmt->execute();
$result = $stmt->get_result();
$account = $result->fetch_assoc();

if (!$account) {
    echo "✗ Account '$username' not found in account_data table\n";
    exit;
}

echo "✓ Account found:\n";
echo "  - ID: {$account['id']}\n";
echo "  - Name: {$account['name']}\n\n";

// 2. Check account_balance table
$balanceQuery = "SELECT ab.*, pt.price_name, pt.symbolic FROM account_balance ab LEFT JOIN price_type pt ON ab.price_id = pt.price_id WHERE ab.account_id = ?";
$stmt = $conn->prepare($balanceQuery);
$stmt->bind_param("i", $account['id']);
$stmt->execute();
$balanceResult = $stmt->get_result();

echo "Account Balance:\n";
if ($balanceResult->num_rows > 0) {
    while ($balance = $balanceResult->fetch_assoc()) {
        echo "  - {$balance['price_name']} ({$balance['symbolic']}): {$balance['value']}\n";
    }
} else {
    echo "  - No balance records found\n";
}

// 3. Check balance_history table
echo "\nBalance History:\n";
$historyQuery = "SELECT * FROM balance_history WHERE account_id = ? ORDER BY date DESC LIMIT 5";
$stmt = $conn->prepare($historyQuery);
$stmt->bind_param("i", $account['id']);
$stmt->execute();
$historyResult = $stmt->get_result();

if ($historyResult->num_rows > 0) {
    while ($history = $historyResult->fetch_assoc()) {
        echo "  - {$history['date']}: {$history['type']} {$history['amount']} - {$history['description']}\n";
    }
} else {
    echo "  - No transaction history found\n";
}

// 4. Check what the dashboard query would return
echo "\nDashboard Query Test:\n";
$dashboardQuery = "
    SELECT ab.value, pt.price_name, pt.symbolic
    FROM account_balance ab
    JOIN price_type pt ON ab.price_id = pt.price_id
    WHERE ab.account_id = ?
";
$stmt = $conn->prepare($dashboardQuery);
$stmt->bind_param("i", $account['id']);
$stmt->execute();
$dashboardResult = $stmt->get_result();

if ($dashboardResult->num_rows > 0) {
    echo "✓ Dashboard would show:\n";
    while ($balance = $dashboardResult->fetch_assoc()) {
        echo "  - {$balance['price_name']} ({$balance['symbolic']}): {$balance['value']}\n";
    }
} else {
    echo "✗ Dashboard query returns no results\n";
}

$conn->close();
?>
