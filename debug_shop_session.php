<?php
session_start();
echo "Debugging shop session and balance...\n";

require_once "db.php";

// Test database connection
if ($conn->connect_error) {
    die("Connection failed: " . $conn->connect_error);
}

echo "✓ Database connection successful!\n\n";

// Check session
echo "=== SESSION DEBUG ===\n";
if (isset($_SESSION["username"])) {
    echo "✓ Session username: " . $_SESSION["username"] . "\n";
} else {
    echo "✗ No session username found!\n";
    echo "Available session data:\n";
    print_r($_SESSION);
}

// Simulate the exact same process as shop.php
$sessionUsername = $_SESSION["username"] ?? "admin"; // fallback for testing
echo "\nUsing username: $sessionUsername\n";

// Get account info (exact same query as shop.php)
$accountQuery = "SELECT id, name, membership FROM account_data WHERE name = ?";
$stmt = $conn->prepare($accountQuery);
$stmt->bind_param("s", $sessionUsername);
$stmt->execute();
$accountResult = $stmt->get_result();
$account = $accountResult->fetch_assoc();

echo "\n=== ACCOUNT DEBUG ===\n";
if (!$account) {
    echo "✗ Account not found for username: $sessionUsername\n";
    exit;
} else {
    echo "✓ Account found:\n";
    echo "  - ID: {$account['id']}\n";
    echo "  - Name: {$account['name']}\n";
    echo "  - Membership: {$account['membership']}\n";
}

// Get account balance (exact same query as shop.php)
echo "\n=== BALANCE DEBUG ===\n";
$balanceQuery = "
    SELECT ab.value, pt.price_name, pt.symbolic
    FROM account_balance ab
    JOIN price_type pt ON ab.price_id = pt.price_id
    WHERE ab.account_id = ? AND pt.price_id = 1
";

echo "Running balance query with account_id: {$account['id']}\n";

$stmt = $conn->prepare($balanceQuery);
$stmt->bind_param("i", $account['id']);
$stmt->execute();
$balanceResult = $stmt->get_result();
$balance = $balanceResult->fetch_assoc();

echo "Balance query result:\n";
if ($balance) {
    print_r($balance);
    $userPoints = $balance['value'] ?? 0;
    echo "✓ userPoints will be: $userPoints\n";
} else {
    echo "✗ No balance result found!\n";
    $userPoints = 0;
    
    // Debug why no balance found
    echo "\nDebugging balance tables...\n";
    
    // Check account_balance for this account
    $debugBalance = $conn->query("SELECT * FROM account_balance WHERE account_id = {$account['id']}");
    echo "account_balance records for account {$account['id']}:\n";
    if ($debugBalance && $debugBalance->num_rows > 0) {
        while ($row = $debugBalance->fetch_assoc()) {
            print_r($row);
        }
    } else {
        echo "  No records found\n";
    }
    
    // Check price_type table
    echo "\nprice_type table:\n";
    $debugPrice = $conn->query("SELECT * FROM price_type");
    if ($debugPrice && $debugPrice->num_rows > 0) {
        while ($row = $debugPrice->fetch_assoc()) {
            print_r($row);
        }
    }
}

echo "\n=== FINAL RESULT ===\n";
echo "The shop will display: " . number_format($userPoints) . " DP\n";

// Test if we can manually set session and try again
if (!isset($_SESSION["username"])) {
    echo "\n=== SETTING TEST SESSION ===\n";
    $_SESSION["username"] = "admin";
    echo "Set session username to: admin\n";
    echo "Try refreshing your shop page now.\n";
}

$conn->close();
?>
