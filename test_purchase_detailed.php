<?php
session_start();
echo "Testing purchase process...\n";

require_once "db.php";

// Simulate being logged in as test22
$_SESSION["username"] = "test22";

// Test database connection
if ($conn->connect_error) {
    die("Connection failed: " . $conn->connect_error);
}

echo "✓ Database connection successful!\n";
echo "✓ Simulating login as: " . $_SESSION["username"] . "\n\n";

// Simulate the exact purchase request that would come from the shop
$testPurchase = [
    'item_id' => 1, // Shop item ID (first item in shop_items table)
    'price' => 10,
    'quantity' => 5,
    'recipient' => [
        'type' => 'self',
        'character_id' => 1217 // Your character ID
    ]
];

echo "=== SIMULATING PURCHASE REQUEST ===\n";
echo "Purchase data:\n";
print_r($testPurchase);

// Now let's manually run through the purchase_item.php logic
echo "\n=== RUNNING PURCHASE LOGIC ===\n";

// Get account info
$accountQuery = "SELECT id, name FROM account_data WHERE name = ?";
$stmt = $conn->prepare($accountQuery);
$stmt->bind_param("s", $_SESSION["username"]);
$stmt->execute();
$accountResult = $stmt->get_result();
$account = $accountResult->fetch_assoc();

if (!$account) {
    echo "✗ Account not found\n";
    exit;
}

echo "✓ Account found: {$account['name']} (ID: {$account['id']})\n";

// Get item details
$itemQuery = "SELECT id, item_id, name, price, quantity FROM shop_items WHERE id = ?";
$stmt = $conn->prepare($itemQuery);
$stmt->bind_param("i", $testPurchase['item_id']);
$stmt->execute();
$itemResult = $stmt->get_result();
$item = $itemResult->fetch_assoc();

if (!$item) {
    echo "✗ Shop item not found\n";
    exit;
}

echo "✓ Shop item found: {$item['name']} - {$item['price']} DP\n";

// Check user balance
$balanceQuery = "SELECT value FROM account_balance WHERE account_id = ? AND price_id = 1";
$stmt = $conn->prepare($balanceQuery);
$stmt->bind_param("i", $account['id']);
$stmt->execute();
$balanceResult = $stmt->get_result();
$balance = $balanceResult->fetch_assoc();
$userPoints = $balance['value'] ?? 0;

echo "✓ Current balance: $userPoints DP\n";

if ($userPoints < $item['price']) {
    echo "✗ Insufficient points\n";
    exit;
}

// Check if character exists
$characterId = $testPurchase['recipient']['character_id'];
$playerQuery = "SELECT id, name FROM players WHERE id = ? AND account_id = ?";
$stmt = $conn->prepare($playerQuery);
$stmt->bind_param("ii", $characterId, $account['id']);
$stmt->execute();
$playerResult = $stmt->get_result();
$player = $playerResult->fetch_assoc();

if (!$player) {
    echo "✗ Character not found or doesn't belong to account\n";
    exit;
}

echo "✓ Character found: {$player['name']} (ID: {$player['id']})\n";

// Test getting next unique IDs
echo "\n=== TESTING UNIQUE ID GENERATION ===\n";

$itemUniqueIdQuery = "SELECT MAX(item_unique_id) + 1 as next_id FROM inventory";
$result = $conn->query($itemUniqueIdQuery);
$newItemUniqueId = $result->fetch_assoc()['next_id'] ?? 1;
echo "✓ Next item unique ID: $newItemUniqueId\n";

$mailUniqueIdQuery = "SELECT MAX(mail_unique_id) + 1 as next_id FROM mail";
$result = $conn->query($mailUniqueIdQuery);
$newMailId = $result->fetch_assoc()['next_id'] ?? 1;
echo "✓ Next mail ID: $newMailId\n";

// Test inventory insertion
echo "\n=== TESTING INVENTORY INSERTION ===\n";

try {
    $conn->begin_transaction();
    
    // Insert into inventory
    $inventoryQuery = "INSERT INTO inventory (item_unique_id, item_id, item_count, item_color, item_creator, item_expire_time, item_activation_count, item_owner, item_location, enchant, item_skin, fusioned_item, optional_socket, charge_points) VALUES (?, ?, ?, 0, '', NULL, 0, ?, 127, 0, 0, 0, 0, 0)";
    $stmt = $conn->prepare($inventoryQuery);
    $stmt->bind_param("iiii", $newItemUniqueId, $item['item_id'], $item['quantity'], $player['id']);
    
    if (!$stmt->execute()) {
        throw new Exception("Failed to insert inventory: " . $stmt->error);
    }
    echo "✓ Inventory item inserted successfully\n";
    
    // Insert mail
    $mailQuery = "INSERT INTO mail (mail_unique_id, mail_recipient_id, sender_name, mail_title, mail_message, unread, attached_item_id, attached_kinah, express, mail_type) VALUES (?, ?, 'Donation Shop', 'Item Purchase', ?, 1, ?, 0, 0, 1)";
    $mailMessage = "You have purchased: " . $item['name'] . " (x" . $item['quantity'] . ")";
    $stmt = $conn->prepare($mailQuery);
    $stmt->bind_param("iisi", $newMailId, $player['id'], $mailMessage, $newItemUniqueId);
    
    if (!$stmt->execute()) {
        throw new Exception("Failed to insert mail: " . $stmt->error);
    }
    echo "✓ Mail inserted successfully\n";
    
    // Deduct points
    $deductQuery = "UPDATE account_balance SET value = value - ? WHERE account_id = ? AND price_id = 1";
    $stmt = $conn->prepare($deductQuery);
    $stmt->bind_param("ii", $item['price'], $account['id']);
    
    if (!$stmt->execute()) {
        throw new Exception("Failed to deduct points: " . $stmt->error);
    }
    echo "✓ Points deducted successfully\n";
    
    // Add transaction history
    $historyQuery = "INSERT INTO balance_history (date, reference_id, account_id, account_name, price_id, amount, description, type) VALUES (NOW(), ?, ?, ?, 1, ?, ?, 'OUT')";
    $orderRef = 'SHOP_' . $account['id'] . '_' . time();
    $description = 'Purchased: ' . $item['name'];
    $stmt = $conn->prepare($historyQuery);
    $stmt->bind_param("ssiss", $orderRef, $account['id'], $account['name'], $item['price'], $description);
    
    if (!$stmt->execute()) {
        throw new Exception("Failed to insert history: " . $stmt->error);
    }
    echo "✓ Transaction history added\n";
    
    // Commit transaction
    $conn->commit();
    
    echo "\n✅ PURCHASE TEST SUCCESSFUL!\n";
    echo "Item delivered to character: {$player['name']}\n";
    echo "Mail ID: $newMailId\n";
    echo "Item Unique ID: $newItemUniqueId\n";
    echo "Remaining balance: " . ($userPoints - $item['price']) . " DP\n";
    
} catch (Exception $e) {
    $conn->rollback();
    echo "\n❌ PURCHASE TEST FAILED!\n";
    echo "Error: " . $e->getMessage() . "\n";
}

$conn->close();
?>
