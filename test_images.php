<!DOCTYPE html>
<html>
<head>
    <title>Test Images</title>
    <style>
        body { background: #1a1a1a; color: #fff; font-family: Arial; padding: 20px; }
        .test-image { margin: 20px; padding: 20px; border: 1px solid #d4af37; display: inline-block; }
        img { border: 1px solid #d4af37; }
    </style>
</head>
<body>
    <h1>Image Test Page</h1>
    
    <div class="test-image">
        <h3>Base64 Default Image (Should Always Work)</h3>
        <?php
        $default_image = 'data:image/svg+xml;base64,' . base64_encode('
        <svg width="50" height="50" xmlns="http://www.w3.org/2000/svg">
            <rect width="50" height="50" fill="#333333"/>
            <rect x="2" y="2" width="46" height="46" stroke="#d4af37" stroke-width="1" fill="none"/>
            <rect x="12" y="12" width="26" height="26" fill="#666666"/>
            <rect x="15" y="15" width="20" height="3" fill="#d4af37"/>
            <rect x="15" y="20" width="12" height="3" fill="#d4af37"/>
            <text x="25" y="42" text-anchor="middle" fill="#d4af37" font-size="6" font-family="Arial">ITEM</text>
        </svg>');
        ?>
        <img src="<?php echo $default_image; ?>" alt="Default Item" style="width: 50px; height: 50px;">
        <p>This should show a default item image</p>
    </div>
    
    <div class="test-image">
        <h3>File Path Test</h3>
        <img src="images/items/default.svg" alt="SVG Default" style="width: 50px; height: 50px;" onerror="this.style.border='2px solid red'; this.alt='FAILED';">
        <p>This tests the file path (may fail)</p>
    </div>
    
    <div class="test-image">
        <h3>Shop Items Preview</h3>
        <?php
        require_once "db.php";
        $itemsQuery = "SELECT id, name, image_url FROM shop_items LIMIT 3";
        $result = $conn->query($itemsQuery);
        
        if ($result->num_rows > 0) {
            while ($item = $result->fetch_assoc()) {
                $image_path = '.' . $item['image_url'];
                $image_exists = !empty($item['image_url']) && 
                               $item['image_url'] !== 'default' && 
                               file_exists($image_path);
                
                if ($image_exists) {
                    $display_image = $item['image_url'];
                } else {
                    $display_image = $default_image;
                }
                
                echo "<div style='margin: 10px; display: inline-block;'>";
                echo "<img src='" . htmlspecialchars($display_image) . "' alt='" . htmlspecialchars($item['name']) . "' style='width: 50px; height: 50px;'>";
                echo "<br><small>" . htmlspecialchars($item['name']) . "</small>";
                echo "</div>";
            }
        }
        $conn->close();
        ?>
    </div>
    
    <p><a href="manage_shop.php" style="color: #d4af37;">Go to Shop Management</a></p>
</body>
</html>
