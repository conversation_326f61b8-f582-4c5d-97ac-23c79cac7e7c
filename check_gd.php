<?php
// Check GD Extension Status
echo "<h2>GD Extension Status Check</h2>";
echo "<hr>";

// Check if GD is loaded
if (extension_loaded('gd')) {
    echo "<p style='color: green;'>✓ GD Extension is ENABLED</p>";
    
    // Get GD info
    $gd_info = gd_info();
    echo "<h3>GD Information:</h3>";
    echo "<ul>";
    foreach ($gd_info as $key => $value) {
        $status = $value ? 'Yes' : 'No';
        echo "<li><strong>$key:</strong> $status</li>";
    }
    echo "</ul>";
    
    // Test image functions
    echo "<h3>Image Function Tests:</h3>";
    $functions = [
        'imagecreate',
        'imagecreatefromjpeg',
        'imagecreatefrompng',
        'imagecreatefromgif',
        'imagecreatefromwebp',
        'imagecopyresampled',
        'imagepng',
        'imagejpeg'
    ];
    
    echo "<ul>";
    foreach ($functions as $func) {
        $exists = function_exists($func);
        $status = $exists ? "<span style='color: green;'>✓ Available</span>" : "<span style='color: red;'>✗ Missing</span>";
        echo "<li><strong>$func():</strong> $status</li>";
    }
    echo "</ul>";
    
} else {
    echo "<p style='color: red;'>✗ GD Extension is NOT ENABLED</p>";
    echo "<h3>How to Enable GD in XAMPP:</h3>";
    echo "<ol>";
    echo "<li>Open your XAMPP Control Panel</li>";
    echo "<li>Click 'Config' next to Apache</li>";
    echo "<li>Select 'PHP (php.ini)'</li>";
    echo "<li>Find the line: <code>;extension=gd</code></li>";
    echo "<li>Remove the semicolon to make it: <code>extension=gd</code></li>";
    echo "<li>Save the file</li>";
    echo "<li>Restart Apache in XAMPP</li>";
    echo "<li>Refresh this page to test</li>";
    echo "</ol>";
    
    echo "<p><strong>Alternative locations to check:</strong></p>";
    echo "<ul>";
    echo "<li>Look for: <code>;extension=php_gd2.dll</code> and change to <code>extension=php_gd2.dll</code></li>";
    echo "<li>Or: <code>;extension=gd2</code> and change to <code>extension=gd2</code></li>";
    echo "</ul>";
}

echo "<hr>";
echo "<h3>PHP Configuration:</h3>";
echo "<p><strong>PHP Version:</strong> " . phpversion() . "</p>";
echo "<p><strong>PHP INI File:</strong> " . php_ini_loaded_file() . "</p>";

// Show upload settings
echo "<h3>Upload Settings:</h3>";
echo "<ul>";
echo "<li><strong>upload_max_filesize:</strong> " . ini_get('upload_max_filesize') . "</li>";
echo "<li><strong>post_max_size:</strong> " . ini_get('post_max_size') . "</li>";
echo "<li><strong>max_file_uploads:</strong> " . ini_get('max_file_uploads') . "</li>";
echo "<li><strong>memory_limit:</strong> " . ini_get('memory_limit') . "</li>";
echo "</ul>";

echo "<hr>";
echo "<p><a href='manage_shop.php'>← Back to Shop Management</a></p>";
?>
