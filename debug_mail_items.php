<?php
echo "Debugging mail and inventory after purchase...\n";

require_once "db.php";

if ($conn->connect_error) {
    die("Connection failed: " . $conn->connect_error);
}

echo "✓ Database connection successful!\n\n";

// Check recent mail records
echo "=== RECENT MAIL RECORDS ===\n";
$mailQuery = "SELECT * FROM mail ORDER BY mailUniqueId DESC LIMIT 5";
$result = $conn->query($mailQuery);

if ($result && $result->num_rows > 0) {
    while ($mail = $result->fetch_assoc()) {
        echo "Mail ID: {$mail['mailUniqueId']}\n";
        echo "  Recipient: {$mail['mailRecipientId']}\n";
        echo "  Sender: {$mail['senderName']}\n";
        echo "  Title: {$mail['mailTitle']}\n";
        echo "  Message: {$mail['mailMessage']}\n";
        echo "  Attached Item: {$mail['attachedItemId']}\n";
        echo "  Kinah: {$mail['attachedKinahCount']}\n";
        echo "  Unread: {$mail['unread']}\n";
        echo "  Express: {$mail['express']}\n";
        echo "  Received: {$mail['recievedTime']}\n";
        echo "---\n";
    }
} else {
    echo "No mail records found\n";
}

// Check recent inventory records
echo "\n=== RECENT INVENTORY RECORDS ===\n";
$inventoryQuery = "SELECT * FROM inventory ORDER BY itemUniqueId DESC LIMIT 5";
$result = $conn->query($inventoryQuery);

if ($result && $result->num_rows > 0) {
    while ($item = $result->fetch_assoc()) {
        echo "Item Unique ID: {$item['itemUniqueId']}\n";
        echo "  Item ID: {$item['itemId']}\n";
        echo "  Count: {$item['itemCount']}\n";
        echo "  Owner: {$item['itemOwner']}\n";
        echo "  Location: {$item['itemLocation']}\n";
        echo "  Color: {$item['itemColor']}\n";
        echo "  Equipped: {$item['isEquiped']}\n";
        echo "  Soul Bound: {$item['isSoulBound']}\n";
        echo "  Slot: {$item['slot']}\n";
        echo "---\n";
    }
} else {
    echo "No inventory records found\n";
}

// Check if there are any mail-inventory mismatches
echo "\n=== CHECKING MAIL-INVENTORY LINKS ===\n";
$linkQuery = "
    SELECT m.mailUniqueId, m.mailRecipientId, m.senderName, m.attachedItemId,
           i.itemUniqueId, i.itemId, i.itemCount, i.itemLocation
    FROM mail m
    LEFT JOIN inventory i ON m.attachedItemId = i.itemUniqueId
    WHERE m.attachedItemId > 0
    ORDER BY m.mailUniqueId DESC
    LIMIT 5
";

$result = $conn->query($linkQuery);
if ($result && $result->num_rows > 0) {
    while ($row = $result->fetch_assoc()) {
        echo "Mail {$row['mailUniqueId']} -> Item {$row['attachedItemId']}\n";
        if ($row['itemUniqueId']) {
            echo "  ✓ Item exists: ID {$row['itemId']}, Count {$row['itemCount']}, Location {$row['itemLocation']}\n";
        } else {
            echo "  ✗ Item missing! Mail references item {$row['attachedItemId']} but it doesn't exist\n";
        }
    }
} else {
    echo "No mail with attachments found\n";
}

// Check your specific character's mail
echo "\n=== CHECKING CHARACTER MAIL ===\n";
$characterId = 1217; // Pinkert
$charMailQuery = "
    SELECT m.*, p.name as character_name
    FROM mail m
    JOIN players p ON m.mailRecipientId = p.id
    WHERE m.mailRecipientId = ?
    ORDER BY m.mailUniqueId DESC
    LIMIT 3
";

$stmt = $conn->prepare($charMailQuery);
$stmt->bind_param("i", $characterId);
$stmt->execute();
$result = $stmt->get_result();

if ($result && $result->num_rows > 0) {
    while ($mail = $result->fetch_assoc()) {
        echo "Character: {$mail['character_name']} (ID: {$mail['mailRecipientId']})\n";
        echo "  Mail: {$mail['mailTitle']} from {$mail['senderName']}\n";
        echo "  Attached Item ID: {$mail['attachedItemId']}\n";
        echo "  Unread: " . ($mail['unread'] ? 'Yes' : 'No') . "\n";
        
        // Check if the attached item exists
        if ($mail['attachedItemId'] > 0) {
            $itemCheckQuery = "SELECT * FROM inventory WHERE itemUniqueId = ?";
            $itemStmt = $conn->prepare($itemCheckQuery);
            $itemStmt->bind_param("i", $mail['attachedItemId']);
            $itemStmt->execute();
            $itemResult = $itemStmt->get_result();
            
            if ($itemResult && $itemResult->num_rows > 0) {
                $item = $itemResult->fetch_assoc();
                echo "  ✓ Item exists: Game Item {$item['itemId']} x{$item['itemCount']} at location {$item['itemLocation']}\n";
            } else {
                echo "  ✗ Item missing! Referenced item {$mail['attachedItemId']} not found in inventory\n";
            }
        }
        echo "---\n";
    }
} else {
    echo "No mail found for character ID $characterId\n";
}

// Compare with working mail example from your SQL
echo "\n=== COMPARING WITH WORKING EXAMPLE ===\n";
echo "Your SQL shows a working mail example:\n";
echo "Mail ID: 1, Recipient: 106932, Sender: 'Web Shop', Item: 106967\n";
echo "Let's check if this pattern works...\n";

// Check the working example structure
$workingQuery = "SELECT * FROM mail WHERE mailUniqueId = 1";
$result = $conn->query($workingQuery);
if ($result && $result->num_rows > 0) {
    $working = $result->fetch_assoc();
    echo "Working example structure:\n";
    foreach ($working as $key => $value) {
        echo "  $key: $value\n";
    }
} else {
    echo "Working example not found in database\n";
}

$conn->close();
?>
