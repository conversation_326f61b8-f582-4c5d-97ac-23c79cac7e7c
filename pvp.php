<?php
require_once "db.php";

// Create database connection
$conn = new mysqli($servername, $username, $password, $dbname);
if ($conn->connect_error) {
    die("Connection failed: " . $conn->connect_error);
}

// Function to calculate level from experience
function calculateLevel($exp) {
    if ($exp < 1000) return 1;
    if ($exp < 5000) return 10;
    if ($exp < 50000) return 20;
    if ($exp < 200000) return 30;
    if ($exp < 500000) return 40;
    if ($exp < 1000000) return 50;
    if ($exp < 2000000) return 60;
    return 65;
}

// Function to time ago
function timeAgo($datetime) {
    $time = time() - strtotime($datetime);
    if ($time < 60) return 'just now';
    if ($time < 3600) return floor($time/60) . ' minutes ago';
    if ($time < 86400) return floor($time/3600) . ' hours ago';
    if ($time < 2592000) return floor($time/86400) . ' days ago';
    return date('M j, Y', strtotime($datetime));
}

// Get top PvP killers
$topKillersQuery = "
    SELECT 
        p.name,
        p.player_class,
        p.race,
        p.exp,
        ar.all_kill,
        ar.daily_kill,
        ar.weekly_kill,
        ar.gp,
        ar.ap,
        ar.rank
    FROM players p
    LEFT JOIN abyss_rank ar ON p.id = ar.player_id
    WHERE ar.all_kill > 0
    ORDER BY ar.all_kill DESC
    LIMIT 20
";

$topKillersResult = $conn->query($topKillersQuery);

// Get most active PvP players (daily kills)
$dailyKillersQuery = "
    SELECT 
        p.name,
        p.player_class,
        p.race,
        p.exp,
        ar.daily_kill,
        ar.weekly_kill,
        ar.all_kill,
        ar.daily_ap,
        ar.gp
    FROM players p
    LEFT JOIN abyss_rank ar ON p.id = ar.player_id
    WHERE ar.daily_kill > 0
    ORDER BY ar.daily_kill DESC
    LIMIT 15
";

$dailyKillersResult = $conn->query($dailyKillersQuery);

// Get achievement-like statistics
$achievementsQuery = "
    SELECT 
        p.name,
        p.player_class,
        p.race,
        p.exp,
        ar.all_kill,
        ar.gp,
        ar.ap,
        ar.rank,
        CASE 
            WHEN ar.all_kill >= 1000 THEN 'Legendary Killer'
            WHEN ar.all_kill >= 500 THEN 'Master Assassin'
            WHEN ar.all_kill >= 100 THEN 'Veteran Warrior'
            WHEN ar.all_kill >= 50 THEN 'Skilled Fighter'
            WHEN ar.all_kill >= 10 THEN 'Warrior'
            ELSE 'Novice'
        END as achievement_title,
        CASE 
            WHEN ar.gp >= 1000000 THEN 'Glory Legend'
            WHEN ar.gp >= 500000 THEN 'Glory Master'
            WHEN ar.gp >= 100000 THEN 'Glory Veteran'
            WHEN ar.gp >= 50000 THEN 'Glory Seeker'
            ELSE 'Glory Novice'
        END as glory_title,
        CASE 
            WHEN ar.ap >= 1000000 THEN 'Abyss Lord'
            WHEN ar.ap >= 500000 THEN 'Abyss Master'
            WHEN ar.ap >= 100000 THEN 'Abyss Veteran'
            WHEN ar.ap >= 50000 THEN 'Abyss Walker'
            ELSE 'Abyss Novice'
        END as abyss_title
    FROM players p
    LEFT JOIN abyss_rank ar ON p.id = ar.player_id
    WHERE ar.player_id IS NOT NULL
    ORDER BY (ar.all_kill + ar.gp/1000 + ar.ap/1000) DESC
    LIMIT 25
";

$achievementsResult = $conn->query($achievementsQuery);

// Get class-based PvP statistics
$classStatsQuery = "
    SELECT 
        p.player_class,
        COUNT(*) as player_count,
        AVG(ar.all_kill) as avg_kills,
        MAX(ar.all_kill) as max_kills,
        SUM(ar.all_kill) as total_kills,
        AVG(ar.gp) as avg_gp,
        AVG(ar.ap) as avg_ap
    FROM players p
    LEFT JOIN abyss_rank ar ON p.id = ar.player_id
    WHERE ar.player_id IS NOT NULL
    GROUP BY p.player_class
    ORDER BY avg_kills DESC
";

$classStatsResult = $conn->query($classStatsQuery);

$conn->close();
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>PvP & Achievements - Aion-Blitz Server</title>
    <link rel="stylesheet" href="styles.css">
    <link href="https://fonts.googleapis.com/css2?family=Cinzel:wght@400;600;700&family=Open+Sans:wght@300;400;600&display=swap" rel="stylesheet">
</head>
<body>
    <!-- Video Background -->
    <div class="video-background">
        <video autoplay muted loop playsinline>
            <source src="images/bg_main.mp4" type="video/mp4">
        </video>
    </div>
    <div class="video-overlay"></div>

    <!-- Top Navigation -->
    <nav class="top-nav">
        <div class="nav-left">
            <span class="server-time">🕐 Server Time: 00:00</span>
            <span class="server-status">🟢 Server Status</span>
        </div>
        <div class="nav-center">
            <ul class="nav-menu">
                <li><a href="index.php">🏠 Home</a></li>
                <li><a href="index.php#faq">❓ F.A.Q</a></li>
                <li><a href="register.php">📝 Register</a></li>
                <li><a href="index.php#schedule">📅 Schedule</a></li>
                <li><a href="ranks.php">🏆 Ranks</a></li>
                <li><a href="activity.php">📊 Activity</a></li>
                <li><a href="pvp.php" class="active">⚔️ PvP</a></li>
                <li><a href="index.php#discord">💬 Discord</a></li>
            </ul>
        </div>
        <div class="nav-right">
            <div class="user-section">
                <?php
                session_start();
                if (isset($_SESSION["username"])) {
                    echo "<span class='welcome-user'>Welcome, " . $_SESSION["username"] . "!</span>";
                    echo "<a href='logout.php' class='logout-btn'>Logout</a>";
                } else {
                    echo "<a href='signin.php' class='signin-btn'>Sign In</a>";
                }
                ?>
            </div>
        </div>
    </nav>

    <!-- PvP Section -->
    <section class="pvp-section">
        <div class="pvp-container">
            <div class="pvp-header">
                <h1>⚔️ PvP & Achievements</h1>
                <p class="pvp-subtitle">Battle statistics and player achievements</p>
            </div>

            <div class="pvp-tabs">
                <button class="pvp-tab active" data-tab="killers">💀 Top Killers</button>
                <button class="pvp-tab" data-tab="daily">🔥 Daily Champions</button>
                <button class="pvp-tab" data-tab="achievements">🏆 Achievements</button>
                <button class="pvp-tab" data-tab="classes">📊 Class Stats</button>
            </div>

            <!-- Top Killers Tab -->
            <div class="pvp-content active" id="killers">
                <div class="pvp-grid">
                    <div class="pvp-card">
                        <div class="card-header">
                            <h3>💀 All-Time Top Killers</h3>
                            <span class="card-badge">Legendary</span>
                        </div>
                        <div class="pvp-list">
                            <?php
                            if ($topKillersResult && $topKillersResult->num_rows > 0) {
                                $position = 1;
                                while ($killer = $topKillersResult->fetch_assoc()) {
                                    $level = calculateLevel($killer['exp']);
                                    echo "<div class='pvp-item'>";
                                    echo "<div class='pvp-rank'>";
                                    if ($position <= 3) {
                                        $medals = ['🥇', '🥈', '🥉'];
                                        echo $medals[$position - 1];
                                    } else {
                                        echo "#$position";
                                    }
                                    echo "</div>";
                                    echo "<div class='pvp-player'>";
                                    echo "<div class='player-info'>";
                                    echo "<a href='player.php?name=" . urlencode($killer['name']) . "' class='player-link'>" . htmlspecialchars($killer['name']) . "</a>";
                                    echo "<span class='player-details'>Lv.$level " . $killer['player_class'] . "</span>";
                                    echo "</div>";
                                    echo "<div class='pvp-stats'>";
                                    echo "<span class='kill-count'>" . number_format($killer['all_kill']) . " kills</span>";
                                    echo "<span class='gp-count'>" . number_format($killer['gp']) . " GP</span>";
                                    echo "</div>";
                                    echo "</div>";
                                    echo "</div>";
                                    $position++;
                                }
                            } else {
                                echo "<div class='no-data'>No PvP data available</div>";
                            }
                            ?>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Daily Champions Tab -->
            <div class="pvp-content" id="daily">
                <div class="pvp-grid">
                    <div class="pvp-card">
                        <div class="card-header">
                            <h3>🔥 Today's Champions</h3>
                            <span class="card-badge">Active</span>
                        </div>
                        <div class="pvp-list">
                            <?php
                            if ($dailyKillersResult && $dailyKillersResult->num_rows > 0) {
                                $position = 1;
                                while ($daily = $dailyKillersResult->fetch_assoc()) {
                                    $level = calculateLevel($daily['exp']);
                                    if ($daily['daily_kill'] > 0) {
                                        echo "<div class='pvp-item'>";
                                        echo "<div class='pvp-rank'>#$position</div>";
                                        echo "<div class='pvp-player'>";
                                        echo "<div class='player-info'>";
                                        echo "<a href='player.php?name=" . urlencode($daily['name']) . "' class='player-link'>" . htmlspecialchars($daily['name']) . "</a>";
                                        echo "<span class='player-details'>Lv.$level " . $daily['player_class'] . "</span>";
                                        echo "</div>";
                                        echo "<div class='pvp-stats'>";
                                        echo "<span class='kill-count'>" . $daily['daily_kill'] . " today</span>";
                                        echo "<span class='ap-count'>" . number_format($daily['daily_ap']) . " AP</span>";
                                        echo "</div>";
                                        echo "</div>";
                                        echo "</div>";
                                        $position++;
                                    }
                                }
                            } else {
                                echo "<div class='no-data'>No daily PvP activity yet</div>";
                            }
                            ?>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Achievements Tab -->
            <div class="pvp-content" id="achievements">
                <div class="achievements-grid">
                    <?php
                    if ($achievementsResult && $achievementsResult->num_rows > 0) {
                        while ($achiever = $achievementsResult->fetch_assoc()) {
                            $level = calculateLevel($achiever['exp']);
                            echo "<div class='achievement-card'>";
                            echo "<div class='achievement-player'>";
                            echo "<a href='player.php?name=" . urlencode($achiever['name']) . "' class='achievement-name'>" . htmlspecialchars($achiever['name']) . "</a>";
                            echo "<div class='achievement-class'>Lv.$level " . $achiever['player_class'] . "</div>";
                            echo "</div>";
                            echo "<div class='achievement-titles'>";
                            echo "<span class='achievement-badge kill-badge'>" . $achiever['achievement_title'] . "</span>";
                            echo "<span class='achievement-badge glory-badge'>" . $achiever['glory_title'] . "</span>";
                            echo "<span class='achievement-badge abyss-badge'>" . $achiever['abyss_title'] . "</span>";
                            echo "</div>";
                            echo "<div class='achievement-stats'>";
                            echo "<span>" . number_format($achiever['all_kill']) . " kills</span>";
                            echo "<span>" . number_format($achiever['gp']) . " GP</span>";
                            echo "<span>" . number_format($achiever['ap']) . " AP</span>";
                            echo "</div>";
                            echo "</div>";
                        }
                    }
                    ?>
                </div>
            </div>

            <!-- Class Stats Tab -->
            <div class="pvp-content" id="classes">
                <div class="class-stats-grid">
                    <?php
                    if ($classStatsResult && $classStatsResult->num_rows > 0) {
                        while ($classData = $classStatsResult->fetch_assoc()) {
                            echo "<div class='class-stat-card'>";
                            echo "<div class='class-header'>";
                            echo "<h4>" . $classData['player_class'] . "</h4>";
                            echo "<span class='player-count'>" . $classData['player_count'] . " players</span>";
                            echo "</div>";
                            echo "<div class='class-stats'>";
                            echo "<div class='stat-row'>";
                            echo "<span class='stat-label'>Avg Kills:</span>";
                            echo "<span class='stat-value'>" . number_format($classData['avg_kills'], 1) . "</span>";
                            echo "</div>";
                            echo "<div class='stat-row'>";
                            echo "<span class='stat-label'>Max Kills:</span>";
                            echo "<span class='stat-value'>" . number_format($classData['max_kills']) . "</span>";
                            echo "</div>";
                            echo "<div class='stat-row'>";
                            echo "<span class='stat-label'>Total Kills:</span>";
                            echo "<span class='stat-value'>" . number_format($classData['total_kills']) . "</span>";
                            echo "</div>";
                            echo "<div class='stat-row'>";
                            echo "<span class='stat-label'>Avg GP:</span>";
                            echo "<span class='stat-value'>" . number_format($classData['avg_gp']) . "</span>";
                            echo "</div>";
                            echo "</div>";
                            echo "</div>";
                        }
                    }
                    ?>
                </div>
            </div>
        </div>
    </section>

    <script src="script.js"></script>
    <script>
        // Tab switching functionality
        document.addEventListener('DOMContentLoaded', function() {
            const tabs = document.querySelectorAll('.pvp-tab');
            const contents = document.querySelectorAll('.pvp-content');
            
            tabs.forEach(tab => {
                tab.addEventListener('click', function() {
                    const targetTab = this.getAttribute('data-tab');
                    
                    // Remove active class from all tabs and contents
                    tabs.forEach(t => t.classList.remove('active'));
                    contents.forEach(c => c.classList.remove('active'));
                    
                    // Add active class to clicked tab and corresponding content
                    this.classList.add('active');
                    document.getElementById(targetTab).classList.add('active');
                });
            });
        });
    </script>
</body>
</html>
