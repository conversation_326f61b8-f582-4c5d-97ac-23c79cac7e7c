<?php
// Simple shop manager without login requirement (for testing)
require_once "db.php";

// Handle form submissions
$message = "";
if ($_POST) {
    if (isset($_POST['add_item'])) {
        $item_id = intval($_POST['item_id']);
        $name = $_POST['name'];
        $description = $_POST['description'];
        $price = intval($_POST['price']);
        $quantity = intval($_POST['quantity']);
        $rarity = $_POST['rarity'];
        
        $insertQuery = "INSERT INTO shop_items (item_id, name, description, price, quantity, image_url, category_id, rarity) VALUES (?, ?, ?, ?, ?, 'default', 1, ?)";
        $stmt = $conn->prepare($insertQuery);
        $stmt->bind_param("issiiis", $item_id, $name, $description, $price, $quantity, $quantity, $rarity);
        
        if ($stmt->execute()) {
            $message = "<span style='color:#4CAF50;'>✓ Item added successfully!</span>";
        } else {
            $message = "<span style='color:#f44336;'>✗ Error adding item: " . $stmt->error . "</span>";
        }
    }
    
    if (isset($_POST['delete_item'])) {
        $delete_id = intval($_POST['delete_id']);
        $deleteQuery = "DELETE FROM shop_items WHERE id = ?";
        $stmt = $conn->prepare($deleteQuery);
        $stmt->bind_param("i", $delete_id);
        
        if ($stmt->execute()) {
            $message = "<span style='color:#4CAF50;'>✓ Item deleted successfully!</span>";
        } else {
            $message = "<span style='color:#f44336;'>✗ Error deleting item: " . $stmt->error . "</span>";
        }
    }
}

// Get current shop items
$itemsQuery = "SELECT * FROM shop_items ORDER BY price ASC";
$itemsResult = $conn->query($itemsQuery);
?>

<!DOCTYPE html>
<html>
<head>
    <title>Simple Shop Manager - Aion-Blitz</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background: #1a1a1a; color: #fff; }
        .container { max-width: 1200px; margin: 0 auto; }
        .form-group { margin: 15px 0; }
        label { display: inline-block; width: 120px; font-weight: bold; }
        input, select, textarea { padding: 8px; margin: 5px; width: 200px; background: #333; color: #fff; border: 1px solid #d4af37; }
        button { padding: 10px 20px; background: #d4af37; color: #000; border: none; cursor: pointer; margin: 5px; }
        button:hover { background: #f4d03f; }
        .delete-btn { background: #f44336; color: white; }
        table { width: 100%; border-collapse: collapse; margin: 20px 0; }
        th, td { padding: 10px; border: 1px solid #333; text-align: left; }
        th { background: #333; }
        .message { padding: 15px; margin: 15px 0; border-radius: 5px; background: rgba(255,255,255,0.1); }
    </style>
</head>
<body>
    <div class="container">
        <h1>🛒 Simple Shop Manager</h1>
        <p><em>No login required - for testing purposes</em></p>
        
        <?php if ($message): ?>
            <div class="message"><?php echo $message; ?></div>
        <?php endif; ?>
        
        <h2>Add New Item</h2>
        <form method="POST">
            <div class="form-group">
                <label>Item ID:</label>
                <input type="number" name="item_id" required placeholder="e.g. 164000073">
            </div>
            <div class="form-group">
                <label>Name:</label>
                <input type="text" name="name" required placeholder="e.g. Health Potion (x5)">
            </div>
            <div class="form-group">
                <label>Description:</label>
                <textarea name="description" required placeholder="Item description"></textarea>
            </div>
            <div class="form-group">
                <label>Price (MP):</label>
                <input type="number" name="price" required placeholder="e.g. 50">
            </div>
            <div class="form-group">
                <label>Quantity:</label>
                <input type="number" name="quantity" required placeholder="e.g. 5">
            </div>
            <div class="form-group">
                <label>Rarity:</label>
                <select name="rarity" required>
                    <option value="Common">Common</option>
                    <option value="Superior">Superior</option>
                    <option value="Heroic">Heroic</option>
                    <option value="Fabled">Fabled</option>
                    <option value="Eternal">Eternal</option>
                    <option value="Mythic">Mythic</option>
                    <option value="Legendary">Legendary</option>
                </select>
            </div>
            <button type="submit" name="add_item">Add Item</button>
        </form>
        
        <h2>Current Shop Items</h2>
        <table>
            <tr>
                <th>ID</th>
                <th>Item ID</th>
                <th>Name</th>
                <th>Description</th>
                <th>Price</th>
                <th>Quantity</th>
                <th>Rarity</th>
                <th>Actions</th>
            </tr>
            <?php if ($itemsResult && $itemsResult->num_rows > 0): ?>
                <?php while ($item = $itemsResult->fetch_assoc()): ?>
                    <tr>
                        <td><?php echo $item['id']; ?></td>
                        <td><?php echo $item['item_id']; ?></td>
                        <td><?php echo htmlspecialchars($item['name']); ?></td>
                        <td><?php echo htmlspecialchars($item['description']); ?></td>
                        <td><?php echo $item['price']; ?> MP</td>
                        <td><?php echo $item['quantity']; ?></td>
                        <td><?php echo $item['rarity']; ?></td>
                        <td>
                            <form method="POST" style="display: inline;">
                                <input type="hidden" name="delete_id" value="<?php echo $item['id']; ?>">
                                <button type="submit" name="delete_item" class="delete-btn" onclick="return confirm('Delete this item?')">Delete</button>
                            </form>
                        </td>
                    </tr>
                <?php endwhile; ?>
            <?php else: ?>
                <tr><td colspan="8">No items in shop</td></tr>
            <?php endif; ?>
        </table>
        
        <h2>Links</h2>
        <p>
            <a href="shop.php" style="color: #d4af37;">View Customer Shop</a> | 
            <a href="manage_shop.php" style="color: #d4af37;">Full Shop Manager</a> | 
            <a href="dashboard.php" style="color: #d4af37;">Dashboard</a>
        </p>
        
        <h2>Notes</h2>
        <ul>
            <li>This simple manager doesn't support image uploads</li>
            <li>All items will use default placeholder images</li>
            <li>For full features, use the main manage_shop.php (requires login)</li>
            <li>Items added here will appear in the customer shop</li>
        </ul>
    </div>
</body>
</html>

<?php $conn->close(); ?>
