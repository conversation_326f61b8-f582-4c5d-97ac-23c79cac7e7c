<?php
echo "Adding Donation Points to your account...\n";

require_once "db.php";

// Test database connection
if ($conn->connect_error) {
    die("Connection failed: " . $conn->connect_error);
}

echo "✓ Database connection successful!\n";

// Ask for username (you can change this to your actual username)
$username = "test22"; // Change this to your actual username (available: admin, test, test22)
$pointsToAdd = 1000; // Adding 1000 DP for testing

echo "Adding $pointsToAdd DP to user: $username\n";

// First, let's check if the account exists
$accountQuery = "SELECT id, name FROM account_data WHERE name = ?";
$stmt = $conn->prepare($accountQuery);
$stmt->bind_param("s", $username);
$stmt->execute();
$result = $stmt->get_result();

if ($result->num_rows == 0) {
    echo "✗ Account '$username' not found!\n";
    echo "Available accounts:\n";
    
    // Show available accounts
    $allAccountsQuery = "SELECT name FROM account_data LIMIT 10";
    $allAccounts = $conn->query($allAccountsQuery);
    if ($allAccounts && $allAccounts->num_rows > 0) {
        while ($row = $allAccounts->fetch_assoc()) {
            echo "  - " . $row['name'] . "\n";
        }
    }
    
    echo "\nPlease edit this script and change the \$username variable to your actual username.\n";
    exit;
}

$account = $result->fetch_assoc();
$accountId = $account['id'];
echo "✓ Found account: {$account['name']} (ID: $accountId)\n";

// Check if account_balance table exists
$result = $conn->query("SHOW TABLES LIKE 'account_balance'");
if ($result->num_rows == 0) {
    echo "✗ account_balance table doesn't exist. Creating it...\n";
    
    // Create account_balance table
    $createBalanceQuery = "
    CREATE TABLE `account_balance` (
        `account_id` int(11) NOT NULL,
        `price_id` int(11) NOT NULL DEFAULT 1,
        `value` bigint(20) NOT NULL DEFAULT 0,
        PRIMARY KEY (`account_id`, `price_id`)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_general_ci;
    ";
    
    if ($conn->query($createBalanceQuery) === TRUE) {
        echo "✓ account_balance table created successfully!\n";
    } else {
        die("✗ Error creating account_balance table: " . $conn->error . "\n");
    }
}

// Check if price_type table exists
$result = $conn->query("SHOW TABLES LIKE 'price_type'");
if ($result->num_rows == 0) {
    echo "✗ price_type table doesn't exist. Creating it...\n";
    
    // Create price_type table
    $createPriceTypeQuery = "
    CREATE TABLE `price_type` (
        `price_id` int(11) NOT NULL AUTO_INCREMENT,
        `price_name` varchar(50) NOT NULL,
        `symbolic` varchar(10) NOT NULL,
        PRIMARY KEY (`price_id`)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_general_ci;
    ";
    
    if ($conn->query($createPriceTypeQuery) === TRUE) {
        echo "✓ price_type table created successfully!\n";
        
        // Add Donation Points currency
        $insertPriceType = "INSERT INTO price_type (price_id, price_name, symbolic) VALUES (1, 'Donation Points', 'DP')";
        if ($conn->query($insertPriceType) === TRUE) {
            echo "✓ Donation Points currency added!\n";
        } else {
            echo "✗ Error adding currency: " . $conn->error . "\n";
        }
    } else {
        die("✗ Error creating price_type table: " . $conn->error . "\n");
    }
}

// Check current balance
$balanceQuery = "SELECT value FROM account_balance WHERE account_id = ? AND price_id = 1";
$stmt = $conn->prepare($balanceQuery);
$stmt->bind_param("i", $accountId);
$stmt->execute();
$balanceResult = $stmt->get_result();

if ($balanceResult->num_rows > 0) {
    // Update existing balance
    $currentBalance = $balanceResult->fetch_assoc()['value'];
    $newBalance = $currentBalance + $pointsToAdd;
    
    echo "Current balance: $currentBalance DP\n";
    
    $updateQuery = "UPDATE account_balance SET value = ? WHERE account_id = ? AND price_id = 1";
    $updateStmt = $conn->prepare($updateQuery);
    $updateStmt->bind_param("ii", $newBalance, $accountId);
    
    if ($updateStmt->execute()) {
        echo "✓ Balance updated! New balance: $newBalance DP\n";
    } else {
        echo "✗ Error updating balance: " . $updateStmt->error . "\n";
    }
} else {
    // Insert new balance record
    echo "No existing balance found. Creating new balance record...\n";
    
    $insertQuery = "INSERT INTO account_balance (account_id, price_id, value) VALUES (?, 1, ?)";
    $insertStmt = $conn->prepare($insertQuery);
    $insertStmt->bind_param("ii", $accountId, $pointsToAdd);
    
    if ($insertStmt->execute()) {
        echo "✓ Balance created! New balance: $pointsToAdd DP\n";
    } else {
        echo "✗ Error creating balance: " . $insertStmt->error . "\n";
    }
}

echo "\n✅ Donation Points added successfully!\n";
echo "🎉 You now have DP to test purchasing items in the shop!\n";
echo "Go to your website dashboard to see your new balance and test the shop!\n";

$conn->close();
?>
