<?php
// Verify all donation-related tables exist and are properly structured
require_once "db.php";

echo "Verifying Donation System Database Tables\n";
echo "=========================================\n";

// Check if donations table exists
$tables = ['donations', 'account_balance', 'balance_history', 'price_type'];

foreach ($tables as $table) {
    $result = $conn->query("SHOW TABLES LIKE '$table'");
    if ($result->num_rows > 0) {
        echo "✓ Table '$table' exists\n";
        
        // Show table structure
        $structure = $conn->query("DESCRIBE $table");
        echo "  Structure:\n";
        while ($row = $structure->fetch_assoc()) {
            echo "    - {$row['Field']}: {$row['Type']}\n";
        }
        echo "\n";
    } else {
        echo "✗ Table '$table' missing\n";
    }
}

// Check if price_type has the correct data
echo "Checking price_type data:\n";
$priceResult = $conn->query("SELECT * FROM price_type");
if ($priceResult->num_rows > 0) {
    while ($row = $priceResult->fetch_assoc()) {
        echo "  - ID: {$row['price_id']}, Name: {$row['price_name']}, Symbol: {$row['symbolic']}\n";
    }
} else {
    echo "  - No price types found\n";
}

echo "\n";

// Check recent donations
echo "Recent donations:\n";
$donationResult = $conn->query("SELECT * FROM donations ORDER BY created_at DESC LIMIT 5");
if ($donationResult->num_rows > 0) {
    while ($row = $donationResult->fetch_assoc()) {
        echo "  - ID: {$row['id']}, User: {$row['user_id']}, Amount: {$row['amount']}, Order: {$row['paypal_order_id']}, Status: {$row['status']}\n";
    }
} else {
    echo "  - No donations found\n";
}

echo "\n✓ Database verification completed\n";

$conn->close();
?>
