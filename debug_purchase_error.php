<?php
echo "Debugging purchase system errors...\n";

require_once "db.php";

// Test database connection
if ($conn->connect_error) {
    die("Connection failed: " . $conn->connect_error);
}

echo "✓ Database connection successful!\n\n";

// Check required tables for purchase system
$requiredTables = [
    'account_data',
    'account_balance', 
    'price_type',
    'shop_items',
    'players',
    'inventory',
    'mail',
    'balance_history'
];

echo "=== CHECKING REQUIRED TABLES ===\n";
foreach ($requiredTables as $table) {
    $result = $conn->query("SHOW TABLES LIKE '$table'");
    if ($result->num_rows > 0) {
        echo "✓ $table exists\n";
        
        // Count records
        $countResult = $conn->query("SELECT COUNT(*) as count FROM $table");
        if ($countResult) {
            $count = $countResult->fetch_assoc()['count'];
            echo "  Records: $count\n";
        }
    } else {
        echo "✗ $table MISSING!\n";
    }
}

// Check if you have characters (required for purchases)
echo "\n=== CHECKING CHARACTERS ===\n";
$username = "test22"; // Change this to your username
echo "Checking characters for username: $username\n";

$accountQuery = "SELECT id FROM account_data WHERE name = ?";
$stmt = $conn->prepare($accountQuery);
$stmt->bind_param("s", $username);
$stmt->execute();
$accountResult = $stmt->get_result();
$account = $accountResult->fetch_assoc();

if ($account) {
    $accountId = $account['id'];
    echo "✓ Account ID: $accountId\n";
    
    $playersQuery = "SELECT id, name, player_class FROM players WHERE account_id = ?";
    $stmt = $conn->prepare($playersQuery);
    $stmt->bind_param("i", $accountId);
    $stmt->execute();
    $playersResult = $stmt->get_result();
    
    if ($playersResult->num_rows > 0) {
        echo "✓ Characters found:\n";
        while ($player = $playersResult->fetch_assoc()) {
            echo "  - ID: {$player['id']}, Name: {$player['name']}, Class: {$player['player_class']}\n";
        }
    } else {
        echo "✗ NO CHARACTERS FOUND!\n";
        echo "  This is likely the issue - you need to create a character in-game first!\n";
    }
} else {
    echo "✗ Account not found\n";
}

// Check if balance_history table exists and create if missing
echo "\n=== CHECKING/CREATING MISSING TABLES ===\n";

$result = $conn->query("SHOW TABLES LIKE 'balance_history'");
if ($result->num_rows == 0) {
    echo "Creating balance_history table...\n";
    
    $createHistoryQuery = "
    CREATE TABLE `balance_history` (
        `id` int(11) NOT NULL AUTO_INCREMENT,
        `date` datetime NOT NULL,
        `reference_id` varchar(50) NOT NULL,
        `account_id` int(11) NOT NULL,
        `account_name` varchar(50) NOT NULL,
        `price_id` int(11) NOT NULL,
        `amount` int(11) NOT NULL,
        `description` varchar(255) NOT NULL,
        `type` enum('IN','OUT') NOT NULL,
        PRIMARY KEY (`id`)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_general_ci;
    ";
    
    if ($conn->query($createHistoryQuery) === TRUE) {
        echo "✓ balance_history table created!\n";
    } else {
        echo "✗ Error creating balance_history: " . $conn->error . "\n";
    }
}

// Test a simple purchase simulation
echo "\n=== TESTING PURCHASE SIMULATION ===\n";
echo "Testing what happens when purchase_item.php is called...\n";

// Simulate the purchase request
$testData = [
    'item_id' => 1, // Shop item ID (not game item ID)
    'price' => 10,
    'quantity' => 5,
    'recipient' => [
        'type' => 'self',
        'character_id' => 1 // This might not exist
    ]
];

echo "Test purchase data:\n";
print_r($testData);

// Check if the shop item exists
$itemQuery = "SELECT id, item_id, name, price, quantity FROM shop_items WHERE id = 1";
$itemResult = $conn->query($itemQuery);

if ($itemResult && $itemResult->num_rows > 0) {
    $item = $itemResult->fetch_assoc();
    echo "✓ Shop item found:\n";
    print_r($item);
} else {
    echo "✗ Shop item with ID 1 not found!\n";
    
    // Show available shop items
    $allItemsQuery = "SELECT id, item_id, name, price FROM shop_items LIMIT 5";
    $allItems = $conn->query($allItemsQuery);
    if ($allItems && $allItems->num_rows > 0) {
        echo "Available shop items:\n";
        while ($row = $allItems->fetch_assoc()) {
            echo "  - Shop ID: {$row['id']}, Game Item ID: {$row['item_id']}, Name: {$row['name']}, Price: {$row['price']}\n";
        }
    }
}

echo "\n=== RECOMMENDATIONS ===\n";
echo "1. Make sure you have created a character in-game first\n";
echo "2. Use the correct shop item ID (not game item ID) when purchasing\n";
echo "3. Check browser console (F12) for JavaScript errors\n";
echo "4. Check Apache error logs for PHP errors\n";

$conn->close();
?>
