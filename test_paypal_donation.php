<?php
require_once "db.php";

echo "=== TESTING PAYPAL DONATION SYSTEM ===\n";

// Test configuration
$testAccount = "admin"; // Change this to your test account
$testAmount = 10.00; // Test donation amount
$testOrderId = "TEST_ORDER_" . time(); // Unique test order ID

echo "Test Account: $testAccount\n";
echo "Test Amount: €$testAmount\n";
echo "Test Order ID: $testOrderId\n\n";

// Get account info
$accountQuery = "SELECT id, name FROM account_data WHERE name = ?";
$stmt = $conn->prepare($accountQuery);
$stmt->bind_param("s", $testAccount);
$stmt->execute();
$accountResult = $stmt->get_result();

if ($accountResult->num_rows === 0) {
    die("✗ Test account '$testAccount' not found!\n");
}

$account = $accountResult->fetch_assoc();
$userId = $account['id'];
$accountName = $account['name'];

echo "✓ Found account: $accountName (ID: $userId)\n";

// Check current might before donation
$mightQuery = "SELECT might FROM might WHERE account_id = ?";
$stmt = $conn->prepare($mightQuery);
$stmt->bind_param("i", $userId);
$stmt->execute();
$mightResult = $stmt->get_result();

$currentMight = 0;
if ($mightResult->num_rows > 0) {
    $mightData = $mightResult->fetch_assoc();
    $currentMight = $mightData['might'];
}

echo "Current might before donation: $currentMight MP\n\n";

// Simulate the donation process (same logic as process_donation.php)
echo "=== SIMULATING DONATION PROCESS ===\n";

// 1. Insert donation record
$insertQuery = "
    INSERT INTO donations (user_id, amount, paypal_order_id, status, created_at)
    VALUES (?, ?, ?, 'completed', NOW())
";

$stmt = $conn->prepare($insertQuery);
$stmt->bind_param("ids", $userId, $testAmount, $testOrderId);

if ($stmt->execute()) {
    $donationId = $conn->insert_id;
    echo "✓ Donation record created (ID: $donationId)\n";
} else {
    die("✗ Failed to create donation record: " . $stmt->error . "\n");
}

// 2. Update membership level
$newMembership = 0;
if ($testAmount >= 50) $newMembership = 4; // Platinum
elseif ($testAmount >= 25) $newMembership = 3; // Gold
elseif ($testAmount >= 10) $newMembership = 2; // Silver
elseif ($testAmount >= 5) $newMembership = 1; // Bronze

if ($newMembership > 0) {
    $updateMembershipQuery = "
        UPDATE account_data
        SET membership = GREATEST(membership, ?)
        WHERE id = ?
    ";
    $stmt = $conn->prepare($updateMembershipQuery);
    $stmt->bind_param("ii", $newMembership, $userId);
    $stmt->execute();
    echo "✓ Membership updated to level $newMembership\n";
}

// 3. Add might points (1 euro = 1 might point)
$mightPoints = intval($testAmount);

// Check if might record exists
$mightCheckQuery = "SELECT might FROM might WHERE account_id = ?";
$stmt = $conn->prepare($mightCheckQuery);
$stmt->bind_param("i", $userId);
$stmt->execute();
$mightResult = $stmt->get_result();

if ($mightResult->num_rows > 0) {
    // Update existing might
    $updateMightQuery = "UPDATE might SET might = might + ? WHERE account_id = ?";
    $stmt = $conn->prepare($updateMightQuery);
    $stmt->bind_param("ii", $mightPoints, $userId);
    $stmt->execute();
    echo "✓ Added $mightPoints might points to existing balance\n";
} else {
    // Insert new might record
    $insertMightQuery = "INSERT INTO might (account_id, might) VALUES (?, ?)";
    $stmt = $conn->prepare($insertMightQuery);
    $stmt->bind_param("ii", $userId, $mightPoints);
    $stmt->execute();
    echo "✓ Created new might record with $mightPoints points\n";
}

// 4. Log the transaction
$historyQuery = "INSERT INTO balance_history (date, reference_id, account_id, account_name, price_id, amount, description, type) VALUES (NOW(), ?, ?, ?, 1, ?, 'PayPal Donation - Might Points', 'IN')";
$stmt = $conn->prepare($historyQuery);
$stmt->bind_param("ssii", $testOrderId, $userId, $accountName, $mightPoints);
$stmt->execute();
echo "✓ Transaction logged in balance_history\n";

// 5. Check final might balance
$finalMightQuery = "SELECT might FROM might WHERE account_id = ?";
$stmt = $conn->prepare($finalMightQuery);
$stmt->bind_param("i", $userId);
$stmt->execute();
$finalMightResult = $stmt->get_result();
$finalMightData = $finalMightResult->fetch_assoc();
$finalMight = $finalMightData['might'];

echo "\n=== RESULTS ===\n";
echo "Might before donation: $currentMight MP\n";
echo "Might points added: $mightPoints MP\n";
echo "Might after donation: $finalMight MP\n";
echo "Expected final might: " . ($currentMight + $mightPoints) . " MP\n";

if ($finalMight == ($currentMight + $mightPoints)) {
    echo "✅ SUCCESS: Donation system correctly added might points!\n";
} else {
    echo "❌ ERROR: Might points calculation is incorrect!\n";
}

echo "\n=== VERIFICATION ===\n";
echo "✓ Donation record created in 'donations' table\n";
echo "✓ Might points added to 'might' table\n";
echo "✓ Transaction logged in 'balance_history' table\n";
echo "✓ Membership level updated if applicable\n";

echo "\n🎉 PayPal donation system is now working with Might Points!\n";
echo "When users donate via PayPal, they will receive Might Points instead of DP.\n";

$conn->close();
?>
