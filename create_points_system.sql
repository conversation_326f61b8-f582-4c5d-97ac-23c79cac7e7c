-- Create the points/coins system for donations
-- This will allow players to get points from donations and spend them in a shop

-- Create account_balance table for storing player points/coins
CREATE TABLE IF NOT EXISTS `account_balance` (
  `account_id` int(11) NOT NULL,
  `account_name` varchar(50) NOT NULL,
  `price_id` int(11) NOT NULL,
  `value` int(11) NOT NULL DEFAULT 0,
  PRIMARY KEY (`account_id`, `price_id`),
  INDEX `account_id` (`account_id`),
  FOREIGN KEY (`account_id`) REFERENCES `account_data` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- Create price_type table for different types of currencies
CREATE TABLE IF NOT EXISTS `price_type` (
  `price_id` int(11) NOT NULL,
  `price_name` varchar(50) NOT NULL,
  `symbolic` varchar(10) NOT NULL,
  `icon_link` varchar(255) DEFAULT NULL,
  PRIMARY KEY (`price_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- Insert default currency types
INSERT INTO `price_type` (`price_id`, `price_name`, `symbolic`, `icon_link`) VALUES
(1, 'Donation Points', 'DP', 'coin-icon.png'),
(2, 'Premium Coins', 'PC', 'premium-coin.png')
ON DUPLICATE KEY UPDATE 
  `price_name` = VALUES(`price_name`),
  `symbolic` = VALUES(`symbolic`),
  `icon_link` = VALUES(`icon_link`);

-- Create balance_history table for tracking transactions
CREATE TABLE IF NOT EXISTS `balance_history` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `date` datetime NOT NULL,
  `reference_id` varchar(255) NOT NULL,
  `account_id` int(11) NOT NULL,
  `account_name` varchar(50) NOT NULL,
  `price_id` int(11) NOT NULL,
  `amount` int(11) NOT NULL,
  `description` varchar(255) NOT NULL,
  `type` enum('IN','OUT') NOT NULL,
  PRIMARY KEY (`id`),
  INDEX `account_id` (`account_id`),
  INDEX `reference_id` (`reference_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- Add some sample shop items to the existing shop_items table
INSERT INTO `shop_items` (`item_id`, `name`, `description`, `price`, `image_url`, `category_id`, `rarity`, `created_at`) VALUES
(*********, 'Test Reward Item', 'A special test item for daily rewards', 100, '/images/items/*********.png', 1, 'Common', NOW()),
(*********, 'Kinah Bundle', '1000 Kinah for your adventures', 50, '/images/items/kinah.png', 1, 'Common', NOW()),
(*********, 'Premium Potion', 'Restores health and mana', 75, '/images/items/potion.png', 1, 'Superior', NOW()),
(*********, 'Rare Enhancement Stone', 'Enhances your equipment', 150, '/images/items/stone.png', 1, 'Heroic', NOW()),
(*********, 'Legendary Weapon', 'A powerful legendary weapon', 500, '/images/items/weapon.png', 1, 'Legendary', NOW())
ON DUPLICATE KEY UPDATE 
  `name` = VALUES(`name`),
  `description` = VALUES(`description`),
  `price` = VALUES(`price`);
