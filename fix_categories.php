<?php
require_once "db.php";

echo "<h2>🔧 Fixing Shop Categories</h2>";

// First, let's see what the table structure looks like
echo "<h3>Current shop_categories table structure:</h3>";
$structure = $conn->query("DESCRIBE shop_categories");
if ($structure) {
    echo "<table border='1' style='border-collapse: collapse;'>";
    echo "<tr><th>Field</th><th>Type</th><th>Null</th><th>Key</th><th>Default</th></tr>";
    while ($row = $structure->fetch_assoc()) {
        echo "<tr>";
        echo "<td>" . $row['Field'] . "</td>";
        echo "<td>" . $row['Type'] . "</td>";
        echo "<td>" . $row['Null'] . "</td>";
        echo "<td>" . $row['Key'] . "</td>";
        echo "<td>" . $row['Default'] . "</td>";
        echo "</tr>";
    }
    echo "</table>";
} else {
    echo "❌ Could not get table structure: " . $conn->error;
}

// Try simple INSERT without prepared statements
echo "<h3>Adding categories with simple INSERT:</h3>";

$categories = [
    "INSERT INTO shop_categories (name, slug, icon, display_order) VALUES ('Items', 'items', '⚔️', 1)",
    "INSERT INTO shop_categories (name, slug, icon, display_order) VALUES ('Skins', 'skins', '👗', 2)",
    "INSERT INTO shop_categories (name, slug, icon, display_order) VALUES ('Consumables', 'consumables', '🧪', 3)"
];

foreach ($categories as $sql) {
    if ($conn->query($sql)) {
        echo "✅ Category added successfully<br>";
    } else {
        echo "❌ Error: " . $conn->error . "<br>";
        echo "SQL: " . $sql . "<br><br>";
    }
}

// Check what we have now
echo "<h3>Current categories:</h3>";
$result = $conn->query("SELECT * FROM shop_categories");
if ($result && $result->num_rows > 0) {
    echo "<table border='1' style='border-collapse: collapse;'>";
    echo "<tr><th>ID</th><th>Name</th><th>Slug</th><th>Icon</th><th>Display Order</th><th>Active</th></tr>";
    while ($row = $result->fetch_assoc()) {
        echo "<tr>";
        echo "<td>" . $row['id'] . "</td>";
        echo "<td>" . $row['name'] . "</td>";
        echo "<td>" . $row['slug'] . "</td>";
        echo "<td>" . $row['icon'] . "</td>";
        echo "<td>" . $row['display_order'] . "</td>";
        echo "<td>" . ($row['is_active'] ? 'Yes' : 'No') . "</td>";
        echo "</tr>";
    }
    echo "</table>";
} else {
    echo "No categories found or error: " . $conn->error;
}

$conn->close();
?>
