<?php
echo "Fixing shop setup...\n";

require_once "db.php";

// Test database connection
if ($conn->connect_error) {
    die("Connection failed: " . $conn->connect_error);
}

echo "✓ Database connection successful!\n";

// Create shop_categories table with proper defaults
echo "Creating shop_categories table...\n";

$createCategoriesQuery = "
CREATE TABLE IF NOT EXISTS `shop_categories` (
    `id` int(11) NOT NULL AUTO_INCREMENT,
    `name` varchar(100) NOT NULL,
    `icon` varchar(50) DEFAULT NULL,
    `display_order` int(11) DEFAULT 0,
    `is_active` tinyint(1) DEFAULT 1,
    PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_general_ci;
";

if ($conn->query($createCategoriesQuery) === TRUE) {
    echo "✓ shop_categories table created successfully!\n";
    
    // Check if categories exist
    $checkCategories = $conn->query("SELECT COUNT(*) as count FROM shop_categories");
    $categoryCount = $checkCategories->fetch_assoc()['count'];
    
    if ($categoryCount == 0) {
        // Add default categories
        $insertCategories = "
        INSERT INTO shop_categories (name, icon, display_order, is_active) VALUES
        ('Items', '📦', 1, 1),
        ('Skins', '👕', 2, 1),
        ('Consumables', '🧪', 3, 1)
        ";
        
        if ($conn->query($insertCategories) === TRUE) {
            echo "✓ Default categories added!\n";
        } else {
            echo "✗ Error adding categories: " . $conn->error . "\n";
        }
    } else {
        echo "✓ Categories already exist ($categoryCount categories)\n";
    }
} else {
    echo "✗ Error creating categories table: " . $conn->error . "\n";
}

// Now add the test item
echo "\nAdding test shop item...\n";

// Add a test item (your favorite item ID 164000073)
$testItem = [
    'item_id' => 164000073,
    'name' => 'Test Reward Item',
    'description' => 'A special test item to verify the shop is working! This item will be delivered to your character\'s in-game mail.',
    'price' => 10, // 10 Donation Points
    'quantity' => 5,
    'image_url' => '/images/items/164000073.png',
    'category_id' => 1,
    'rarity' => 'Common'
];

// Check if item already exists
$checkQuery = "SELECT id FROM shop_items WHERE item_id = ? LIMIT 1";
$checkStmt = $conn->prepare($checkQuery);
$checkStmt->bind_param("i", $testItem['item_id']);
$checkStmt->execute();
$existingResult = $checkStmt->get_result();

if ($existingResult->num_rows > 0) {
    echo "✓ Test item already exists in shop!\n";
} else {
    // Insert the test item
    $insertQuery = "INSERT INTO shop_items (item_id, name, description, price, quantity, image_url, category_id, rarity) VALUES (?, ?, ?, ?, ?, ?, ?, ?)";
    $stmt = $conn->prepare($insertQuery);
    $stmt->bind_param("issiiiss", 
        $testItem['item_id'], 
        $testItem['name'], 
        $testItem['description'], 
        $testItem['price'], 
        $testItem['quantity'], 
        $testItem['image_url'], 
        $testItem['category_id'], 
        $testItem['rarity']
    );
    
    if ($stmt->execute()) {
        echo "✓ Test item added successfully!\n";
        echo "  - Item ID: {$testItem['item_id']}\n";
        echo "  - Name: {$testItem['name']}\n";
        echo "  - Price: {$testItem['price']} DP\n";
        echo "  - Quantity: {$testItem['quantity']}\n";
    } else {
        echo "✗ Error adding test item: " . $stmt->error . "\n";
    }
}

// Add a few more test items for variety
$moreItems = [
    [
        'item_id' => 186000236,
        'name' => 'Kinah Bundle (1000)',
        'description' => '1000 Kinah for your adventures',
        'price' => 5,
        'quantity' => 1,
        'image_url' => '/images/items/kinah.png',
        'category_id' => 1,
        'rarity' => 'Common'
    ],
    [
        'item_id' => 186000242,
        'name' => 'Premium Health Potion',
        'description' => 'Restores health instantly',
        'price' => 15,
        'quantity' => 10,
        'image_url' => '/images/items/potion.png',
        'category_id' => 3,
        'rarity' => 'Superior'
    ]
];

foreach ($moreItems as $item) {
    // Check if item already exists
    $checkStmt = $conn->prepare($checkQuery);
    $checkStmt->bind_param("i", $item['item_id']);
    $checkStmt->execute();
    $existingResult = $checkStmt->get_result();
    
    if ($existingResult->num_rows == 0) {
        $stmt = $conn->prepare($insertQuery);
        $stmt->bind_param("issiiiss", 
            $item['item_id'], 
            $item['name'], 
            $item['description'], 
            $item['price'], 
            $item['quantity'], 
            $item['image_url'], 
            $item['category_id'], 
            $item['rarity']
        );
        
        if ($stmt->execute()) {
            echo "✓ Added: {$item['name']} - {$item['price']} DP\n";
        } else {
            echo "✗ Error adding {$item['name']}: " . $stmt->error . "\n";
        }
    }
}

// Final count
$finalCountResult = $conn->query("SELECT COUNT(*) as count FROM shop_items");
if ($finalCountResult) {
    $finalCount = $finalCountResult->fetch_assoc()['count'];
    echo "\n✅ Shop setup complete! Total items in shop: $finalCount\n";
}

echo "\n🎉 SUCCESS! Your shop is now ready!\n";
echo "Go to your website dashboard and click 'Visit Shop' to see the items!\n";

$conn->close();
?>
