/* Reset default styles */
html, body, ul {
    margin: 0;
    padding: 0;
}

/* Set background image */
body {
    background-image: url("images/background.jpg");
    background-size: cover;
    background-repeat: no-repeat;
    background-attachment: fixed;
}

/* Navigation bar styles */
nav {
    background-color: #333;
    padding: 10px;
    text-align: center;
}

ul {
    list-style-type: none;
}

li {
    display: inline-block;
    margin-right: 10px;
}

a {
    color: #fff;
    text-decoration: none;
    display: inline-block;
    position: relative;
    padding: 12px 20px;
    font-size: 18px;
    transition: 0.3s;
}

a::before {
    content: "";
    position: absolute;
    bottom: 0;
    left: 50%;
    width: 0;
    height: 2px;
    background-color: #fff;
    transition: 0.3s;
    transform: translateX(-50%);
}

a:hover::before {
    width: 100%;
}

/* Sign In section styles */
.signin {
    display: inline-block;
    float: right;
    color: #fff;
    margin-top: 5px;
}

/* Main section styles */
section {
    margin: 20px;
    text-align: center;
}

h1 {
    font-size: 24px;
    margin-bottom: 10px;
}

/* Form styles */
form {
    margin-bottom: 20px;
}

label {
    display: block;
    margin-bottom: 5px;
}

input[type="text"],
input[type="password"] {
    width: 250px;
    padding: 10px;
    margin-bottom: 10px;
    font-size: 16px;
}

input[type="submit"] {
    padding: 12px 20px;
    background-color: #333;
    color: #fff;
    border: none;
    cursor: pointer;
    font-size: 18px;
    transition: 0.3s;
}

input[type="submit"]:hover {
    background-color: #555;
}

/* Sign-in container styles */
.signin-container {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background-color: rgba(255, 255, 255, 0.8);
    padding: 30px;
    border-radius: 10px;
}

/* Registration page styles */
.register-container {
    width: 300px;
    margin: 0 auto;
    padding: 20px;
    background-color: rgba(255, 255, 255, 0.8);
    text-align: center;
    margin-top: 100px;
    border-radius: 10px;
}

.register-container h1 {
    font-size: 24px;
    margin-bottom: 10px;
}

.register-container label {
    display: block;
    margin-bottom: 5px;
}

.register-container input[type="text"],
.register-container input[type="password"] {
    width: 200px;
    padding: 5px;
    margin-bottom: 10px;
}

.register-container input[type="submit"] {
    padding: 8px 15px;
    background-color: #333;
    color: #fff;
    border: none;
    cursor: pointer;
}
