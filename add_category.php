<?php
require_once "db.php";

echo "<h2>➕ Add New Category</h2>";

if ($_POST) {
    $name = trim($_POST['name']);
    $slug = strtolower(trim($_POST['slug']));
    $icon = trim($_POST['icon']);
    $display_order = intval($_POST['display_order']);
    
    if (!empty($name) && !empty($slug)) {
        $sql = "INSERT INTO shop_categories (name, slug, icon, display_order, is_active) VALUES (?, ?, ?, ?, 1)";
        $stmt = $conn->prepare($sql);
        $stmt->bind_param("sssi", $name, $slug, $icon, $display_order);
        
        if ($stmt->execute()) {
            echo "<div style='color: green; padding: 10px; background: rgba(0,255,0,0.1);'>✅ Category '$name' added successfully!</div>";
        } else {
            echo "<div style='color: red; padding: 10px; background: rgba(255,0,0,0.1);'>❌ Error: " . $stmt->error . "</div>";
        }
    } else {
        echo "<div style='color: red; padding: 10px; background: rgba(255,0,0,0.1);'>❌ Please fill in name and slug fields.</div>";
    }
}

// Show current categories
echo "<h3>Current Categories:</h3>";
$result = $conn->query("SELECT * FROM shop_categories ORDER BY display_order");
if ($result && $result->num_rows > 0) {
    echo "<table border='1' style='border-collapse: collapse; margin-bottom: 20px;'>";
    echo "<tr><th>ID</th><th>Name</th><th>Slug</th><th>Icon</th><th>Display Order</th><th>Active</th></tr>";
    while ($row = $result->fetch_assoc()) {
        echo "<tr>";
        echo "<td>" . $row['id'] . "</td>";
        echo "<td>" . $row['name'] . "</td>";
        echo "<td>" . $row['slug'] . "</td>";
        echo "<td>" . $row['icon'] . "</td>";
        echo "<td>" . $row['display_order'] . "</td>";
        echo "<td>" . ($row['is_active'] ? 'Yes' : 'No') . "</td>";
        echo "</tr>";
    }
    echo "</table>";
}
?>

<!DOCTYPE html>
<html>
<head>
    <title>Add Category</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background: #1a1a1a; color: #fff; }
        .form-group { margin: 15px 0; }
        label { display: inline-block; width: 120px; font-weight: bold; }
        input, select { padding: 8px; margin: 5px; width: 200px; }
        button { padding: 10px 20px; background: #d4af37; color: #000; border: none; cursor: pointer; }
        button:hover { background: #f4d03f; }
        .examples { background: rgba(0,0,0,0.5); padding: 15px; margin: 10px 0; border-radius: 5px; }
    </style>
</head>
<body>
    <h2>Add New Category</h2>
    
    <form method="POST">
        <div class="form-group">
            <label>Name:</label>
            <input type="text" name="name" required placeholder="e.g. Weapons">
        </div>
        <div class="form-group">
            <label>Slug:</label>
            <input type="text" name="slug" required placeholder="e.g. weapons">
        </div>
        <div class="form-group">
            <label>Icon:</label>
            <input type="text" name="icon" placeholder="e.g. weapon">
        </div>
        <div class="form-group">
            <label>Display Order:</label>
            <input type="number" name="display_order" value="10" min="0">
        </div>
        <button type="submit">Add Category</button>
    </form>
    
    <div class="examples">
        <h4>💡 Icon Examples:</h4>
        <p><strong>Text Icons:</strong> weapon, armor, accessory, mount, pet, material, tool, food</p>
        <p><strong>Display Order:</strong> Lower numbers appear first (1, 2, 3...)</p>
        <p><strong>Slug:</strong> URL-friendly version (lowercase, no spaces)</p>
    </div>
    
    <p><a href="manage_shop.php" style="color: #d4af37;">← Back to Shop Management</a></p>
</body>
</html>

<?php $conn->close(); ?>
