/*
 Navicat Premium Dump SQL

 Source Server         : Aion4.8
 Source Server Type    : MySQL
 Source Server Version : 50733 (5.7.33)
 Source Host           : localhost:3306
 Source Schema         : aion4.8

 Target Server Type    : MySQL
 Target Server Version : 50733 (5.7.33)
 File Encoding         : 65001

 Date: 19/06/2025 01:10:46
*/

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- Table structure for abyss_rank
-- ----------------------------
DROP TABLE IF EXISTS `abyss_rank`;
CREATE TABLE `abyss_rank`  (
  `player_id` int(11) NOT NULL,
  `daily_ap` int(11) NOT NULL,
  `weekly_ap` int(11) NOT NULL,
  `ap` int(11) NOT NULL,
  `rank` tinyint(4) NOT NULL DEFAULT 1,
  `max_rank` tinyint(4) NOT NULL DEFAULT 1,
  `rank_pos` smallint(6) NOT NULL DEFAULT 0,
  `old_rank_pos` smallint(6) NOT NULL DEFAULT 0,
  `daily_kill` int(11) NOT NULL,
  `weekly_kill` int(11) NOT NULL,
  `all_kill` int(11) NOT NULL DEFAULT 0,
  `last_kill` int(11) NOT NULL,
  `last_ap` int(11) NOT NULL,
  `last_update` decimal(20, 0) NOT NULL,
  `rank_ap` int(11) NOT NULL DEFAULT 0,
  `daily_gp` int(11) NOT NULL DEFAULT 0,
  `weekly_gp` int(11) NOT NULL DEFAULT 0,
  `gp` int(11) NOT NULL DEFAULT 0,
  `last_gp` int(11) NOT NULL DEFAULT 0,
  PRIMARY KEY (`player_id`) USING BTREE,
  INDEX `rank`(`rank`) USING BTREE,
  INDEX `rank_pos`(`rank_pos`) USING BTREE,
  INDEX `gp`(`gp`) USING BTREE,
  CONSTRAINT `abyss_rank_ibfk_1` FOREIGN KEY (`player_id`) REFERENCES `players` (`id`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of abyss_rank
-- ----------------------------
INSERT INTO `abyss_rank` VALUES (105507, 0, 0, 0, 1, 1, 0, 0, 0, 0, 0, 0, 0, *************, 0, 0, 0, 0, 0);
INSERT INTO `abyss_rank` VALUES (106621, 0, 0, 0, 1, 1, 0, 0, 0, 0, 0, 0, 0, *************, 0, 0, 0, 0, 0);
INSERT INTO `abyss_rank` VALUES (106932, 0, 0, 0, 1, 1, 0, 0, 0, 0, 0, 0, 0, *************, 0, 0, 0, 0, 0);

-- ----------------------------
-- Table structure for account_data
-- ----------------------------
DROP TABLE IF EXISTS `account_data`;
CREATE TABLE `account_data`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(45) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `ext_auth_name` varchar(45) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `password` varchar(65) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
  `creation_date` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `activated` tinyint(1) NOT NULL DEFAULT 1,
  `access_level` tinyint(4) NOT NULL DEFAULT 0,
  `membership` tinyint(4) NOT NULL DEFAULT 0,
  `old_membership` tinyint(4) NOT NULL DEFAULT 0,
  `last_server` tinyint(4) NOT NULL DEFAULT -1,
  `last_ip` varchar(20) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `last_mac` varchar(20) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT 'xx-xx-xx-xx-xx-xx',
  `last_hdd_serial` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `allowed_hdd_serial` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `ip_force` varchar(20) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `expire` date NULL DEFAULT NULL,
  `toll` bigint(20) NOT NULL DEFAULT 0,
  `reset_token` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `reset_token_expires` datetime NULL DEFAULT NULL,
  `reward_points` int(11) NULL DEFAULT NULL,
  `email` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `pin` varchar(6) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `membershipExpiry` timestamp NULL DEFAULT NULL,
  `answer` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `vote` int(11) NOT NULL DEFAULT 0,
  `remember_token` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `rewarded_status` int(11) NOT NULL DEFAULT 0,
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `name`(`name`) USING BTREE,
  UNIQUE INDEX `ext_auth_name`(`ext_auth_name`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 4 CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of account_data
-- ----------------------------
INSERT INTO `account_data` VALUES (1, 'admin', NULL, 'zV6nPNWPgn+nju9xl7juYGyZsuY=', '2025-06-16 21:14:21', 1, 2, 10, 0, 1, '*************', '50-7B-9D-CD-52-DB', '0x100062007600DC0232005400', NULL, NULL, NULL, 2221, NULL, NULL, NULL, '<EMAIL>', NULL, NULL, NULL, 0, NULL, '2025-06-16 21:14:21', NULL, 0);
INSERT INTO `account_data` VALUES (2, 'Arghoke', NULL, '9MrYP7Pwe81kdDjXPBkx4M3g85Q=', '2025-06-18 14:38:37', 1, 10, 0, 0, 1, '5.49.25.137', '08-62-66-B6-F2-89', 'S3YJNX0K776167E', NULL, NULL, NULL, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, NULL, NULL, NULL, 0);
INSERT INTO `account_data` VALUES (3, 'lahart77', NULL, 'VN4X/r4dHRAdhzcgEbVxqC3tSM0=', '2025-06-19 01:08:31', 1, 0, 0, 0, 1, '*************', 'A8-3B-76-24-C8-24', '1cb0830b66bee3e3', NULL, NULL, NULL, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, NULL, NULL, NULL, 0);

-- ----------------------------
-- Table structure for account_login_history
-- ----------------------------
DROP TABLE IF EXISTS `account_login_history`;
CREATE TABLE `account_login_history`  (
  `account_id` int(11) NOT NULL,
  `gameserver_id` tinyint(3) UNSIGNED NOT NULL,
  `date` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `ip` varchar(20) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `mac` varchar(20) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `hdd_serial` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  PRIMARY KEY (`account_id`, `date`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of account_login_history
-- ----------------------------

-- ----------------------------
-- Table structure for account_passports
-- ----------------------------
DROP TABLE IF EXISTS `account_passports`;
CREATE TABLE `account_passports`  (
  `account_id` int(11) NOT NULL,
  `passport_id` int(11) NOT NULL,
  `rewarded` int(11) NOT NULL,
  `arrive_date` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`account_id`, `passport_id`, `arrive_date`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of account_passports
-- ----------------------------

-- ----------------------------
-- Table structure for account_rewards
-- ----------------------------
DROP TABLE IF EXISTS `account_rewards`;
CREATE TABLE `account_rewards`  (
  `uniqId` int(11) NOT NULL AUTO_INCREMENT,
  `accountId` int(11) NOT NULL,
  `added` varchar(70) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '',
  `points` decimal(20, 0) NOT NULL DEFAULT 0,
  `received` varchar(70) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '0',
  `rewarded` tinyint(1) NOT NULL DEFAULT 0,
  PRIMARY KEY (`uniqId`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of account_rewards
-- ----------------------------

-- ----------------------------
-- Table structure for account_stamps
-- ----------------------------
DROP TABLE IF EXISTS `account_stamps`;
CREATE TABLE `account_stamps`  (
  `account_id` int(11) NOT NULL,
  `stamps` tinyint(4) NOT NULL DEFAULT 0,
  `last_stamp` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`account_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of account_stamps
-- ----------------------------
INSERT INTO `account_stamps` VALUES (1, 0, NULL);
INSERT INTO `account_stamps` VALUES (2, 0, NULL);
INSERT INTO `account_stamps` VALUES (3, 0, NULL);

-- ----------------------------
-- Table structure for account_time
-- ----------------------------
DROP TABLE IF EXISTS `account_time`;
CREATE TABLE `account_time`  (
  `account_id` int(11) NOT NULL,
  `last_active` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `expiration_time` timestamp NULL DEFAULT NULL,
  `session_duration` int(11) NULL DEFAULT 0,
  `accumulated_online` int(11) NULL DEFAULT 0,
  `accumulated_rest` int(11) NULL DEFAULT 0,
  `penalty_end` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`account_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of account_time
-- ----------------------------
INSERT INTO `account_time` VALUES (1, '2025-06-16 22:31:29', NULL, 0, 0, 5, NULL);
INSERT INTO `account_time` VALUES (2, '2025-06-18 17:57:19', NULL, 0, 0, 31497, NULL);
INSERT INTO `account_time` VALUES (3, '2025-06-19 01:09:29', NULL, 0, 0, 0, NULL);

-- ----------------------------
-- Table structure for announcements
-- ----------------------------
DROP TABLE IF EXISTS `announcements`;
CREATE TABLE `announcements`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `announce` text CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
  `faction` enum('ALL','ASMODIANS','ELYOS') CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT 'ALL',
  `type` enum('SHOUT','ORANGE','YELLOW','WHITE','SYSTEM') CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT 'SYSTEM',
  `delay` int(11) NOT NULL DEFAULT 1800,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of announcements
-- ----------------------------

-- ----------------------------
-- Table structure for banned_hdd
-- ----------------------------
DROP TABLE IF EXISTS `banned_hdd`;
CREATE TABLE `banned_hdd`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `serial` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
  `time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of banned_hdd
-- ----------------------------

-- ----------------------------
-- Table structure for banned_ip
-- ----------------------------
DROP TABLE IF EXISTS `banned_ip`;
CREATE TABLE `banned_ip`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `mask` varchar(45) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
  `time_end` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `mask`(`mask`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of banned_ip
-- ----------------------------

-- ----------------------------
-- Table structure for banned_mac
-- ----------------------------
DROP TABLE IF EXISTS `banned_mac`;
CREATE TABLE `banned_mac`  (
  `uniId` int(11) NOT NULL AUTO_INCREMENT,
  `address` varchar(20) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
  `time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `details` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '',
  PRIMARY KEY (`uniId`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of banned_mac
-- ----------------------------

-- ----------------------------
-- Table structure for blocks
-- ----------------------------
DROP TABLE IF EXISTS `blocks`;
CREATE TABLE `blocks`  (
  `player` int(11) NOT NULL,
  `blocked_player` int(11) NOT NULL,
  `reason` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '',
  PRIMARY KEY (`player`, `blocked_player`) USING BTREE,
  INDEX `blocked_player`(`blocked_player`) USING BTREE,
  CONSTRAINT `blocks_ibfk_1` FOREIGN KEY (`player`) REFERENCES `players` (`id`) ON DELETE CASCADE ON UPDATE CASCADE,
  CONSTRAINT `blocks_ibfk_2` FOREIGN KEY (`blocked_player`) REFERENCES `players` (`id`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of blocks
-- ----------------------------

-- ----------------------------
-- Table structure for bonus_packs
-- ----------------------------
DROP TABLE IF EXISTS `bonus_packs`;
CREATE TABLE `bonus_packs`  (
  `account_id` int(11) NOT NULL,
  `receiving_player` int(11) NOT NULL,
  PRIMARY KEY (`account_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of bonus_packs
-- ----------------------------

-- ----------------------------
-- Table structure for bookmark
-- ----------------------------
DROP TABLE IF EXISTS `bookmark`;
CREATE TABLE `bookmark`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `char_id` int(11) NOT NULL,
  `x` float NOT NULL,
  `y` float NOT NULL,
  `z` float NOT NULL,
  `world_id` int(11) NOT NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of bookmark
-- ----------------------------

-- ----------------------------
-- Table structure for broker
-- ----------------------------
DROP TABLE IF EXISTS `broker`;
CREATE TABLE `broker`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `item_pointer` int(11) NOT NULL DEFAULT 0,
  `item_id` int(11) NOT NULL,
  `item_count` bigint(20) NOT NULL,
  `item_creator` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `price` bigint(20) NOT NULL DEFAULT 0,
  `broker_race` enum('ELYOS','ASMODIAN') CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
  `expire_time` timestamp NOT NULL DEFAULT '2010-01-01 02:00:00',
  `settle_time` timestamp NOT NULL DEFAULT '2010-01-01 02:00:00',
  `seller_id` int(11) NOT NULL,
  `is_sold` tinyint(1) NOT NULL,
  `is_settled` tinyint(1) NOT NULL,
  `splitting_available` tinyint(1) NOT NULL DEFAULT 0,
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `seller_id`(`seller_id`) USING BTREE,
  CONSTRAINT `broker_ibfk_1` FOREIGN KEY (`seller_id`) REFERENCES `players` (`id`) ON DELETE CASCADE ON UPDATE RESTRICT
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of broker
-- ----------------------------

-- ----------------------------
-- Table structure for challenge_tasks
-- ----------------------------
DROP TABLE IF EXISTS `challenge_tasks`;
CREATE TABLE `challenge_tasks`  (
  `task_id` int(11) NOT NULL,
  `quest_id` int(11) NOT NULL,
  `owner_id` int(11) NOT NULL,
  `owner_type` enum('LEGION','TOWN') CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
  `complete_count` int(10) UNSIGNED NOT NULL DEFAULT 0,
  `complete_time` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`task_id`, `quest_id`, `owner_id`, `owner_type`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of challenge_tasks
-- ----------------------------

-- ----------------------------
-- Table structure for chatlog
-- ----------------------------
DROP TABLE IF EXISTS `chatlog`;
CREATE TABLE `chatlog`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `sender` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `message` text CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
  `type` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of chatlog
-- ----------------------------

-- ----------------------------
-- Table structure for commands_access
-- ----------------------------
DROP TABLE IF EXISTS `commands_access`;
CREATE TABLE `commands_access`  (
  `player_id` int(11) NOT NULL,
  `command` varchar(40) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
  PRIMARY KEY (`player_id`, `command`) USING BTREE,
  CONSTRAINT `commands_access_ibfk_1` FOREIGN KEY (`player_id`) REFERENCES `players` (`id`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of commands_access
-- ----------------------------

-- ----------------------------
-- Table structure for craft_cooldowns
-- ----------------------------
DROP TABLE IF EXISTS `craft_cooldowns`;
CREATE TABLE `craft_cooldowns`  (
  `player_id` int(11) NOT NULL,
  `delay_id` int(10) UNSIGNED NOT NULL,
  `reuse_time` bigint(20) UNSIGNED NOT NULL,
  PRIMARY KEY (`player_id`, `delay_id`) USING BTREE,
  CONSTRAINT `craft_cooldowns_ibfk_1` FOREIGN KEY (`player_id`) REFERENCES `players` (`id`) ON DELETE CASCADE ON UPDATE RESTRICT
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of craft_cooldowns
-- ----------------------------

-- ----------------------------
-- Table structure for custom_instance
-- ----------------------------
DROP TABLE IF EXISTS `custom_instance`;
CREATE TABLE `custom_instance`  (
  `player_id` int(11) NOT NULL,
  `rank` int(11) NOT NULL,
  `last_entry` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `max_rank` int(11) NOT NULL,
  `dps` int(11) NOT NULL,
  PRIMARY KEY (`player_id`) USING BTREE,
  INDEX `rank`(`rank`) USING BTREE,
  INDEX `last_entry`(`last_entry`) USING BTREE,
  CONSTRAINT `custom_instance_ibfk_1` FOREIGN KEY (`player_id`) REFERENCES `players` (`id`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of custom_instance
-- ----------------------------

-- ----------------------------
-- Table structure for custom_instance_records
-- ----------------------------
DROP TABLE IF EXISTS `custom_instance_records`;
CREATE TABLE `custom_instance_records`  (
  `player_id` int(11) NOT NULL,
  `timestamp` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `skill_id` int(11) NOT NULL,
  `player_class_id` int(11) NOT NULL,
  `player_hp_percentage` float NOT NULL,
  `player_mp_percentage` float NOT NULL,
  `player_is_rooted` tinyint(1) NOT NULL,
  `player_is_silenced` tinyint(1) NOT NULL,
  `player_is_bound` tinyint(1) NOT NULL,
  `player_is_stunned` tinyint(1) NOT NULL,
  `player_is_aetherhold` tinyint(1) NOT NULL,
  `player_buff_count` int(11) NOT NULL,
  `player_debuff_count` int(11) NOT NULL,
  `player_is_shielded` tinyint(1) NOT NULL,
  `target_hp_percentage` float NULL DEFAULT NULL,
  `target_mp_percentage` float NULL DEFAULT NULL,
  `target_focuses_player` tinyint(1) NULL DEFAULT NULL,
  `distance` float NULL DEFAULT NULL,
  `target_is_rooted` tinyint(1) NULL DEFAULT NULL,
  `target_is_silenced` tinyint(1) NULL DEFAULT NULL,
  `target_is_bound` tinyint(1) NULL DEFAULT NULL,
  `target_is_stunned` tinyint(1) NULL DEFAULT NULL,
  `target_is_aetherhold` tinyint(1) NULL DEFAULT NULL,
  `target_buff_count` int(11) NULL DEFAULT NULL,
  `target_debuff_count` int(11) NULL DEFAULT NULL,
  `target_is_shielded` tinyint(1) NULL DEFAULT NULL,
  INDEX `custom_instance_records_ibfk_1`(`player_id`) USING BTREE,
  CONSTRAINT `custom_instance_records_ibfk_1` FOREIGN KEY (`player_id`) REFERENCES `players` (`id`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of custom_instance_records
-- ----------------------------

-- ----------------------------
-- Table structure for donations
-- ----------------------------
DROP TABLE IF EXISTS `donations`;
CREATE TABLE `donations`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) NOT NULL,
  `amount` decimal(10, 2) NOT NULL,
  `paypal_order_id` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
  `status` varchar(20) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `user_id`(`user_id`) USING BTREE,
  CONSTRAINT `donations_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `account_data` (`id`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of donations
-- ----------------------------

-- ----------------------------
-- Table structure for event
-- ----------------------------
DROP TABLE IF EXISTS `event`;
CREATE TABLE `event`  (
  `event_name` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
  `buff_index` int(11) NOT NULL,
  `buff_active_pool_ids` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `buff_allowed_days` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `last_change` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`event_name`, `buff_index`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of event
-- ----------------------------
INSERT INTO `event` VALUES ('Beyond Aion Server Buffs', 3, '10824', '28', '2025-06-19 00:00:00');

-- ----------------------------
-- Table structure for faction_packs
-- ----------------------------
DROP TABLE IF EXISTS `faction_packs`;
CREATE TABLE `faction_packs`  (
  `account_id` int(11) NOT NULL,
  `receiving_player` int(11) NOT NULL,
  PRIMARY KEY (`account_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of faction_packs
-- ----------------------------

-- ----------------------------
-- Table structure for flash_news
-- ----------------------------
DROP TABLE IF EXISTS `flash_news`;
CREATE TABLE `flash_news`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `message` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
  `priority` int(11) NULL DEFAULT 0,
  `start_date` timestamp NULL DEFAULT NULL,
  `end_date` timestamp NULL DEFAULT NULL,
  `is_active` tinyint(1) NULL DEFAULT 1,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of flash_news
-- ----------------------------

-- ----------------------------
-- Table structure for friends
-- ----------------------------
DROP TABLE IF EXISTS `friends`;
CREATE TABLE `friends`  (
  `player` int(11) NOT NULL,
  `friend` int(11) NOT NULL,
  `memo` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '',
  PRIMARY KEY (`player`, `friend`) USING BTREE,
  INDEX `friend`(`friend`) USING BTREE,
  CONSTRAINT `friends_ibfk_1` FOREIGN KEY (`player`) REFERENCES `players` (`id`) ON DELETE CASCADE ON UPDATE CASCADE,
  CONSTRAINT `friends_ibfk_2` FOREIGN KEY (`friend`) REFERENCES `players` (`id`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of friends
-- ----------------------------

-- ----------------------------
-- Table structure for gameservers
-- ----------------------------
DROP TABLE IF EXISTS `gameservers`;
CREATE TABLE `gameservers`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `mask` varchar(45) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
  `password` varchar(65) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 2 CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of gameservers
-- ----------------------------
INSERT INTO `gameservers` VALUES (1, '*', 'Azer$tyuio^p789');

-- ----------------------------
-- Table structure for guides
-- ----------------------------
DROP TABLE IF EXISTS `guides`;
CREATE TABLE `guides`  (
  `guide_id` int(11) NOT NULL AUTO_INCREMENT,
  `player_id` int(11) NOT NULL,
  `title` varchar(80) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
  PRIMARY KEY (`guide_id`) USING BTREE,
  INDEX `player_id`(`player_id`) USING BTREE,
  CONSTRAINT `guides_ibfk_1` FOREIGN KEY (`player_id`) REFERENCES `players` (`id`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of guides
-- ----------------------------

-- ----------------------------
-- Table structure for headhunting
-- ----------------------------
DROP TABLE IF EXISTS `headhunting`;
CREATE TABLE `headhunting`  (
  `hunter_id` int(11) NOT NULL,
  `accumulated_kills` int(11) NOT NULL,
  `last_update` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`hunter_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of headhunting
-- ----------------------------

-- ----------------------------
-- Table structure for house_bids
-- ----------------------------
DROP TABLE IF EXISTS `house_bids`;
CREATE TABLE `house_bids`  (
  `player_id` int(11) NOT NULL,
  `house_id` int(11) NOT NULL,
  `bid` bigint(20) NOT NULL,
  `bid_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`player_id`, `house_id`, `bid`) USING BTREE,
  INDEX `house_id_ibfk_1`(`house_id`) USING BTREE,
  CONSTRAINT `house_id_ibfk_1` FOREIGN KEY (`house_id`) REFERENCES `houses` (`id`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of house_bids
-- ----------------------------

-- ----------------------------
-- Table structure for house_object_cooldowns
-- ----------------------------
DROP TABLE IF EXISTS `house_object_cooldowns`;
CREATE TABLE `house_object_cooldowns`  (
  `player_id` int(11) NOT NULL,
  `object_id` int(11) NOT NULL,
  `reuse_time` bigint(20) NOT NULL,
  PRIMARY KEY (`player_id`, `object_id`) USING BTREE,
  INDEX `house_object_cooldowns_ibfk_2`(`object_id`) USING BTREE,
  CONSTRAINT `house_object_cooldowns_ibfk_1` FOREIGN KEY (`player_id`) REFERENCES `players` (`id`) ON DELETE CASCADE ON UPDATE CASCADE,
  CONSTRAINT `house_object_cooldowns_ibfk_2` FOREIGN KEY (`object_id`) REFERENCES `player_registered_items` (`item_unique_id`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of house_object_cooldowns
-- ----------------------------

-- ----------------------------
-- Table structure for house_scripts
-- ----------------------------
DROP TABLE IF EXISTS `house_scripts`;
CREATE TABLE `house_scripts`  (
  `house_id` int(11) NOT NULL,
  `script_id` tinyint(4) NOT NULL,
  `script` mediumtext CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
  `date_added` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`house_id`, `script_id`) USING BTREE,
  CONSTRAINT `houses_id_ibfk_1` FOREIGN KEY (`house_id`) REFERENCES `houses` (`id`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_general_ci KEY_BLOCK_SIZE = 16 ROW_FORMAT = COMPRESSED;

-- ----------------------------
-- Records of house_scripts
-- ----------------------------

-- ----------------------------
-- Table structure for houses
-- ----------------------------
DROP TABLE IF EXISTS `houses`;
CREATE TABLE `houses`  (
  `id` int(11) NOT NULL,
  `player_id` int(11) NOT NULL DEFAULT 0,
  `building_id` int(11) NOT NULL,
  `address` int(11) NOT NULL,
  `acquire_time` timestamp NULL DEFAULT NULL,
  `settings` int(11) NOT NULL DEFAULT 0,
  `next_pay` timestamp NULL DEFAULT NULL,
  `sign_notice` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `address`(`address`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of houses
-- ----------------------------

-- ----------------------------
-- Table structure for ingameshop
-- ----------------------------
DROP TABLE IF EXISTS `ingameshop`;
CREATE TABLE `ingameshop`  (
  `object_id` int(11) NOT NULL AUTO_INCREMENT,
  `item_id` int(11) NOT NULL,
  `item_count` bigint(20) NOT NULL DEFAULT 0,
  `item_price` bigint(20) NOT NULL DEFAULT 0,
  `category` tinyint(4) NOT NULL DEFAULT 0,
  `sub_category` tinyint(4) NOT NULL DEFAULT 0,
  `list` int(11) NOT NULL DEFAULT 0,
  `sales_ranking` int(11) NOT NULL DEFAULT 0,
  `item_type` tinyint(4) NOT NULL DEFAULT 0,
  `gift` tinyint(1) NOT NULL DEFAULT 0,
  `title_description` varchar(20) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
  `description` varchar(20) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
  PRIMARY KEY (`object_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of ingameshop
-- ----------------------------

-- ----------------------------
-- Table structure for ingameshop_log
-- ----------------------------
DROP TABLE IF EXISTS `ingameshop_log`;
CREATE TABLE `ingameshop_log`  (
  `transaction_id` int(11) NOT NULL AUTO_INCREMENT,
  `transaction_type` enum('BUY','GIFT') CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
  `transaction_date` timestamp NULL DEFAULT NULL,
  `payer_name` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
  `payer_account_name` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
  `receiver_name` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
  `item_id` int(11) NOT NULL,
  `item_count` bigint(20) NOT NULL DEFAULT 0,
  `item_price` bigint(20) NOT NULL DEFAULT 0,
  PRIMARY KEY (`transaction_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of ingameshop_log
-- ----------------------------

-- ----------------------------
-- Table structure for inventory
-- ----------------------------
DROP TABLE IF EXISTS `inventory`;
CREATE TABLE `inventory`  (
  `item_unique_id` int(11) NOT NULL,
  `item_id` int(11) NOT NULL,
  `item_count` bigint(20) NOT NULL DEFAULT 0,
  `item_color` mediumint(8) UNSIGNED NULL DEFAULT NULL,
  `color_expires` int(11) NOT NULL DEFAULT 0,
  `item_creator` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `item_creation_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `expire_time` int(11) NOT NULL DEFAULT 0,
  `activation_count` int(11) NOT NULL DEFAULT 0,
  `item_owner` int(11) NOT NULL,
  `is_equipped` tinyint(1) NOT NULL DEFAULT 0,
  `is_soul_bound` tinyint(1) NOT NULL DEFAULT 0,
  `slot` bigint(20) NOT NULL DEFAULT 0,
  `item_location` tinyint(4) NULL DEFAULT 0,
  `enchant` tinyint(3) UNSIGNED NOT NULL DEFAULT 0,
  `enchant_bonus` tinyint(4) NOT NULL DEFAULT 0,
  `item_skin` int(11) NOT NULL DEFAULT 0,
  `fusioned_item` int(11) NOT NULL DEFAULT 0,
  `optional_socket` tinyint(3) UNSIGNED NOT NULL DEFAULT 0,
  `optional_fusion_socket` tinyint(3) UNSIGNED NOT NULL DEFAULT 0,
  `charge` mediumint(9) NOT NULL DEFAULT 0,
  `tune_count` smallint(6) NOT NULL DEFAULT 0,
  `rnd_bonus` smallint(6) NOT NULL DEFAULT 0,
  `fusion_rnd_bonus` smallint(6) NOT NULL DEFAULT 0,
  `tempering` tinyint(3) UNSIGNED NOT NULL DEFAULT 0,
  `pack_count` smallint(6) NOT NULL DEFAULT 0,
  `is_amplified` tinyint(1) NOT NULL DEFAULT 0,
  `buff_skill` int(11) NOT NULL DEFAULT 0,
  `rnd_plume_bonus` smallint(5) UNSIGNED NOT NULL DEFAULT 0,
  PRIMARY KEY (`item_unique_id`) USING BTREE,
  INDEX `item_location`(`item_location`) USING BTREE,
  INDEX `index3`(`item_owner`, `item_location`, `is_equipped`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of inventory
-- ----------------------------
INSERT INTO `inventory` VALUES (105508, 182400001, 1282, NULL, 0, '', '2025-06-18 14:39:19', 0, 0, 105507, 0, 0, 65535, 0, 0, 0, 182400001, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0);
INSERT INTO `inventory` VALUES (105509, 101800181, 1, NULL, 0, '', '2025-06-18 14:39:19', 0, 0, 105507, 1, 0, 1, 0, 0, 0, 101800181, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0);
INSERT INTO `inventory` VALUES (105510, 110301667, 1, NULL, 0, '', '2025-06-18 14:39:19', 0, 0, 105507, 1, 0, 8, 0, 0, 0, 110301667, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0);
INSERT INTO `inventory` VALUES (105511, 113301636, 1, NULL, 0, '', '2025-06-18 14:39:19', 0, 0, 105507, 1, 0, 4096, 0, 0, 0, 113301636, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0);
INSERT INTO `inventory` VALUES (105512, 160000001, 12, NULL, 0, '', '2025-06-18 14:39:19', 0, 1, 105507, 0, 0, 0, 0, 0, 0, 160000001, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0);
INSERT INTO `inventory` VALUES (105513, 169300002, 30, NULL, 0, '', '2025-06-18 14:39:19', 0, 0, 105507, 0, 0, 1, 0, 0, 0, 169300002, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0);
INSERT INTO `inventory` VALUES (105514, 162000002, 100, NULL, 0, '', '2025-06-18 14:39:19', 0, 1, 105507, 0, 0, 2, 0, 0, 0, 162000002, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0);
INSERT INTO `inventory` VALUES (105515, 162000007, 100, NULL, 0, '', '2025-06-18 14:39:19', 0, 1, 105507, 0, 0, 3, 0, 0, 0, 162000007, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0);
INSERT INTO `inventory` VALUES (105516, 164002116, 50, NULL, 0, '', '2025-06-18 14:39:19', 0, 1, 105507, 0, 0, 4, 0, 0, 0, 164002116, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0);
INSERT INTO `inventory` VALUES (105517, 164002117, 50, NULL, 0, '', '2025-06-18 14:39:19', 0, 1, 105507, 0, 0, 5, 0, 0, 0, 164002117, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0);
INSERT INTO `inventory` VALUES (105518, 164002118, 50, NULL, 0, '', '2025-06-18 14:39:19', 0, 1, 105507, 0, 0, 6, 0, 0, 0, 164002118, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0);
INSERT INTO `inventory` VALUES (105519, 169620005, 2, NULL, 0, '', '2025-06-18 14:39:19', 0, 1, 105507, 0, 0, 7, 0, 0, 0, 169620005, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0);
INSERT INTO `inventory` VALUES (105521, 164002039, 1, NULL, 0, '', '2025-06-18 14:39:19', 1750509618, 1000, 105507, 0, 0, 8, 0, 0, 0, 164002039, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0);
INSERT INTO `inventory` VALUES (106635, 182400001, 1000, NULL, 0, '', '2025-06-19 01:09:00', 0, 0, 106621, 0, 0, 65535, 0, 0, 0, 182400001, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0);
INSERT INTO `inventory` VALUES (106665, 100200112, 1, NULL, 0, '', '2025-06-19 01:09:00', 0, 0, 106621, 1, 0, 1, 0, 0, 0, 100200112, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0);
INSERT INTO `inventory` VALUES (106674, 110300015, 1, NULL, 0, '', '2025-06-19 01:09:00', 0, 0, 106621, 1, 0, 8, 0, 0, 0, 110300015, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0);
INSERT INTO `inventory` VALUES (106675, 113300005, 1, NULL, 0, '', '2025-06-19 01:09:00', 0, 0, 106621, 1, 0, 4096, 0, 0, 0, 113300005, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0);
INSERT INTO `inventory` VALUES (106676, 160000001, 12, NULL, 0, '', '2025-06-19 01:09:00', 0, 1, 106621, 0, 0, 0, 0, 0, 0, 160000001, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0);
INSERT INTO `inventory` VALUES (106677, 169300002, 20, NULL, 0, '', '2025-06-19 01:09:00', 0, 0, 106621, 0, 0, 1, 0, 0, 0, 169300002, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0);
INSERT INTO `inventory` VALUES (106678, 162000002, 100, NULL, 0, '', '2025-06-19 01:09:00', 0, 1, 106621, 0, 0, 2, 0, 0, 0, 162000002, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0);
INSERT INTO `inventory` VALUES (106679, 162000007, 100, NULL, 0, '', '2025-06-19 01:09:00', 0, 1, 106621, 0, 0, 3, 0, 0, 0, 162000007, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0);
INSERT INTO `inventory` VALUES (106680, 164002116, 50, NULL, 0, '', '2025-06-19 01:09:00', 0, 1, 106621, 0, 0, 4, 0, 0, 0, 164002116, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0);
INSERT INTO `inventory` VALUES (106681, 164002117, 50, NULL, 0, '', '2025-06-19 01:09:00', 0, 1, 106621, 0, 0, 5, 0, 0, 0, 164002117, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0);
INSERT INTO `inventory` VALUES (106682, 164002118, 50, NULL, 0, '', '2025-06-19 01:09:00', 0, 1, 106621, 0, 0, 6, 0, 0, 0, 164002118, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0);
INSERT INTO `inventory` VALUES (106683, 169620005, 2, NULL, 0, '', '2025-06-19 01:09:00', 0, 1, 106621, 0, 0, 7, 0, 0, 0, 169620005, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0);
INSERT INTO `inventory` VALUES (106684, 164002039, 1, NULL, 0, '', '2025-06-19 01:09:00', 1750547399, 1000, 106621, 0, 0, 8, 0, 0, 0, 164002039, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0);
INSERT INTO `inventory` VALUES (106933, 182400001, 1000, NULL, 0, '', '2025-06-16 22:52:18', 0, 0, 106932, 0, 0, 65535, 0, 0, 0, 182400001, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0);
INSERT INTO `inventory` VALUES (106934, 100000094, 1, NULL, 0, '', '2025-06-16 22:52:18', 0, 0, 106932, 1, 0, 1, 0, 0, 0, 100000094, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0);
INSERT INTO `inventory` VALUES (106935, 110500003, 1, NULL, 0, '', '2025-06-16 22:52:18', 0, 0, 106932, 1, 0, 8, 0, 0, 0, 110500003, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0);
INSERT INTO `inventory` VALUES (106936, 113500001, 1, NULL, 0, '', '2025-06-16 22:52:18', 0, 0, 106932, 1, 0, 4096, 0, 0, 0, 113500001, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0);
INSERT INTO `inventory` VALUES (106937, 160000001, 12, NULL, 0, '', '2025-06-16 22:52:18', 0, 1, 106932, 0, 0, 0, 0, 0, 0, 160000001, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0);
INSERT INTO `inventory` VALUES (106938, 169300002, 20, NULL, 0, '', '2025-06-16 22:52:18', 0, 0, 106932, 0, 0, 1, 0, 0, 0, 169300002, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0);
INSERT INTO `inventory` VALUES (106939, 162000002, 100, NULL, 0, '', '2025-06-16 22:52:18', 0, 1, 106932, 0, 0, 2, 0, 0, 0, 162000002, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0);
INSERT INTO `inventory` VALUES (106940, 162000007, 100, NULL, 0, '', '2025-06-16 22:52:18', 0, 1, 106932, 0, 0, 3, 0, 0, 0, 162000007, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0);
INSERT INTO `inventory` VALUES (106941, 164002116, 50, NULL, 0, '', '2025-06-16 22:52:18', 0, 1, 106932, 0, 0, 4, 0, 0, 0, 164002116, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0);
INSERT INTO `inventory` VALUES (106942, 164002117, 50, NULL, 0, '', '2025-06-16 22:52:18', 0, 1, 106932, 0, 0, 5, 0, 0, 0, 164002117, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0);
INSERT INTO `inventory` VALUES (106943, 164002118, 50, NULL, 0, '', '2025-06-16 22:52:18', 0, 1, 106932, 0, 0, 6, 0, 0, 0, 164002118, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0);
INSERT INTO `inventory` VALUES (106944, 169620005, 2, NULL, 0, '', '2025-06-16 22:52:18', 0, 1, 106932, 0, 0, 7, 0, 0, 0, 169620005, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0);
INSERT INTO `inventory` VALUES (106945, 164002039, 1, NULL, 0, '', '2025-06-16 22:52:18', 1750347195, 1000, 106932, 0, 0, 8, 0, 0, 0, 164002039, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0);
INSERT INTO `inventory` VALUES (106947, *********, 1, NULL, 0, NULL, '2025-06-16 23:42:00', 0, 0, 106932, 0, 0, 0, 100, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0);
INSERT INTO `inventory` VALUES (106948, *********, 1, NULL, 0, NULL, '2025-06-16 23:42:06', 0, 0, 106932, 0, 0, 0, 100, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0);
INSERT INTO `inventory` VALUES (106949, *********, 1, NULL, 0, NULL, '2025-06-16 23:43:30', 0, 0, 106932, 0, 0, 0, 100, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0);
INSERT INTO `inventory` VALUES (106950, *********, 1, NULL, 0, NULL, '2025-06-16 23:46:58', 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0);
INSERT INTO `inventory` VALUES (106951, *********, 1, NULL, 0, NULL, '2025-06-17 00:10:00', 0, 0, 106932, 0, 0, 0, 100, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0);
INSERT INTO `inventory` VALUES (106952, *********, 1, NULL, 0, NULL, '2025-06-17 00:10:07', 0, 0, 106932, 0, 0, 0, 100, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0);
INSERT INTO `inventory` VALUES (106955, *********, 1, NULL, 0, NULL, '2025-06-17 00:32:07', 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0);
INSERT INTO `inventory` VALUES (106956, *********, 1, NULL, 0, NULL, '2025-06-17 00:48:15', 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0);
INSERT INTO `inventory` VALUES (106957, *********, 1, NULL, 0, NULL, '2025-06-17 00:49:37', 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0);
INSERT INTO `inventory` VALUES (106958, *********, 1, NULL, 0, NULL, '2025-06-17 00:50:44', 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0);
INSERT INTO `inventory` VALUES (106959, *********, 1, NULL, 0, NULL, '2025-06-17 00:52:31', 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0);
INSERT INTO `inventory` VALUES (106961, *********, 1, NULL, 0, NULL, '2025-06-17 01:17:51', 0, 0, 106932, 0, 0, 0, 100, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0);
INSERT INTO `inventory` VALUES (106962, *********, 1, NULL, 0, NULL, '2025-06-17 01:18:30', 0, 0, 106932, 0, 0, 0, 100, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0);
INSERT INTO `inventory` VALUES (106963, *********, 1, NULL, 0, NULL, '2025-06-17 01:25:25', 0, 0, 106932, 0, 0, 0, 100, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0);
INSERT INTO `inventory` VALUES (106965, *********, 1, NULL, 0, NULL, '2025-06-17 01:37:20', 0, 0, 106932, 0, 0, 0, 127, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0);
INSERT INTO `inventory` VALUES (106966, *********, 1, NULL, 0, '', '2025-06-17 01:38:58', 0, 0, 106932, 0, 0, 9, 0, 0, 0, *********, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0);
INSERT INTO `inventory` VALUES (106967, *********, 1, NULL, 0, NULL, '2025-06-17 01:46:34', 0, 0, 106932, 0, 0, 0, 127, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0);
INSERT INTO `inventory` VALUES (107081, 182004813, 3, NULL, 0, '', '2025-06-18 15:24:21', 0, 0, 105507, 0, 0, 9, 0, 0, 0, 182004813, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0);

-- ----------------------------
-- Table structure for item_cooldowns
-- ----------------------------
DROP TABLE IF EXISTS `item_cooldowns`;
CREATE TABLE `item_cooldowns`  (
  `player_id` int(11) NOT NULL,
  `delay_id` int(11) NOT NULL,
  `use_delay` int(10) UNSIGNED NOT NULL,
  `reuse_time` bigint(20) NOT NULL,
  PRIMARY KEY (`player_id`, `delay_id`) USING BTREE,
  CONSTRAINT `item_cooldowns_ibfk_1` FOREIGN KEY (`player_id`) REFERENCES `players` (`id`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of item_cooldowns
-- ----------------------------

-- ----------------------------
-- Table structure for item_stones
-- ----------------------------
DROP TABLE IF EXISTS `item_stones`;
CREATE TABLE `item_stones`  (
  `item_unique_id` int(11) NOT NULL,
  `item_id` int(11) NOT NULL,
  `slot` int(11) NOT NULL,
  `category` int(11) NOT NULL DEFAULT 0,
  `polishNumber` int(11) NOT NULL,
  `polishCharge` int(11) NOT NULL,
  `proc_count` int(11) NOT NULL DEFAULT 0,
  PRIMARY KEY (`item_unique_id`, `slot`, `category`) USING BTREE,
  CONSTRAINT `item_stones_ibfk_1` FOREIGN KEY (`item_unique_id`) REFERENCES `inventory` (`item_unique_id`) ON DELETE CASCADE ON UPDATE RESTRICT
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of item_stones
-- ----------------------------

-- ----------------------------
-- Table structure for legion_announcement_list
-- ----------------------------
DROP TABLE IF EXISTS `legion_announcement_list`;
CREATE TABLE `legion_announcement_list`  (
  `legion_id` int(11) NOT NULL,
  `announcement` varchar(256) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
  `date` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  INDEX `legion_id`(`legion_id`) USING BTREE,
  CONSTRAINT `legion_announcement_list_ibfk_1` FOREIGN KEY (`legion_id`) REFERENCES `legions` (`id`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of legion_announcement_list
-- ----------------------------

-- ----------------------------
-- Table structure for legion_dominion_locations
-- ----------------------------
DROP TABLE IF EXISTS `legion_dominion_locations`;
CREATE TABLE `legion_dominion_locations`  (
  `id` int(11) NOT NULL DEFAULT 0,
  `legion_id` int(11) NOT NULL DEFAULT 0,
  `occupied_date` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of legion_dominion_locations
-- ----------------------------
INSERT INTO `legion_dominion_locations` VALUES (1, 0, '2025-06-16 21:44:18');
INSERT INTO `legion_dominion_locations` VALUES (2, 0, '2025-06-16 21:44:18');
INSERT INTO `legion_dominion_locations` VALUES (3, 0, '2025-06-16 21:44:18');
INSERT INTO `legion_dominion_locations` VALUES (4, 0, '2025-06-16 21:44:18');
INSERT INTO `legion_dominion_locations` VALUES (5, 0, '2025-06-16 21:44:18');
INSERT INTO `legion_dominion_locations` VALUES (6, 0, '2025-06-16 21:44:18');

-- ----------------------------
-- Table structure for legion_dominion_participants
-- ----------------------------
DROP TABLE IF EXISTS `legion_dominion_participants`;
CREATE TABLE `legion_dominion_participants`  (
  `legion_dominion_id` int(11) NOT NULL DEFAULT 0,
  `legion_id` int(11) NOT NULL DEFAULT 0,
  `points` int(11) NOT NULL DEFAULT 0,
  `survived_time` int(11) NOT NULL DEFAULT 0,
  `participated_date` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`legion_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of legion_dominion_participants
-- ----------------------------

-- ----------------------------
-- Table structure for legion_emblems
-- ----------------------------
DROP TABLE IF EXISTS `legion_emblems`;
CREATE TABLE `legion_emblems`  (
  `legion_id` int(11) NOT NULL,
  `emblem_id` tinyint(4) NOT NULL DEFAULT 0,
  `color_a` tinyint(4) NOT NULL DEFAULT 0,
  `color_r` tinyint(4) NOT NULL DEFAULT 0,
  `color_g` tinyint(4) NOT NULL DEFAULT 0,
  `color_b` tinyint(4) NOT NULL DEFAULT 0,
  `emblem_type` enum('DEFAULT','CUSTOM') CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT 'DEFAULT',
  `emblem_data` longblob NULL,
  PRIMARY KEY (`legion_id`) USING BTREE,
  CONSTRAINT `legion_emblems_ibfk_1` FOREIGN KEY (`legion_id`) REFERENCES `legions` (`id`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of legion_emblems
-- ----------------------------

-- ----------------------------
-- Table structure for legion_history
-- ----------------------------
DROP TABLE IF EXISTS `legion_history`;
CREATE TABLE `legion_history`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `legion_id` int(11) NOT NULL,
  `date` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `history_type` enum('CREATE','JOIN','KICK','APPOINTED','EMBLEM_REGISTER','EMBLEM_MODIFIED','ITEM_DEPOSIT','ITEM_WITHDRAW','KINAH_DEPOSIT','KINAH_WITHDRAW','LEVEL_UP','DEFENSE','OCCUPATION','LEGION_RENAME','CHARACTER_RENAME') CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
  `name` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
  `description` varchar(30) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `legion_id`(`legion_id`) USING BTREE,
  CONSTRAINT `legion_history_ibfk_1` FOREIGN KEY (`legion_id`) REFERENCES `legions` (`id`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of legion_history
-- ----------------------------

-- ----------------------------
-- Table structure for legion_members
-- ----------------------------
DROP TABLE IF EXISTS `legion_members`;
CREATE TABLE `legion_members`  (
  `legion_id` int(11) NOT NULL,
  `player_id` int(11) NOT NULL,
  `nickname` varchar(10) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '',
  `rank` enum('BRIGADE_GENERAL','CENTURION','LEGIONARY','DEPUTY','VOLUNTEER') CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT 'VOLUNTEER',
  `selfintro` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT '',
  `challenge_score` int(11) NOT NULL DEFAULT 0,
  PRIMARY KEY (`player_id`) USING BTREE,
  INDEX `player_id`(`player_id`) USING BTREE,
  INDEX `legion_id`(`legion_id`) USING BTREE,
  CONSTRAINT `legion_members_ibfk_1` FOREIGN KEY (`player_id`) REFERENCES `players` (`id`) ON DELETE CASCADE ON UPDATE CASCADE,
  CONSTRAINT `legion_members_ibfk_2` FOREIGN KEY (`legion_id`) REFERENCES `legions` (`id`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of legion_members
-- ----------------------------

-- ----------------------------
-- Table structure for legions
-- ----------------------------
DROP TABLE IF EXISTS `legions`;
CREATE TABLE `legions`  (
  `id` int(11) NOT NULL,
  `name` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
  `level` int(11) NOT NULL DEFAULT 1,
  `contribution_points` bigint(20) NOT NULL DEFAULT 0,
  `deputy_permission` int(11) NOT NULL DEFAULT 7692,
  `centurion_permission` int(11) NOT NULL DEFAULT 7176,
  `legionary_permission` int(11) NOT NULL DEFAULT 6144,
  `volunteer_permission` int(11) NOT NULL DEFAULT 2048,
  `disband_time` int(11) NOT NULL DEFAULT 0,
  `rank_pos` smallint(6) NOT NULL DEFAULT 0,
  `old_rank_pos` smallint(6) NOT NULL DEFAULT 0,
  `occupied_legion_dominion` int(11) NOT NULL DEFAULT 0,
  `last_legion_dominion` int(11) NOT NULL DEFAULT 0,
  `current_legion_dominion` int(11) NOT NULL DEFAULT 0,
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `name_unique`(`name`) USING BTREE,
  INDEX `rank_pos`(`rank_pos`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of legions
-- ----------------------------

-- ----------------------------
-- Table structure for mail
-- ----------------------------
DROP TABLE IF EXISTS `mail`;
CREATE TABLE `mail`  (
  `mail_unique_id` int(11) NOT NULL,
  `mail_recipient_id` int(11) NOT NULL,
  `sender_name` varchar(20) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
  `mail_title` varchar(20) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
  `mail_message` varchar(1000) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
  `unread` tinyint(1) NOT NULL DEFAULT 1,
  `attached_item_id` int(11) NOT NULL,
  `attached_kinah_count` bigint(20) NOT NULL,
  `express` tinyint(4) NOT NULL DEFAULT 0,
  `recieved_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`mail_unique_id`) USING BTREE,
  INDEX `mail_recipient_id`(`mail_recipient_id`) USING BTREE,
  CONSTRAINT `FK_mail` FOREIGN KEY (`mail_recipient_id`) REFERENCES `players` (`id`) ON DELETE CASCADE ON UPDATE RESTRICT
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of mail
-- ----------------------------
INSERT INTO `mail` VALUES (1, 106932, 'Web Shop', 'Web Shop Purchase', 'Thank you for your purchase!\n\nHere is your item:\nPrestige Wings', 1, 106967, 0, 1, '2025-06-17 01:46:35');

-- ----------------------------
-- Table structure for old_names
-- ----------------------------
DROP TABLE IF EXISTS `old_names`;
CREATE TABLE `old_names`  (
  `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT,
  `player_id` int(11) NOT NULL,
  `old_name` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
  `new_name` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
  `renamed_date` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `player_id`(`player_id`) USING BTREE,
  INDEX `renamed_date`(`renamed_date`) USING BTREE,
  CONSTRAINT `old_names_ibfk_1` FOREIGN KEY (`player_id`) REFERENCES `players` (`id`) ON DELETE CASCADE ON UPDATE RESTRICT
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of old_names
-- ----------------------------

-- ----------------------------
-- Table structure for player_appearance
-- ----------------------------
DROP TABLE IF EXISTS `player_appearance`;
CREATE TABLE `player_appearance`  (
  `player_id` int(11) NOT NULL,
  `face` int(11) NOT NULL,
  `hair` int(11) NOT NULL,
  `deco` int(11) NOT NULL,
  `tattoo` int(11) NOT NULL,
  `face_contour` int(11) NOT NULL,
  `expression` int(11) NOT NULL,
  `jaw_line` int(11) NOT NULL,
  `skin_rgb` int(11) NOT NULL,
  `hair_rgb` int(11) NOT NULL,
  `lip_rgb` int(11) NOT NULL,
  `eye_rgb` int(11) NOT NULL,
  `face_shape` int(11) NOT NULL,
  `forehead` int(11) NOT NULL,
  `eye_height` int(11) NOT NULL,
  `eye_space` int(11) NOT NULL,
  `eye_width` int(11) NOT NULL,
  `eye_size` int(11) NOT NULL,
  `eye_shape` int(11) NOT NULL,
  `eye_angle` int(11) NOT NULL,
  `brow_height` int(11) NOT NULL,
  `brow_angle` int(11) NOT NULL,
  `brow_shape` int(11) NOT NULL,
  `nose` int(11) NOT NULL,
  `nose_bridge` int(11) NOT NULL,
  `nose_width` int(11) NOT NULL,
  `nose_tip` int(11) NOT NULL,
  `cheek` int(11) NOT NULL,
  `lip_height` int(11) NOT NULL,
  `mouth_size` int(11) NOT NULL,
  `lip_size` int(11) NOT NULL,
  `smile` int(11) NOT NULL,
  `lip_shape` int(11) NOT NULL,
  `jaw_height` int(11) NOT NULL,
  `chin_jut` int(11) NOT NULL,
  `ear_shape` int(11) NOT NULL,
  `head_size` int(11) NOT NULL,
  `neck` int(11) NOT NULL,
  `neck_length` int(11) NOT NULL,
  `shoulders` int(11) NOT NULL,
  `shoulder_size` int(11) NOT NULL,
  `torso` int(11) NOT NULL,
  `chest` int(11) NOT NULL,
  `waist` int(11) NOT NULL,
  `hips` int(11) NOT NULL,
  `arm_thickness` int(11) NOT NULL,
  `arm_length` int(11) NOT NULL,
  `hand_size` int(11) NOT NULL,
  `leg_thickness` int(11) NOT NULL,
  `leg_length` int(11) NOT NULL,
  `foot_size` int(11) NOT NULL,
  `facial_rate` int(11) NOT NULL,
  `voice` int(11) NOT NULL,
  `height` float NOT NULL,
  PRIMARY KEY (`player_id`) USING BTREE,
  CONSTRAINT `player_id_fk` FOREIGN KEY (`player_id`) REFERENCES `players` (`id`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of player_appearance
-- ----------------------------
INSERT INTO `player_appearance` VALUES (105507, 32, 35, 0, 0, 1, 0, 1, 12562360, 12158438, 9990314, 5395138, 100, 23, 71, 200, 199, 28, 6, 228, 16, 3, 12, 136, 5, 13, 143, 150, 167, 0, 0, 130, 0, 0, 2, 162, 45, 228, 226, 241, 161, 246, 161, 21, 12, 230, 231, 206, 244, 210, 219, 8, 0, 0.841949);
INSERT INTO `player_appearance` VALUES (106621, 15, 4, 0, 0, 0, 0, 145, 14145515, 14339505, 9211314, 14138284, 0, 151, 16, 50, 43, 5, 16, 165, 100, 25, 133, 168, 138, 135, 0, 10, 0, 7, 46, 36, 0, 146, 43, 36, 254, 25, 248, 10, 37, 25, 0, 10, 10, 25, 0, 37, 15, 4, 12, 243, 0, 1.09081);
INSERT INTO `player_appearance` VALUES (106932, 28, 60, 0, 0, 5, 0, 0, 13753086, 2036501, 9345244, 4539717, 31, 0, 140, 10, 2, 26, 18, 135, 79, 43, 139, 16, 136, 131, 134, 0, 0, 13, 0, 140, 0, 13, 1, 0, 244, 253, 245, 242, 161, 238, 222, 242, 241, 236, 245, 236, 247, 0, 247, 0, 0, 0.65);

-- ----------------------------
-- Table structure for player_bind_point
-- ----------------------------
DROP TABLE IF EXISTS `player_bind_point`;
CREATE TABLE `player_bind_point`  (
  `player_id` int(11) NOT NULL,
  `map_id` int(11) NOT NULL,
  `x` float NOT NULL,
  `y` float NOT NULL,
  `z` float NOT NULL,
  `heading` int(11) NOT NULL,
  PRIMARY KEY (`player_id`) USING BTREE,
  CONSTRAINT `player_bind_point_ibfk_1` FOREIGN KEY (`player_id`) REFERENCES `players` (`id`) ON DELETE CASCADE ON UPDATE RESTRICT
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of player_bind_point
-- ----------------------------

-- ----------------------------
-- Table structure for player_cooldowns
-- ----------------------------
DROP TABLE IF EXISTS `player_cooldowns`;
CREATE TABLE `player_cooldowns`  (
  `player_id` int(11) NOT NULL,
  `cooldown_id` int(11) NOT NULL,
  `reuse_delay` bigint(20) NOT NULL,
  PRIMARY KEY (`player_id`, `cooldown_id`) USING BTREE,
  CONSTRAINT `player_cooldowns_ibfk_1` FOREIGN KEY (`player_id`) REFERENCES `players` (`id`) ON DELETE CASCADE ON UPDATE RESTRICT
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of player_cooldowns
-- ----------------------------

-- ----------------------------
-- Table structure for player_effects
-- ----------------------------
DROP TABLE IF EXISTS `player_effects`;
CREATE TABLE `player_effects`  (
  `player_id` int(11) NOT NULL,
  `skill_id` int(11) NOT NULL,
  `skill_lvl` tinyint(4) NOT NULL,
  `remaining_time` int(11) NOT NULL,
  `end_time` bigint(20) NOT NULL,
  `force_type` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  PRIMARY KEY (`player_id`, `skill_id`) USING BTREE,
  CONSTRAINT `player_effects_ibfk_1` FOREIGN KEY (`player_id`) REFERENCES `players` (`id`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of player_effects
-- ----------------------------

-- ----------------------------
-- Table structure for player_emotions
-- ----------------------------
DROP TABLE IF EXISTS `player_emotions`;
CREATE TABLE `player_emotions`  (
  `player_id` int(11) NOT NULL,
  `emotion` int(11) NOT NULL,
  `remaining` int(11) NOT NULL DEFAULT 0,
  PRIMARY KEY (`player_id`, `emotion`) USING BTREE,
  CONSTRAINT `player_emotions_ibfk_1` FOREIGN KEY (`player_id`) REFERENCES `players` (`id`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of player_emotions
-- ----------------------------

-- ----------------------------
-- Table structure for player_life_stats
-- ----------------------------
DROP TABLE IF EXISTS `player_life_stats`;
CREATE TABLE `player_life_stats`  (
  `player_id` int(11) NOT NULL,
  `hp` int(11) NOT NULL DEFAULT 1,
  `mp` int(11) NOT NULL DEFAULT 1,
  `fp` int(11) NOT NULL DEFAULT 1,
  PRIMARY KEY (`player_id`) USING BTREE,
  CONSTRAINT `FK_player_life_stats` FOREIGN KEY (`player_id`) REFERENCES `players` (`id`) ON DELETE CASCADE ON UPDATE RESTRICT
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of player_life_stats
-- ----------------------------
INSERT INTO `player_life_stats` VALUES (105507, 261, 240, 60);
INSERT INTO `player_life_stats` VALUES (106621, 219, 170, 60);
INSERT INTO `player_life_stats` VALUES (106932, 284, 170, 60);

-- ----------------------------
-- Table structure for player_macrosses
-- ----------------------------
DROP TABLE IF EXISTS `player_macrosses`;
CREATE TABLE `player_macrosses`  (
  `player_id` int(11) NOT NULL,
  `order` int(11) NOT NULL,
  `macro` text CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
  UNIQUE INDEX `main`(`player_id`, `order`) USING BTREE,
  CONSTRAINT `player_macrosses_ibfk_1` FOREIGN KEY (`player_id`) REFERENCES `players` (`id`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of player_macrosses
-- ----------------------------

-- ----------------------------
-- Table structure for player_motions
-- ----------------------------
DROP TABLE IF EXISTS `player_motions`;
CREATE TABLE `player_motions`  (
  `player_id` int(11) NOT NULL,
  `motion_id` int(11) NOT NULL,
  `time` int(11) NOT NULL DEFAULT 0,
  `active` tinyint(1) NOT NULL DEFAULT 0,
  PRIMARY KEY (`player_id`, `motion_id`) USING BTREE,
  CONSTRAINT `motions_player_id_fk` FOREIGN KEY (`player_id`) REFERENCES `players` (`id`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of player_motions
-- ----------------------------

-- ----------------------------
-- Table structure for player_npc_factions
-- ----------------------------
DROP TABLE IF EXISTS `player_npc_factions`;
CREATE TABLE `player_npc_factions`  (
  `player_id` int(11) NOT NULL,
  `faction_id` int(11) NOT NULL,
  `active` tinyint(1) NOT NULL,
  `time` int(11) NOT NULL,
  `state` enum('NOTING','START','COMPLETE') CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT 'NOTING',
  `quest_id` int(11) NOT NULL DEFAULT 0,
  PRIMARY KEY (`player_id`, `faction_id`) USING BTREE,
  CONSTRAINT `player_npc_factions_ibfk_1` FOREIGN KEY (`player_id`) REFERENCES `players` (`id`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of player_npc_factions
-- ----------------------------

-- ----------------------------
-- Table structure for player_passkey
-- ----------------------------
DROP TABLE IF EXISTS `player_passkey`;
CREATE TABLE `player_passkey`  (
  `account_id` int(11) NOT NULL,
  `passkey` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '',
  PRIMARY KEY (`account_id`, `passkey`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of player_passkey
-- ----------------------------

-- ----------------------------
-- Table structure for player_pets
-- ----------------------------
DROP TABLE IF EXISTS `player_pets`;
CREATE TABLE `player_pets`  (
  `id` int(11) NOT NULL,
  `player_id` int(11) NOT NULL,
  `template_id` int(11) NOT NULL,
  `decoration` int(11) NOT NULL,
  `name` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
  `hungry_level` tinyint(4) NOT NULL DEFAULT 0,
  `feed_progress` int(11) NOT NULL DEFAULT 0,
  `reuse_time` bigint(20) NOT NULL DEFAULT 0,
  `birthday` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `mood_started` bigint(20) NOT NULL DEFAULT 0,
  `counter` int(11) NOT NULL DEFAULT 0,
  `mood_cd_started` bigint(20) NOT NULL DEFAULT 0,
  `gift_cd_started` bigint(20) NOT NULL DEFAULT 0,
  `dopings` varchar(80) CHARACTER SET ascii COLLATE ascii_general_ci NULL DEFAULT NULL,
  `despawn_time` timestamp NULL DEFAULT NULL,
  `expire_time` int(11) NOT NULL DEFAULT 0,
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `player_id`(`player_id`) USING BTREE,
  CONSTRAINT `FK_player_pets` FOREIGN KEY (`player_id`) REFERENCES `players` (`id`) ON DELETE CASCADE ON UPDATE RESTRICT
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of player_pets
-- ----------------------------

-- ----------------------------
-- Table structure for player_punishments
-- ----------------------------
DROP TABLE IF EXISTS `player_punishments`;
CREATE TABLE `player_punishments`  (
  `player_id` int(11) NOT NULL,
  `punishment_type` enum('PRISON','GATHER','CHARBAN') CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
  `start_time` int(10) UNSIGNED NULL DEFAULT 0,
  `duration` int(10) UNSIGNED NULL DEFAULT 0,
  `reason` text CHARACTER SET utf8 COLLATE utf8_general_ci NULL,
  PRIMARY KEY (`player_id`, `punishment_type`) USING BTREE,
  CONSTRAINT `player_punishments_ibfk_1` FOREIGN KEY (`player_id`) REFERENCES `players` (`id`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of player_punishments
-- ----------------------------

-- ----------------------------
-- Table structure for player_quests
-- ----------------------------
DROP TABLE IF EXISTS `player_quests`;
CREATE TABLE `player_quests`  (
  `player_id` int(11) NOT NULL,
  `quest_id` int(10) UNSIGNED NOT NULL DEFAULT 0,
  `status` enum('LOCKED','START','REWARD','COMPLETE') CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
  `quest_vars` int(10) UNSIGNED NOT NULL DEFAULT 0,
  `flags` int(10) UNSIGNED NOT NULL DEFAULT 0,
  `complete_count` int(10) UNSIGNED NOT NULL DEFAULT 0,
  `next_repeat_time` timestamp NULL DEFAULT NULL,
  `reward` smallint(6) NULL DEFAULT NULL,
  `complete_time` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`player_id`, `quest_id`) USING BTREE,
  CONSTRAINT `player_quests_ibfk_1` FOREIGN KEY (`player_id`) REFERENCES `players` (`id`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of player_quests
-- ----------------------------
INSERT INTO `player_quests` VALUES (105507, 2000, 'COMPLETE', 0, 0, 1, NULL, 0, '2025-06-18 14:40:08');
INSERT INTO `player_quests` VALUES (105507, 2100, 'LOCKED', 0, 0, 0, NULL, NULL, NULL);
INSERT INTO `player_quests` VALUES (105507, 2101, 'COMPLETE', 0, 0, 1, NULL, 0, '2025-06-18 14:54:08');
INSERT INTO `player_quests` VALUES (105507, 2102, 'COMPLETE', 0, 0, 1, NULL, 0, '2025-06-18 15:24:30');
INSERT INTO `player_quests` VALUES (105507, 2103, 'START', 0, 0, 0, NULL, NULL, NULL);
INSERT INTO `player_quests` VALUES (106621, 1000, 'COMPLETE', 0, 0, 1, NULL, 0, '2025-06-19 01:09:15');
INSERT INTO `player_quests` VALUES (106621, 1100, 'LOCKED', 0, 0, 0, NULL, NULL, NULL);
INSERT INTO `player_quests` VALUES (106932, 1000, 'COMPLETE', 0, 0, 1, NULL, 0, '2025-06-16 22:32:25');
INSERT INTO `player_quests` VALUES (106932, 1100, 'LOCKED', 0, 0, 0, NULL, NULL, NULL);

-- ----------------------------
-- Table structure for player_recipes
-- ----------------------------
DROP TABLE IF EXISTS `player_recipes`;
CREATE TABLE `player_recipes`  (
  `player_id` int(11) NOT NULL,
  `recipe_id` int(11) NOT NULL,
  PRIMARY KEY (`player_id`, `recipe_id`) USING BTREE,
  CONSTRAINT `player_recipes_ibfk_1` FOREIGN KEY (`player_id`) REFERENCES `players` (`id`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of player_recipes
-- ----------------------------

-- ----------------------------
-- Table structure for player_registered_items
-- ----------------------------
DROP TABLE IF EXISTS `player_registered_items`;
CREATE TABLE `player_registered_items`  (
  `player_id` int(11) NOT NULL,
  `item_unique_id` int(11) NOT NULL,
  `item_id` int(11) NOT NULL,
  `expire_time` int(11) NULL DEFAULT NULL,
  `color` int(11) NULL DEFAULT NULL,
  `color_expires` int(11) NOT NULL DEFAULT 0,
  `owner_use_count` int(11) NOT NULL DEFAULT 0,
  `visitor_use_count` int(11) NOT NULL DEFAULT 0,
  `x` float NOT NULL DEFAULT 0,
  `y` float NOT NULL DEFAULT 0,
  `z` float NOT NULL DEFAULT 0,
  `h` smallint(6) NULL DEFAULT NULL,
  `area` enum('NONE','INTERIOR','EXTERIOR','ALL','DECOR') CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT 'NONE',
  `room` tinyint(4) NOT NULL DEFAULT 0,
  PRIMARY KEY (`player_id`, `item_unique_id`, `item_id`) USING BTREE,
  UNIQUE INDEX `item_unique_id`(`item_unique_id`) USING BTREE,
  CONSTRAINT `player_regitems_ibfk_1` FOREIGN KEY (`player_id`) REFERENCES `players` (`id`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of player_registered_items
-- ----------------------------

-- ----------------------------
-- Table structure for player_settings
-- ----------------------------
DROP TABLE IF EXISTS `player_settings`;
CREATE TABLE `player_settings`  (
  `player_id` int(11) NOT NULL,
  `settings_type` tinyint(4) NOT NULL,
  `settings` blob NOT NULL,
  PRIMARY KEY (`player_id`, `settings_type`) USING BTREE,
  CONSTRAINT `ps_pl_fk` FOREIGN KEY (`player_id`) REFERENCES `players` (`id`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of player_settings
-- ----------------------------
INSERT INTO `player_settings` VALUES (105507, -2, 0x30);
INSERT INTO `player_settings` VALUES (105507, -1, 0x30);
INSERT INTO `player_settings` VALUES (105507, 0, 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
INSERT INTO `player_settings` VALUES (105507, 1, 0xD40000003E020000789C9591490E824010459F5B13EF40DC2B324862E2B0F304BA2682468D8A036A38A3A7F20794406817A617D55DF5EAFFEAEE172DC6CCC83871C4E2C9861B297BCE244CE8E2D067A068A992102BBF5635619B57972C98D31315E834634A87B614CF5CB87F54D2321BB3639577A71C148F5A53750EE5F07B8DB17F7416AA571ECAC4CA47626E8435F75073671536D27EA0DBB89AD92A9D83DCA5A87E29A7A4DC3C8E0C8CDB50720D945751F24536895143C7314ED49CDC37724ECDD1AB30C53BD87FBD9A6DFC4F78035A6B50CC);
INSERT INTO `player_settings` VALUES (105507, 2, 0xA70000007E010000789C9D90CF0EC1301CC73FAE12EFB0B8332E4E6C374FC059D061C9D62E8AF08A9ECAB79D4884D3D243FBFBFE6DFBA4C79C9C3B351509370ACE784A1C960543A68C99684FC458F6C28D58CB31B26B562C194935D3949331A0AF4447C3E59DE23FE849F335A607FF86035B353A359652174276E28DD643E74AA8179EC99976F6B6CDC165C45B71E10D4E8A70BF3A3A7FB3BEBBBBBBDBF6F4EF7FC00B76C044AC);
INSERT INTO `player_settings` VALUES (106621, -2, 0x30);
INSERT INTO `player_settings` VALUES (106621, -1, 0x30);
INSERT INTO `player_settings` VALUES (106621, 0, 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
INSERT INTO `player_settings` VALUES (106621, 1, 0xDA00000062020000789C9591DD0EC14010853FB712EFD0B8A7DAD290D0DE7902AE851204F553C4337A2A272D8D66D7856CD29D9DF9E69CD9ED931A43621E1CD8E37067C5858C2D47524634F168D3D1EEA8929228BF5435659D57A74C18D31215EA1413D1A02EC52327AE6F95ACCC266C98E7DD193BED7BAD489D3D39FC5E43DC1F9D85EA999B3289F20B31176615F799E67E7CB10BC51DDDC6D7CC4EE91CE62E45F543792515E8DB576C327E851948CF6402C3CDA6D42D295FB14D6760E878D6A9CDDB75AD9C57710CBE98E2ADDCBF5ED6B5FE737801B514553E);
INSERT INTO `player_settings` VALUES (106621, 2, 0xA70000007E010000789C9D90CF0EC1301CC73FAE12EFB0B8332E4E6C374FC059D061C9D62E8AF08A9ECAB79D4884D3D243FBFBFE6DFBA4C79C9C3B351509370ACE784A1C960543A68C99684FC458F6C28D58CB31B26B562C194935D3949331A0AF4447C3E59DE23FE849F335A607FF86035B353A359652174276E28DD643E74AA8179EC99976F6B6CDC165C45B71E10D4E8A70BF3A3A7FB3BEBBBBBBDBF6F4EF7FC00B76C044AC);
INSERT INTO `player_settings` VALUES (106932, -2, 0x30);
INSERT INTO `player_settings` VALUES (106932, -1, 0x30);
INSERT INTO `player_settings` VALUES (106932, 0, 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
INSERT INTO `player_settings` VALUES (106932, 1, 0xED0000009E020000789C9592DB6AC240144597AF05FF21F4BD8D995CAAE005A4F40BDA67D128ADA8F11215BFB15FD54DA26962A68172203373F69A73F61CF24D8B3E232E6C58E37066C18194255B12063CE2F14C47AB232521567E2E35E133533F78E78D2751914E2386B47950C52D3B8ED72A69918DF9629ADD4E59695D2B86BA19AA4339CCDDB91A7EF61D37E8AF153DE4A591EFE3FEE12C77BDE7A44CACFC4CCC8149E57513CDE5526267DA77342DA39938C5CBA2AC4BAEDE28AFA00C5D11818531B54AC642F9A54A81C83AD1ABD5F1AC8EEACE6DAEBC8AF740FD7F997C0EEEBFA6E65AFF17F80133CD5A30);
INSERT INTO `player_settings` VALUES (106932, 2, 0xA70000007E010000789C9D90CF0EC1301CC73FAE12EFB0B8332E4E6C374FC059D061C9D62E8AF08A9ECAB79D4884D3D243FBFBFE6DFBA4C79C9C3B351509370ACE784A1C960543A68C99684FC458F6C28D58CB31B26B562C194935D3949331A0AF4447C3E59DE23FE849F335A607FF86035B353A359652174276E28DD643E74AA8179EC99976F6B6CDC165C45B71E10D4E8A70BF3A3A7FB3BEBBBBBBDBF6F4EF7FC00B76C044AC);

-- ----------------------------
-- Table structure for player_skills
-- ----------------------------
DROP TABLE IF EXISTS `player_skills`;
CREATE TABLE `player_skills`  (
  `player_id` int(11) NOT NULL,
  `skill_id` int(11) NOT NULL,
  `skill_level` int(11) NOT NULL DEFAULT 1,
  PRIMARY KEY (`player_id`, `skill_id`) USING BTREE,
  CONSTRAINT `player_skills_ibfk_1` FOREIGN KEY (`player_id`) REFERENCES `players` (`id`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of player_skills
-- ----------------------------
INSERT INTO `player_skills` VALUES (105507, 40, 1);
INSERT INTO `player_skills` VALUES (105507, 41, 1);
INSERT INTO `player_skills` VALUES (105507, 103, 1);
INSERT INTO `player_skills` VALUES (105507, 112, 1);
INSERT INTO `player_skills` VALUES (105507, 243, 1);
INSERT INTO `player_skills` VALUES (105507, 245, 1);
INSERT INTO `player_skills` VALUES (105507, 302, 1);
INSERT INTO `player_skills` VALUES (105507, 2219, 1);
INSERT INTO `player_skills` VALUES (105507, 30001, 1);
INSERT INTO `player_skills` VALUES (106621, 37, 1);
INSERT INTO `player_skills` VALUES (106621, 40, 1);
INSERT INTO `player_skills` VALUES (106621, 41, 1);
INSERT INTO `player_skills` VALUES (106621, 66, 1);
INSERT INTO `player_skills` VALUES (106621, 103, 1);
INSERT INTO `player_skills` VALUES (106621, 243, 1);
INSERT INTO `player_skills` VALUES (106621, 245, 1);
INSERT INTO `player_skills` VALUES (106621, 302, 1);
INSERT INTO `player_skills` VALUES (106621, 3182, 1);
INSERT INTO `player_skills` VALUES (106621, 3195, 1);
INSERT INTO `player_skills` VALUES (106621, 30001, 1);
INSERT INTO `player_skills` VALUES (106932, 37, 1);
INSERT INTO `player_skills` VALUES (106932, 39, 1);
INSERT INTO `player_skills` VALUES (106932, 40, 1);
INSERT INTO `player_skills` VALUES (106932, 41, 1);
INSERT INTO `player_skills` VALUES (106932, 42, 1);
INSERT INTO `player_skills` VALUES (106932, 43, 1);
INSERT INTO `player_skills` VALUES (106932, 103, 1);
INSERT INTO `player_skills` VALUES (106932, 140, 1);
INSERT INTO `player_skills` VALUES (106932, 243, 1);
INSERT INTO `player_skills` VALUES (106932, 245, 1);
INSERT INTO `player_skills` VALUES (106932, 302, 1);
INSERT INTO `player_skills` VALUES (106932, 2864, 1);
INSERT INTO `player_skills` VALUES (106932, 30001, 1);

-- ----------------------------
-- Table structure for player_titles
-- ----------------------------
DROP TABLE IF EXISTS `player_titles`;
CREATE TABLE `player_titles`  (
  `player_id` int(11) NOT NULL,
  `title_id` int(11) NOT NULL,
  `remaining` int(11) NOT NULL DEFAULT 0,
  PRIMARY KEY (`player_id`, `title_id`) USING BTREE,
  CONSTRAINT `player_titles_ibfk_1` FOREIGN KEY (`player_id`) REFERENCES `players` (`id`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of player_titles
-- ----------------------------

-- ----------------------------
-- Table structure for player_transfers
-- ----------------------------
DROP TABLE IF EXISTS `player_transfers`;
CREATE TABLE `player_transfers`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `source_server` tinyint(4) NOT NULL,
  `target_server` tinyint(4) NOT NULL,
  `source_account_id` int(11) NOT NULL,
  `target_account_id` int(11) NOT NULL,
  `player_id` int(11) NOT NULL,
  `status` tinyint(4) NOT NULL DEFAULT 0,
  `time_added` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `time_performed` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `time_done` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `comment` text CHARACTER SET utf8 COLLATE utf8_general_ci NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of player_transfers
-- ----------------------------

-- ----------------------------
-- Table structure for player_veteran_rewards
-- ----------------------------
DROP TABLE IF EXISTS `player_veteran_rewards`;
CREATE TABLE `player_veteran_rewards`  (
  `player_id` int(11) NOT NULL,
  `received_months` tinyint(3) UNSIGNED NOT NULL DEFAULT 0,
  PRIMARY KEY (`player_id`) USING BTREE,
  CONSTRAINT `player_veteran_rewards_ibfk_1` FOREIGN KEY (`player_id`) REFERENCES `players` (`id`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of player_veteran_rewards
-- ----------------------------

-- ----------------------------
-- Table structure for player_web_rewards
-- ----------------------------
DROP TABLE IF EXISTS `player_web_rewards`;
CREATE TABLE `player_web_rewards`  (
  `entry_id` int(11) NOT NULL AUTO_INCREMENT,
  `player_id` int(11) NOT NULL,
  `item_id` int(11) NOT NULL,
  `item_count` bigint(20) NOT NULL DEFAULT 1,
  `added` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `received` timestamp NULL DEFAULT NULL,
  `order_id` varchar(10) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  PRIMARY KEY (`entry_id`) USING BTREE,
  UNIQUE INDEX `order_id`(`order_id`) USING BTREE,
  INDEX `item_owner`(`player_id`) USING BTREE,
  CONSTRAINT `player_web_rewards_ibfk_1` FOREIGN KEY (`player_id`) REFERENCES `players` (`id`) ON DELETE CASCADE ON UPDATE RESTRICT
) ENGINE = InnoDB AUTO_INCREMENT = 4 CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of player_web_rewards
-- ----------------------------
INSERT INTO `player_web_rewards` VALUES (3, 106932, *********, 1, '2025-06-17 01:41:42', NULL, NULL);

-- ----------------------------
-- Table structure for players
-- ----------------------------
DROP TABLE IF EXISTS `players`;
CREATE TABLE `players`  (
  `id` int(11) NOT NULL,
  `name` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
  `account_id` int(11) NOT NULL,
  `account_name` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
  `exp` bigint(20) NOT NULL DEFAULT 0,
  `recoverexp` bigint(20) NOT NULL DEFAULT 0,
  `old_level` tinyint(4) NOT NULL DEFAULT 0,
  `x` float NOT NULL,
  `y` float NOT NULL,
  `z` float NOT NULL,
  `heading` int(11) NOT NULL,
  `world_id` int(11) NOT NULL,
  `world_owner` int(11) NOT NULL DEFAULT 0,
  `gender` enum('MALE','FEMALE') CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
  `race` enum('ASMODIANS','ELYOS') CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
  `player_class` enum('WARRIOR','GLADIATOR','TEMPLAR','SCOUT','ASSASSIN','RANGER','MAGE','SORCERER','SPIRIT_MASTER','PRIEST','CLERIC','CHANTER','ENGINEER','GUNNER','ARTIST','BARD','RIDER','ALL') CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
  `creation_date` timestamp NULL DEFAULT NULL,
  `deletion_date` timestamp NULL DEFAULT NULL,
  `last_online` timestamp NULL DEFAULT NULL,
  `quest_expands` tinyint(4) NOT NULL DEFAULT 0,
  `npc_expands` tinyint(4) NOT NULL DEFAULT 0,
  `item_expands` tinyint(4) NOT NULL DEFAULT 0,
  `wh_npc_expands` tinyint(4) NOT NULL DEFAULT 0,
  `wh_bonus_expands` tinyint(4) NOT NULL DEFAULT 0,
  `mailbox_letters` tinyint(3) UNSIGNED NOT NULL DEFAULT 0,
  `title_id` int(11) NOT NULL DEFAULT -1,
  `bonus_title_id` int(11) NOT NULL DEFAULT -1,
  `dp` int(11) NOT NULL DEFAULT 0,
  `soul_sickness` tinyint(3) UNSIGNED NOT NULL DEFAULT 0,
  `reposte_energy` bigint(20) NOT NULL DEFAULT 0,
  `online` tinyint(1) NOT NULL DEFAULT 0,
  `note` text CHARACTER SET utf8 COLLATE utf8_general_ci NULL,
  `mentor_flag_time` int(11) NOT NULL DEFAULT 0,
  `last_transfer_time` decimal(20, 0) NOT NULL DEFAULT 0,
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `name_unique`(`name`) USING BTREE,
  INDEX `account_id`(`account_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of players
-- ----------------------------
INSERT INTO `players` VALUES (105507, 'Admin', 2, 'Arghoke', 1043, 0, 2, 524.314, 2767.81, 294.277, 85, *********, 0, 'FEMALE', 'ASMODIANS', 'ENGINEER', '2025-06-18 14:39:20', NULL, '2025-06-18 17:57:29', 0, 0, 0, 0, 0, 0, -1, -1, 0, 0, 0, 0, NULL, 0, 0);
INSERT INTO `players` VALUES (106621, 'test', 3, 'lahart77', 1, 0, 1, 1212.42, 1046.28, 140.634, 29, *********, 0, 'MALE', 'ELYOS', 'SCOUT', '2025-06-19 01:09:00', NULL, '2025-06-19 01:09:29', 0, 0, 0, 0, 0, 0, -1, -1, 0, 0, 0, 0, NULL, 0, 0);
INSERT INTO `players` VALUES (106932, 'Admintutus', 1, 'admin', 2, 0, 1, 827.091, 1240.48, 118.78, 96, *********, 0, 'FEMALE', 'ELYOS', 'WARRIOR', '2025-06-16 22:32:16', NULL, '2025-06-17 01:49:41', 0, 0, 0, 0, 0, 0, -1, -1, 0, 0, 0, 0, NULL, 0, 0);

-- ----------------------------
-- Table structure for portal_cooldowns
-- ----------------------------
DROP TABLE IF EXISTS `portal_cooldowns`;
CREATE TABLE `portal_cooldowns`  (
  `player_id` int(11) NOT NULL,
  `world_id` int(11) NOT NULL,
  `reuse_time` bigint(20) NOT NULL,
  `entry_count` int(11) NOT NULL,
  PRIMARY KEY (`player_id`, `world_id`) USING BTREE,
  CONSTRAINT `portal_cooldowns_ibfk_1` FOREIGN KEY (`player_id`) REFERENCES `players` (`id`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of portal_cooldowns
-- ----------------------------

-- ----------------------------
-- Table structure for security_questions
-- ----------------------------
DROP TABLE IF EXISTS `security_questions`;
CREATE TABLE `security_questions`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `account_id` int(11) NOT NULL,
  `question_number` int(11) NOT NULL,
  `answer` varchar(255) CHARACTER SET latin1 COLLATE latin1_swedish_ci NOT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `unique_account_question`(`account_id`, `question_number`) USING BTREE
) ENGINE = MyISAM AUTO_INCREMENT = 4 CHARACTER SET = latin1 COLLATE = latin1_swedish_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of security_questions
-- ----------------------------
INSERT INTO `security_questions` VALUES (1, 1, 1, 'Qu9j54Nu9iLZGFwaRWBR7fFglcw=');
INSERT INTO `security_questions` VALUES (2, 1, 2, 'eT+XDFLe0SdrkmTHQvGdGIjLr3M=');
INSERT INTO `security_questions` VALUES (3, 1, 3, 'K1uAe3VfYmVtwRxCXFtvNXbkKrk=');

-- ----------------------------
-- Table structure for server_variables
-- ----------------------------
DROP TABLE IF EXISTS `server_variables`;
CREATE TABLE `server_variables`  (
  `key` varchar(30) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
  `value` varchar(30) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
  PRIMARY KEY (`key`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of server_variables
-- ----------------------------
INSERT INTO `server_variables` VALUES ('serverLastRun', '1750288181398');
INSERT INTO `server_variables` VALUES ('time', '12456');

-- ----------------------------
-- Table structure for shop_categories
-- ----------------------------
DROP TABLE IF EXISTS `shop_categories`;
CREATE TABLE `shop_categories`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
  `slug` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
  `icon` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `display_order` int(11) NULL DEFAULT 0,
  `is_active` tinyint(1) NULL DEFAULT 1,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `slug`(`slug`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 2 CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of shop_categories
-- ----------------------------
INSERT INTO `shop_categories` VALUES (1, 'Wings', '1', 'prestige_wings_g_01.png', 0, 1, '2025-06-16 22:04:55', '2025-06-16 22:04:59');

-- ----------------------------
-- Table structure for shop_items
-- ----------------------------
DROP TABLE IF EXISTS `shop_items`;
CREATE TABLE `shop_items`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `item_id` int(11) NULL DEFAULT NULL,
  `name` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
  `description` text CHARACTER SET utf8 COLLATE utf8_general_ci NULL,
  `price` int(11) NOT NULL,
  `image_url` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
  `category_id` int(11) NULL DEFAULT NULL,
  `rarity` enum('Common','Superior','Heroic','Fabled','Eternal','Mythic','Ancient','Legendary','Ultimate') CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `subcategory_id` int(11) NULL DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `category_id`(`category_id`) USING BTREE,
  INDEX `subcategory_id`(`subcategory_id`) USING BTREE,
  CONSTRAINT `shop_items_ibfk_1` FOREIGN KEY (`category_id`) REFERENCES `shop_categories` (`id`) ON DELETE SET NULL ON UPDATE RESTRICT,
  CONSTRAINT `shop_items_ibfk_2` FOREIGN KEY (`subcategory_id`) REFERENCES `shop_subcategories` (`id`) ON DELETE SET NULL ON UPDATE RESTRICT
) ENGINE = InnoDB AUTO_INCREMENT = 2 CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of shop_items
-- ----------------------------
INSERT INTO `shop_items` VALUES (1, *********, 'Prestige Wings', 'Available for Level 65 or higher. Soul Binding Available.', 1111, '/images/items/*********.png', 1, 'Mythic', '2025-06-16 22:04:42', '2025-06-16 22:04:46', 1);

-- ----------------------------
-- Table structure for shop_subcategories
-- ----------------------------
DROP TABLE IF EXISTS `shop_subcategories`;
CREATE TABLE `shop_subcategories`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
  `slug` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
  `icon` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `category_id` int(11) NOT NULL,
  `display_order` int(11) NULL DEFAULT 0,
  `is_active` tinyint(1) NULL DEFAULT 1,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `slug`(`slug`) USING BTREE,
  INDEX `category_id`(`category_id`) USING BTREE,
  CONSTRAINT `shop_subcategories_ibfk_1` FOREIGN KEY (`category_id`) REFERENCES `shop_categories` (`id`) ON DELETE CASCADE ON UPDATE RESTRICT
) ENGINE = InnoDB AUTO_INCREMENT = 2 CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of shop_subcategories
-- ----------------------------
INSERT INTO `shop_subcategories` VALUES (1, 'Special Wings', 'special-wings', 'icon-special.png', 1, 10, 1, NULL, NULL);

-- ----------------------------
-- Table structure for shop_transactions
-- ----------------------------
DROP TABLE IF EXISTS `shop_transactions`;
CREATE TABLE `shop_transactions`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) NOT NULL,
  `item_id` int(11) NOT NULL,
  `quantity` int(11) NOT NULL DEFAULT 1,
  `price_paid` int(11) NOT NULL,
  `character_id` int(11) NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `status` enum('completed','refunded','failed') CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT 'completed',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `user_id`(`user_id`) USING BTREE,
  INDEX `item_id`(`item_id`) USING BTREE,
  INDEX `character_id`(`character_id`) USING BTREE,
  CONSTRAINT `shop_transactions_ibfk_1` FOREIGN KEY (`character_id`) REFERENCES `players` (`id`) ON DELETE CASCADE ON UPDATE RESTRICT,
  CONSTRAINT `shop_transactions_ibfk_2` FOREIGN KEY (`user_id`) REFERENCES `account_data` (`id`) ON DELETE CASCADE ON UPDATE RESTRICT
) ENGINE = InnoDB AUTO_INCREMENT = 25 CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of shop_transactions
-- ----------------------------
INSERT INTO `shop_transactions` VALUES (1, 1, 1, 1, 1111, 106932, '2025-06-16 23:08:25', 'completed');
INSERT INTO `shop_transactions` VALUES (2, 1, 1, 1, 1111, 106932, '2025-06-16 23:30:13', 'completed');
INSERT INTO `shop_transactions` VALUES (3, 1, 1, 1, 1111, 106932, '2025-06-16 23:43:30', 'completed');
INSERT INTO `shop_transactions` VALUES (4, 1, 1, 1, 1111, 106932, '2025-06-16 23:46:58', 'completed');
INSERT INTO `shop_transactions` VALUES (5, 1, 1, 1, 1111, 106932, '2025-06-17 00:10:00', 'completed');
INSERT INTO `shop_transactions` VALUES (6, 1, 1, 1, 1111, 106932, '2025-06-17 00:10:07', 'completed');
INSERT INTO `shop_transactions` VALUES (7, 1, 1, 1, 1111, 106932, '2025-06-17 00:16:00', 'completed');
INSERT INTO `shop_transactions` VALUES (8, 1, 1, 1, 1111, 106932, '2025-06-17 00:16:40', 'completed');
INSERT INTO `shop_transactions` VALUES (9, 1, 1, 1, 1111, 106932, '2025-06-17 00:27:26', 'completed');
INSERT INTO `shop_transactions` VALUES (10, 1, 1, 1, 1111, 106932, '2025-06-17 00:32:07', 'completed');
INSERT INTO `shop_transactions` VALUES (11, 1, 1, 1, 1111, 106932, '2025-06-17 00:48:15', 'completed');
INSERT INTO `shop_transactions` VALUES (12, 1, 1, 1, 1111, 106932, '2025-06-17 00:49:37', 'completed');
INSERT INTO `shop_transactions` VALUES (13, 1, 1, 1, 1111, 106932, '2025-06-17 00:50:44', 'completed');
INSERT INTO `shop_transactions` VALUES (14, 1, 1, 1, 1111, 106932, '2025-06-17 00:52:31', 'completed');
INSERT INTO `shop_transactions` VALUES (15, 1, 1, 1, 1111, 106932, '2025-06-17 01:06:24', 'completed');
INSERT INTO `shop_transactions` VALUES (16, 1, 1, 1, 1111, 106932, '2025-06-17 01:11:59', 'completed');
INSERT INTO `shop_transactions` VALUES (17, 1, 1, 1, 1111, 106932, '2025-06-17 01:17:51', 'completed');
INSERT INTO `shop_transactions` VALUES (18, 1, 1, 1, 1111, 106932, '2025-06-17 01:18:31', 'completed');
INSERT INTO `shop_transactions` VALUES (19, 1, 1, 1, 1111, 106932, '2025-06-17 01:25:25', 'completed');
INSERT INTO `shop_transactions` VALUES (20, 1, 1, 1, 1111, 106932, '2025-06-17 01:32:34', 'completed');
INSERT INTO `shop_transactions` VALUES (21, 1, 1, 1, 1111, 106932, '2025-06-17 01:37:20', 'completed');
INSERT INTO `shop_transactions` VALUES (22, 1, 1, 1, 1111, 106932, '2025-06-17 01:38:58', 'completed');
INSERT INTO `shop_transactions` VALUES (23, 1, 1, 1, 1111, 106932, '2025-06-17 01:41:42', 'completed');
INSERT INTO `shop_transactions` VALUES (24, 1, 1, 1, 1111, 106932, '2025-06-17 01:46:35', 'completed');

-- ----------------------------
-- Table structure for siege_locations
-- ----------------------------
DROP TABLE IF EXISTS `siege_locations`;
CREATE TABLE `siege_locations`  (
  `id` int(11) NOT NULL,
  `race` enum('ELYOS','ASMODIANS','BALAUR') CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
  `legion_id` int(11) NOT NULL,
  `occupy_count` tinyint(4) NOT NULL DEFAULT 0,
  `faction_balance` tinyint(4) NOT NULL DEFAULT 0,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of siege_locations
-- ----------------------------
INSERT INTO `siege_locations` VALUES (1011, 'BALAUR', 0, 0, 0);
INSERT INTO `siege_locations` VALUES (1012, 'BALAUR', 0, 0, 0);
INSERT INTO `siege_locations` VALUES (1013, 'BALAUR', 0, 0, 0);
INSERT INTO `siege_locations` VALUES (1014, 'BALAUR', 0, 0, 0);
INSERT INTO `siege_locations` VALUES (1015, 'BALAUR', 0, 0, 0);
INSERT INTO `siege_locations` VALUES (1016, 'BALAUR', 0, 0, 0);
INSERT INTO `siege_locations` VALUES (1017, 'BALAUR', 0, 0, 0);
INSERT INTO `siege_locations` VALUES (1018, 'BALAUR', 0, 0, 0);
INSERT INTO `siege_locations` VALUES (1019, 'BALAUR', 0, 0, 0);
INSERT INTO `siege_locations` VALUES (1020, 'BALAUR', 0, 0, 0);
INSERT INTO `siege_locations` VALUES (1131, 'BALAUR', 0, 0, 0);
INSERT INTO `siege_locations` VALUES (1132, 'BALAUR', 0, 0, 0);
INSERT INTO `siege_locations` VALUES (1133, 'BALAUR', 0, 0, 0);
INSERT INTO `siege_locations` VALUES (1134, 'BALAUR', 0, 0, 0);
INSERT INTO `siege_locations` VALUES (1135, 'BALAUR', 0, 0, 0);
INSERT INTO `siege_locations` VALUES (1141, 'BALAUR', 0, 0, 0);
INSERT INTO `siege_locations` VALUES (1142, 'BALAUR', 0, 0, 0);
INSERT INTO `siege_locations` VALUES (1143, 'BALAUR', 0, 0, 0);
INSERT INTO `siege_locations` VALUES (1144, 'BALAUR', 0, 0, 0);
INSERT INTO `siege_locations` VALUES (1145, 'BALAUR', 0, 0, 0);
INSERT INTO `siege_locations` VALUES (1146, 'BALAUR', 0, 0, 0);
INSERT INTO `siege_locations` VALUES (1211, 'BALAUR', 0, 0, 0);
INSERT INTO `siege_locations` VALUES (1212, 'BALAUR', 0, 0, 0);
INSERT INTO `siege_locations` VALUES (1213, 'BALAUR', 0, 0, 0);
INSERT INTO `siege_locations` VALUES (1214, 'BALAUR', 0, 0, 0);
INSERT INTO `siege_locations` VALUES (1215, 'BALAUR', 0, 0, 0);
INSERT INTO `siege_locations` VALUES (1221, 'BALAUR', 0, 0, 0);
INSERT INTO `siege_locations` VALUES (1222, 'BALAUR', 0, 0, 0);
INSERT INTO `siege_locations` VALUES (1223, 'BALAUR', 0, 0, 0);
INSERT INTO `siege_locations` VALUES (1224, 'BALAUR', 0, 0, 0);
INSERT INTO `siege_locations` VALUES (1231, 'BALAUR', 0, 0, 0);
INSERT INTO `siege_locations` VALUES (1232, 'BALAUR', 0, 0, 0);
INSERT INTO `siege_locations` VALUES (1233, 'BALAUR', 0, 0, 0);
INSERT INTO `siege_locations` VALUES (1241, 'BALAUR', 0, 0, 0);
INSERT INTO `siege_locations` VALUES (1242, 'BALAUR', 0, 0, 0);
INSERT INTO `siege_locations` VALUES (1243, 'BALAUR', 0, 0, 0);
INSERT INTO `siege_locations` VALUES (1251, 'BALAUR', 0, 0, 0);
INSERT INTO `siege_locations` VALUES (1252, 'BALAUR', 0, 0, 0);
INSERT INTO `siege_locations` VALUES (1253, 'BALAUR', 0, 0, 0);
INSERT INTO `siege_locations` VALUES (1254, 'BALAUR', 0, 0, 0);
INSERT INTO `siege_locations` VALUES (1401, 'BALAUR', 0, 0, 0);
INSERT INTO `siege_locations` VALUES (1402, 'BALAUR', 0, 0, 0);
INSERT INTO `siege_locations` VALUES (1403, 'BALAUR', 0, 0, 0);
INSERT INTO `siege_locations` VALUES (2011, 'BALAUR', 0, 0, 0);
INSERT INTO `siege_locations` VALUES (2012, 'BALAUR', 0, 0, 0);
INSERT INTO `siege_locations` VALUES (2013, 'BALAUR', 0, 0, 0);
INSERT INTO `siege_locations` VALUES (2014, 'BALAUR', 0, 0, 0);
INSERT INTO `siege_locations` VALUES (2021, 'BALAUR', 0, 0, 0);
INSERT INTO `siege_locations` VALUES (2022, 'BALAUR', 0, 0, 0);
INSERT INTO `siege_locations` VALUES (2023, 'BALAUR', 0, 0, 0);
INSERT INTO `siege_locations` VALUES (2024, 'BALAUR', 0, 0, 0);
INSERT INTO `siege_locations` VALUES (2111, 'ELYOS', 0, 0, 0);
INSERT INTO `siege_locations` VALUES (3011, 'BALAUR', 0, 0, 0);
INSERT INTO `siege_locations` VALUES (3012, 'BALAUR', 0, 0, 0);
INSERT INTO `siege_locations` VALUES (3013, 'BALAUR', 0, 0, 0);
INSERT INTO `siege_locations` VALUES (3014, 'BALAUR', 0, 0, 0);
INSERT INTO `siege_locations` VALUES (3021, 'BALAUR', 0, 0, 0);
INSERT INTO `siege_locations` VALUES (3022, 'BALAUR', 0, 0, 0);
INSERT INTO `siege_locations` VALUES (3023, 'BALAUR', 0, 0, 0);
INSERT INTO `siege_locations` VALUES (3024, 'BALAUR', 0, 0, 0);
INSERT INTO `siege_locations` VALUES (3111, 'ASMODIANS', 0, 0, 0);
INSERT INTO `siege_locations` VALUES (7011, 'BALAUR', 0, 0, 0);
INSERT INTO `siege_locations` VALUES (7012, 'BALAUR', 0, 0, 0);
INSERT INTO `siege_locations` VALUES (7013, 'BALAUR', 0, 0, 0);
INSERT INTO `siege_locations` VALUES (7014, 'BALAUR', 0, 0, 0);
INSERT INTO `siege_locations` VALUES (8011, 'BALAUR', 0, 0, 0);
INSERT INTO `siege_locations` VALUES (10111, 'BALAUR', 0, 0, 0);
INSERT INTO `siege_locations` VALUES (10211, 'BALAUR', 0, 0, 0);
INSERT INTO `siege_locations` VALUES (10311, 'BALAUR', 0, 0, 0);
INSERT INTO `siege_locations` VALUES (10411, 'BALAUR', 0, 0, 0);

-- ----------------------------
-- Table structure for surveys
-- ----------------------------
DROP TABLE IF EXISTS `surveys`;
CREATE TABLE `surveys`  (
  `unique_id` int(11) NOT NULL AUTO_INCREMENT,
  `owner_id` int(11) NOT NULL,
  `item_id` int(11) NOT NULL,
  `item_count` decimal(20, 0) NOT NULL DEFAULT 1,
  `html_text` text CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
  `html_radio` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT 'accept',
  `used` tinyint(1) NOT NULL DEFAULT 0,
  `used_time` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '',
  PRIMARY KEY (`unique_id`) USING BTREE,
  INDEX `owner_id`(`owner_id`) USING BTREE,
  CONSTRAINT `surveys_ibfk_1` FOREIGN KEY (`owner_id`) REFERENCES `players` (`id`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of surveys
-- ----------------------------

-- ----------------------------
-- Table structure for towns
-- ----------------------------
DROP TABLE IF EXISTS `towns`;
CREATE TABLE `towns`  (
  `id` int(11) NOT NULL,
  `level` int(11) NOT NULL DEFAULT 1,
  `points` int(11) NOT NULL DEFAULT 0,
  `race` enum('ELYOS','ASMODIANS') CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
  `level_up_date` timestamp NOT NULL DEFAULT '1970-01-01 07:00:01',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of towns
-- ----------------------------
INSERT INTO `towns` VALUES (1001, 1, 0, 'ELYOS', '1970-01-01 07:00:01');
INSERT INTO `towns` VALUES (1002, 1, 0, 'ELYOS', '1970-01-01 07:00:01');
INSERT INTO `towns` VALUES (1003, 1, 0, 'ELYOS', '1970-01-01 07:00:01');
INSERT INTO `towns` VALUES (1004, 1, 0, 'ELYOS', '1970-01-01 07:00:01');
INSERT INTO `towns` VALUES (1005, 1, 0, 'ELYOS', '1970-01-01 07:00:01');
INSERT INTO `towns` VALUES (1006, 1, 0, 'ELYOS', '1970-01-01 07:00:01');
INSERT INTO `towns` VALUES (1007, 1, 0, 'ELYOS', '1970-01-01 07:00:01');
INSERT INTO `towns` VALUES (1008, 1, 0, 'ELYOS', '1970-01-01 07:00:01');
INSERT INTO `towns` VALUES (1009, 1, 0, 'ELYOS', '1970-01-01 07:00:01');
INSERT INTO `towns` VALUES (1010, 1, 0, 'ELYOS', '1970-01-01 07:00:01');
INSERT INTO `towns` VALUES (1011, 1, 0, 'ELYOS', '1970-01-01 07:00:01');
INSERT INTO `towns` VALUES (1012, 1, 0, 'ELYOS', '1970-01-01 07:00:01');
INSERT INTO `towns` VALUES (1013, 1, 0, 'ELYOS', '1970-01-01 07:00:01');
INSERT INTO `towns` VALUES (1014, 1, 0, 'ELYOS', '1970-01-01 07:00:01');
INSERT INTO `towns` VALUES (1015, 1, 0, 'ELYOS', '1970-01-01 07:00:01');
INSERT INTO `towns` VALUES (1016, 1, 0, 'ELYOS', '1970-01-01 07:00:01');
INSERT INTO `towns` VALUES (1017, 1, 0, 'ELYOS', '1970-01-01 07:00:01');
INSERT INTO `towns` VALUES (1018, 1, 0, 'ELYOS', '1970-01-01 07:00:01');
INSERT INTO `towns` VALUES (1019, 1, 0, 'ELYOS', '1970-01-01 07:00:01');
INSERT INTO `towns` VALUES (1020, 1, 0, 'ELYOS', '1970-01-01 07:00:01');
INSERT INTO `towns` VALUES (1021, 1, 0, 'ELYOS', '1970-01-01 07:00:01');
INSERT INTO `towns` VALUES (1022, 1, 0, 'ELYOS', '1970-01-01 07:00:01');
INSERT INTO `towns` VALUES (1023, 1, 0, 'ELYOS', '1970-01-01 07:00:01');
INSERT INTO `towns` VALUES (1024, 1, 0, 'ELYOS', '1970-01-01 07:00:01');
INSERT INTO `towns` VALUES (1025, 1, 0, 'ELYOS', '1970-01-01 07:00:01');
INSERT INTO `towns` VALUES (2001, 1, 0, 'ASMODIANS', '1970-01-01 07:00:01');
INSERT INTO `towns` VALUES (2002, 1, 0, 'ASMODIANS', '1970-01-01 07:00:01');
INSERT INTO `towns` VALUES (2003, 1, 0, 'ASMODIANS', '1970-01-01 07:00:01');
INSERT INTO `towns` VALUES (2004, 1, 0, 'ASMODIANS', '1970-01-01 07:00:01');
INSERT INTO `towns` VALUES (2005, 1, 0, 'ASMODIANS', '1970-01-01 07:00:01');
INSERT INTO `towns` VALUES (2006, 1, 0, 'ASMODIANS', '1970-01-01 07:00:01');
INSERT INTO `towns` VALUES (2007, 1, 0, 'ASMODIANS', '1970-01-01 07:00:01');
INSERT INTO `towns` VALUES (2008, 1, 0, 'ASMODIANS', '1970-01-01 07:00:01');
INSERT INTO `towns` VALUES (2009, 1, 0, 'ASMODIANS', '1970-01-01 07:00:01');
INSERT INTO `towns` VALUES (2010, 1, 0, 'ASMODIANS', '1970-01-01 07:00:01');
INSERT INTO `towns` VALUES (2011, 1, 0, 'ASMODIANS', '1970-01-01 07:00:01');
INSERT INTO `towns` VALUES (2012, 1, 0, 'ASMODIANS', '1970-01-01 07:00:01');
INSERT INTO `towns` VALUES (2013, 1, 0, 'ASMODIANS', '1970-01-01 07:00:01');
INSERT INTO `towns` VALUES (2014, 1, 0, 'ASMODIANS', '1970-01-01 07:00:01');
INSERT INTO `towns` VALUES (2015, 1, 0, 'ASMODIANS', '1970-01-01 07:00:01');
INSERT INTO `towns` VALUES (2016, 1, 0, 'ASMODIANS', '1970-01-01 07:00:01');
INSERT INTO `towns` VALUES (2017, 1, 0, 'ASMODIANS', '1970-01-01 07:00:01');
INSERT INTO `towns` VALUES (2018, 1, 0, 'ASMODIANS', '1970-01-01 07:00:01');
INSERT INTO `towns` VALUES (2019, 1, 0, 'ASMODIANS', '1970-01-01 07:00:01');
INSERT INTO `towns` VALUES (2020, 1, 0, 'ASMODIANS', '1970-01-01 07:00:01');
INSERT INTO `towns` VALUES (2021, 1, 0, 'ASMODIANS', '1970-01-01 07:00:01');
INSERT INTO `towns` VALUES (2022, 1, 0, 'ASMODIANS', '1970-01-01 07:00:01');
INSERT INTO `towns` VALUES (2023, 1, 0, 'ASMODIANS', '1970-01-01 07:00:01');
INSERT INTO `towns` VALUES (2024, 1, 0, 'ASMODIANS', '1970-01-01 07:00:01');
INSERT INTO `towns` VALUES (2025, 1, 0, 'ASMODIANS', '1970-01-01 07:00:01');

-- ----------------------------
-- Table structure for votes
-- ----------------------------
DROP TABLE IF EXISTS `votes`;
CREATE TABLE `votes`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) NOT NULL,
  `site_name` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
  `reward` int(11) NOT NULL,
  `created_at` datetime NOT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `user_id`(`user_id`) USING BTREE,
  CONSTRAINT `votes_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `account_data` (`id`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of votes
-- ----------------------------

-- ----------------------------
-- Triggers structure for table player_web_rewards
-- ----------------------------
DROP TRIGGER IF EXISTS `trg_process_web_reward`;
delimiter ;;
CREATE TRIGGER `trg_process_web_reward` AFTER INSERT ON `player_web_rewards` FOR EACH ROW BEGIN
    -- Declare variables to hold the new unique IDs
    DECLARE new_item_unique_id INT;
    DECLARE new_mail_id INT;
    DECLARE mail_title VARCHAR(255);
    DECLARE mail_body VARCHAR(1000);

    -- Generate a unique ID for the new item in the inventory
    SELECT MAX(item_unique_id) + 1 INTO new_item_unique_id FROM inventory;
    IF new_item_unique_id IS NULL THEN
        SET new_item_unique_id = 1;
    END IF;

    -- Generate a unique ID for the new mail
    SELECT MAX(mail_unique_id) + 1 INTO new_mail_id FROM mail;
    IF new_mail_id IS NULL THEN
        SET new_mail_id = 1;
    END IF;
    
    -- Create the item in the inventory table, setting its location to 127 (Mail Attachment)
    -- This is the correct logic from the Beyond Aion source code.
    INSERT INTO `inventory` (item_unique_id, item_id, item_count, item_owner, item_location)
    VALUES (new_item_unique_id, NEW.item_id, NEW.item_count, NEW.player_id, 127);
    
    -- Set the mail title and body
    SET mail_title = 'Web Shop Purchase';
    SET mail_body = 'Thank you for your purchase!';

    -- Create the mail record and attach the item's unique ID
    INSERT INTO `mail` (mail_unique_id, mail_recipient_id, sender_name, mail_title, mail_message, attached_item_id, attached_kinah_count, unread, express)
    VALUES (new_mail_id, NEW.player_id, 'Web Shop', mail_title, mail_body, new_item_unique_id, 0, 1, 1);

    -- Mark the reward as processed so it doesn't get sent again
    UPDATE `player_web_rewards` SET `received` = NOW() WHERE `entry_id` = NEW.entry_id;

END
;;
delimiter ;

SET FOREIGN_KEY_CHECKS = 1;
